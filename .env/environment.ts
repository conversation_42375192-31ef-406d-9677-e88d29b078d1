const baseUrl = window['env']['baseUrl'] || 'https://api.myapp.local';

export const environment = {
  production: false,
  accessKey: window['env']['accessKey'] || '',
  baseUrl: baseUrl,
  apiVersion: window['env']['apiVersion'] || 'v1',
  appVersion: window['env']['appVersion'] || '',
  workflowExecutionMode: window['env']['workflowExecutionMode'] || 'all',
  useBasicLogin: window['env']['useBasicLogin'] || 'false',

  // Console-specific environment variables
  consoleApi: window['env']['consoleApi']
    ? baseUrl + window['env']['consoleApi']
    : baseUrl,
  consoleApiV2: window['env']['consoleApiV2']
    ? baseUrl + window['env']['consoleApiV2']
    : baseUrl,
  consoleApiAuthUrl: window['env']['consoleApiAuthUrl']
    ? baseUrl + window['env']['consoleApiAuthUrl']
    : baseUrl,
  consoleEmbeddingApi: window['env']['consoleEmbeddingApi']
    ? baseUrl + window['env']['consoleEmbeddingApi']
    : baseUrl,
  consoleInstructionApi: window['env']['consoleInstructionApi']
    ? baseUrl + window['env']['consoleInstructionApi']
    : baseUrl,
  consoleLangfuseUrl: window['env']['consoleLangfuseUrl'] || '',
  consoleTruelensUrl: window['env']['consoleTruelensUrl'] || '',
  consolePipelineApi: window['env']['consolePipelineApi']
    ? baseUrl + window['env']['consolePipelineApi']
    : baseUrl,
  consoleRedirectUrl: window['env']['consoleRedirectUrl']
    ? baseUrl + window['env']['consoleRedirectUrl']
    : baseUrl,
  consoleUrl: window['env']['consoleUrl']
    ? baseUrl + window['env']['consoleUrl']
    : baseUrl,
  consoleRedirectUri: window['env']['consoleRedirectUri']
    ? baseUrl + window['env']['consoleRedirectUri']
    : baseUrl,
  enableLogStreaming: window['env']['enableLogStreaming'] || '',
  logStreamingApiUrl: window['env']['logStreamingApiUrl'] || '',

  // Experience-specific environment variables
  experienceStudioUrl: window['env']['experienceStudioUrl']
    ? baseUrl + window['env']['experienceStudioUrl']
    : baseUrl,
  experienceApiUrl: window['env']['experienceApiUrl']
    ? baseUrl + window['env']['experienceApiUrl']
    : baseUrl,
  experienceBaseUrl: window["env"]["experienceBaseUrl"] || "",

  // Product-specific environment variables
  productStudioUrl: window['env']['productStudioUrl']
    ? baseUrl + window['env']['productStudioUrl']
    : baseUrl,
  productApiUrl: window['env']['productApiUrl']
    ? baseUrl + window['env']['productApiUrl']
    : baseUrl,
  pipelineApiBaseUrl: window['env']['pipelineApiBaseUrl']
    ? baseUrl + window['env']['pipelineApiBaseUrl']
    : baseUrl,

  // Elder Wand-specific environment variables
  elderWandUrl: window['env']['elderWandUrl']
    ? baseUrl + window['env']['elderWandUrl']
    : baseUrl,
};
