// Environment configuration injected from DevOps
// This file is processed by envsubst to replace environment variables
// All values must be provided by the deployment environment
(function (window) {
  window.env = window.env || {};

  // Core configuration - must be provided by DevOps
  window.env.production = "${NODE_ENV}" === "production" ? "true" : "false";
  window.env.baseUrl = "${BASE_URL}";

  // Application URLs - all from DevOps environment
  window.env.elderWandUrl = "${ELDER_WAND_URL}";
  window.env.experienceStudioUrl = "${EXPERIENCE_STUDIO_URL}";
  window.env.productStudioUrl = "${PRODUCT_STUDIO_URL}";
  window.env.consoleRedirectUrl = "${CONSOLE_REDIRECT_URL}";
  window.env.consoleUrl = "${CONSOLE_URL}";
  window.env.consoleRedirectUri = "${CONSOLE_REDIRECT_URI}";

  // API Configuration - all from DevOps environment
  window.env.apiVersion = "${API_VERSION}";
  window.env.consoleApi = "${CONSOLE_API}";
  window.env.consoleApiV2 = "${CONSOLE_API_V2}";
  window.env.consoleApiAuthUrl = "${CONSOLE_API_AUTH_URL}";
  window.env.consoleEmbeddingApi = "${CONSOLE_EMBEDDING_API}";
  window.env.consoleInstructionApi = "${CONSOLE_INSTRUCTION_API}";
  window.env.consoleLangfuseUrl = "${CONSOLE_LANGFUSE_URL}";
  window.env.consoleTruelensUrl = "${CONSOLE_TRUELENS_URL}";
  window.env.consolePipelineApi = "${CONSOLE_PIPELINE_API}";
  window.env.experienceApiUrl = "${EXPERIENCE_API_URL}";
  window.env.productApiUrl = "${PRODUCT_API_URL}";

  // Experience Studio specific variables
  window.env.experienceBaseUrl = "${EXPERIENCE_BASE_URL}";
  window.env.experianceApiAuthUrl = "${EXPERIENCE_API_AUTH_URL}";
  window.env.experianceRedirectUrl = "${EXPERIENCE_REDIRECT_URL}";

  // Product Studio specific variables
  window.env.pipelineApiBaseUrl = "${PIPELINE_API_BASE_URL}";

  // Logging and App Specific - all from DevOps environment
  window.env.enableLogStreaming = "${ENABLE_LOG_STREAMING}";
  window.env.logStreamingApiUrl = "${LOG_STREAMING_API_URL}";
  window.env.appVersion = "${APP_VERSION}";
  window.env.workflowExecutionMode = "${WORKFLOW_EXECUTION_MODE}";
  window.env.useBasicLogin = "${USE_BASIC_LOGIN}";

  // Access key - from DevOps environment
  window.env.accessKey = "${ACCESS_KEY}";

  // Auto-construct full URLs from baseUrl for any relative paths
  const baseUrl = window.env.baseUrl;
  Object.keys(window.env).forEach((key) => {
    if (
      key !== "baseUrl" &&
      key !== "production" &&
      typeof window.env[key] === "string" &&
      window.env[key].startsWith("/") &&
      !window.env[key].startsWith("${")
    ) {
      // Only process if not a placeholder
      window.env[key] = baseUrl + window.env[key];
    }
  });

  // Validate that required environment variables are set
  const requiredVars = [
    "baseUrl",
    "consoleApi",
    "consoleApiAuthUrl",
    "experienceBaseUrl",
    "pipelineApiBaseUrl",
  ];
  const missingVars = requiredVars.filter(
    (key) => !window.env[key] || window.env[key].startsWith("${"),
  );

  if (missingVars.length > 0) {
    console.error("❌ Missing required environment variables:", missingVars);
    console.error(
      "Please ensure DevOps pipeline provides all required environment variables",
    );
  } else {
    console.log("✅ Environment configuration loaded from DevOps:", window.env);
  }
})(this);
