import {
  provideHttpClient,
  withInterceptors,
  HTTP_INTERCEPTORS,
  withInterceptorsFromDi,
} from '@angular/common/http';
import {
  ApplicationConfig,
  importProvidersFrom,
  provideZoneChangeDetection,
  SecurityContext,
} from '@angular/core';
import { provideRouter, withPreloading } from '@angular/router';
import { provideMarkdown, MARKED_OPTIONS, MarkdownModule } from 'ngx-markdown';
import { provideAnimations } from '@angular/platform-browser/animations';
import { MonacoEditorModule, NgxMonacoEditorConfig } from 'ngx-monaco-editor-v2';
import { LOGGER_CONFIG, LoggerService } from './shared/utils/logger';
import { environment } from '../environments/environment';
import { CacheInterceptor } from './shared/interceptors/cache.interceptor';
import { HttpErrorInterceptor } from './shared/interceptors/http-error.interceptor';
import { LongRequestInterceptor } from './shared/interceptors/long-request.interceptor';
import { ProjectLoadingMockInterceptor } from './shared/interceptors/project-loading-mock.interceptor';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import { SelectivePreloadingStrategy } from './shared/strategies/selective-preloading-strategy';

import { routes } from './app.routes';

// Monaco Editor Configuration
const monacoConfig: NgxMonacoEditorConfig = {
  baseUrl: 'assets/monaco',
  defaultOptions: {
    scrollBeyondLastLine: false,
    automaticLayout: true
  },
  onMonacoLoad: () => {
    // Define custom themes when Monaco loads
    const monaco = (window as any).monaco;
    if (monaco) {
      // Define custom light theme
      monaco.editor.defineTheme('code-editor-light', {
        base: 'vs',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },
          { token: 'keyword', foreground: '0066cc' },
          { token: 'string', foreground: '28a745' },
          { token: 'number', foreground: 'e83e8c' },
          { token: 'function', foreground: 'fd7e14' }
        ],
        colors: {
          'editor.background': '#ffffff',
          'editor.foreground': '#2c3e50',
          'editorLineNumber.foreground': '#6c757d',
          'editor.selectionBackground': '#007bff26',
          'editor.lineHighlightBackground': '#f8f9fa',
          'editorCursor.foreground': '#007bff'
        }
      });

      // Define custom dark theme
      monaco.editor.defineTheme('code-editor-dark', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6c757d', fontStyle: 'italic' },
          { token: 'keyword', foreground: '66d9ef' },
          { token: 'string', foreground: 'a6e22e' },
          { token: 'number', foreground: 'fd971f' },
          { token: 'function', foreground: 'f92672' }
        ],
        colors: {
          'editor.background': '#1e1e1e',
          'editor.foreground': '#e9ecef',
          'editorLineNumber.foreground': '#6c757d',
          'editor.selectionBackground': '#66d9ef26',
          'editor.lineHighlightBackground': '#2c2c2c',
          'editorCursor.foreground': '#66d9ef'
        }
      });
    }
  }
};
import {
  LucideAngularModule,
  Hammer,
  User,
  Settings,
  Info,
  ChevronLeft,
  ChevronRight,
  ShieldAlert,
  Hourglass,
  CircleCheck,
  XCircle,
  AlignVerticalDistributeStart,
  CircleCheckBig,
  MoveLeft,
  Play,
  CalendarDays,
  EllipsisVertical,
  SquarePen,
  Wifi,
  Search,
  AlertCircle,
  EyeOff,
  Mail,
  Phone,
  Check,
  X,
  Edit,
  Trash,
  Plus,
  Minus,
  ChevronDown,
  ChevronUp,
  Eye,
  Home,
  Layout,
  Bell,
  Grid,
  Star,
  Leaf,
  CheckCircle,
  AlertTriangle,
  XOctagon,
  Sparkles,
  Slash,
  Feather,
  Globe,
  Send,
  Box,
  Paperclip,
  Bot,
  Archive,
  Copy,
  Trash2,
  Users,
  Wrench,
  TrendingUp,
  PanelLeft,
  BookOpen,
  NotebookText,
  Redo,
  RotateCcw,
  Swords,
  Undo,
  Pencil,
  RotateCw,
  SendHorizontal,
  WandSparkles,
  MousePointer2,
  Hand,
  ZoomIn,
  ZoomOut,
  Clock,
  CircleX,
  FileText,
  Download,
  Save,
  LayoutGrid,
} from 'lucide-angular';

export const appConfig: ApplicationConfig = {
  providers: [
    // Configure HTTP client with interceptors
    // Order matters: ProjectLoadingMockInterceptor first for testing,
    // LongRequestInterceptor to configure headers,
    // then HttpErrorInterceptor to catch errors, then CacheInterceptor
    provideHttpClient(
      withInterceptors([
        ProjectLoadingMockInterceptor,
        LongRequestInterceptor,
        HttpErrorInterceptor,
        CacheInterceptor,
      ]),
      withInterceptorsFromDi()
    ),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withPreloading(SelectivePreloadingStrategy)),
    importProvidersFrom(
      LucideAngularModule.pick({
        Hammer,
        User,
        Settings,
        Info,
        ChevronLeft,
        ChevronRight,
        ShieldAlert,
        Hourglass,
        CircleCheck,
        XCircle,
        AlignVerticalDistributeStart,
        CircleCheckBig,
        MoveLeft,
        Play,
        CalendarDays,
        EllipsisVertical,
        SquarePen,
        Wifi,
        Search,
        AlertCircle,
        EyeOff,
        Mail,
        Phone,
        Check,
        X,
        Edit,
        Trash,
        Plus,
        Minus,
        ChevronDown,
        ChevronUp,
        Eye,
        Home,
        Layout,
        Bell,
        Grid,
        Star,
        Leaf,
        CheckCircle,
        AlertTriangle,
        XOctagon,
        Sparkles,
        Slash,
        Feather,
        Globe,
        Send,
        Box,
        Paperclip,
        Bot,
        Archive,
        Copy,
        Trash2,
        Users,
        Wrench,
        TrendingUp,
        PanelLeft,
        BookOpen,
        NotebookText,
        Redo,
        RotateCcw,
        Swords,
        Undo,
        Pencil,
        RotateCw,
        SendHorizontal,
        WandSparkles,
        MousePointer2,
        Hand,
        ZoomIn,
        ZoomOut,
        Clock,
        CircleX,
        FileText,
        Download,
        Save,
        LayoutGrid,
      }),
      MarkdownModule.forRoot(),
      MonacoEditorModule.forRoot(monacoConfig)
    ),
    SelectivePreloadingStrategy,
    provideMarkdown({
      sanitize: SecurityContext.HTML,
      markedOptions: {
        provide: MARKED_OPTIONS,
        useValue: {
          gfm: true,
          breaks: true,
          pedantic: false,
        },
      },
    }),
    provideAnimations(),
    {
      provide: LOGGER_CONFIG,
      useValue: {
        level: environment.production ? 'warn' : 'debug',
        enableConsoleOutput: !environment.production,
      },
    },
    LoggerService,
  ],
};
