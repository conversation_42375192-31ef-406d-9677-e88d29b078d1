import {
  HeaderConfig,
  SharedNavItem,
  CenterContentConfig,
  RouteContentMapping
} from '@shared/components/app-header/app-header.component';
import { environment } from '../../environments/environment';

// Experience Studio specific navigation items
const experienceStudioNavItems: SharedNavItem[] = [
  {
    label: 'Dashboard',
    route: '/',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/awe_dashboard.svg`,
  },
  // {
  //   label: 'Image to Code',
  //   route: '/experience/generate-application',
  //   selected: false,
  //   hasDropdown: false,
  //   icon: `assets/icons/awe_i2c.svg`,
  //   disabled: true, // Temporarily disabled
  // },
  // {
  //   label: 'Prompt to Code',
  //   route: '/experience/generate-application',
  //   selected: false,
  //   hasDropdown: false,
  //   icon: `assets/icons/awe_p2c.svg`,
  //   disabled: true, // Temporarily disabled
  // },
  // {
  //   label: 'Generate Wireframes',
  //   route: 'experience/generate-ui-design',
  //   selected: false,
  //   hasDropdown: false,
  //   icon: `assets/icons/awe_wireframe.svg`,
  //   disabled: true, // Temporarily disabled
  // }
];

// Available studio apps for the app drawer
const availableStudioApps = [
  {
    name: 'Product Studio',
    route: environment.productStudioUrl,
    icon: 'assets/studio-logo/product-studio.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Experience Studio',
    route: environment.experianceRedirectUrl,
    icon: 'assets/studio-logo/experience-studio.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Launchpad',
    route: environment.elderWandUrl,
    icon: 'assets/studio-logo/launch-pad.svg',
    description: 'Central application launcher and hub',
  },
  {
    name: 'Console',
    route: environment.consoleUrl,
    icon: 'assets/studio-logo/console.svg',
    description: 'Agent management and workflow automation',
  },
];

// Route-based content mappings for dynamic header text
const experienceStudioRouteContentMappings: RouteContentMapping[] = [
  {
    route: '/experience/generate-ui-design',
    displayText: 'Generate Wireframes',
    icon: 'assets/icons/awe_wireframe.svg',
    description: 'Create wireframes from your ideas'
  },
  {
    route: '/experience/generate-application',
    displayText: 'Generate Application',
    icon: 'assets/icons/awe_i2c.svg',
    description: 'Build applications from images or prompts'
  },
  {
    route: '/experience/image-to-code',
    displayText: 'Image to Application',
    icon: 'assets/icons/awe_i2c.svg',
    description: 'Convert images to working applications'
  },
  {
    route: '/prompt-to-code',
    displayText: 'Prompt to Application',
    icon: 'assets/icons/awe_p2c.svg',
    description: 'Generate applications from text prompts'
  },
  {
    route: '/design-analysis',
    displayText: 'Design Accessibility',
    icon: 'assets/icons/awe_accessibility.svg',
    description: 'Analyze and improve design accessibility'
  }
];

// Center content configuration for Experience Studio
const experienceStudioCenterContent: CenterContentConfig = {
  type: 'dynamic-text',
  defaultText: 'Experience Studio',
  textClass: 'experience-studio-header-text',
  containerClass: 'experience-studio-header-container'
};

// Available languages for the language switcher
const availableLanguages = [
  { code: 'en', name: 'English' },
  // { code: 'es', name: 'Español' },
  // { code: 'fr', name: 'Français' },
];

// Experience Studio header configuration
export const experienceStudioHeaderConfig: HeaderConfig = {
  // Main logo configuration - will switch based on theme
  logoSrc: 'assets/svgs/ascendion-logo-light.svg', // Light theme logo
  navItems: experienceStudioNavItems,
  showOrgSelector: true,//todo-> toggle to true
  showThemeToggle: false,//todo -> toggle to true
  showAppDrawer: true,//todo -> toggle to true
  showProfileDropdown: true,
  showThemeToggleInProfile: true, //todo -> toggle to true
  showLanguageSwitcher: false,
  disableThemeToggle: false,
  disableLanguageChange: false,
  availableLanguages: [
    { code: 'en', name: 'English' },
    // { code: 'fil', name: 'Filipino' },
    // { code: 'es', name: 'Español' },
  ],
  currentApp: 'Experience Studio',
  availableApps: availableStudioApps,
  projectName: 'Experience Studio',
  redirectUrl: '/',
  // Logo configuration for studio branding
  showOnlyAscendionLogo: false,// Use the new dedicated option for single Ascendion logo
  enableLogoAnimation: true,// Disable animation by default
  logoAnimationInterval: 3000,// Can be enabled if needed
  logoAnimationStyle: 'fade',// Animation style if enabled
  studioNames: ['Experience Studio'],
  // Theme-aware logo configuration
  // studioLogos = []
  // studioLogosDark = []
  //studioNames = []
  studioLogos: ['assets/svgs/ascendion-logo-light.svg'], // Light theme logo
  studioLogosDark: ['assets/svgs/ascendion-logo-dark.svg'], // Dark theme logo
  // studioLogos: ['ascendionAAVA-logo-light.svg', 'AAVA_logo.svg', 'ES_LOGO.svg'],
  // studioNames: ['Experience Studio', 'Design Innovation', 'Code Generation'],
  enableThemeAwareLogos: true,
  // NEW: Center content configuration for dynamic text display
  centerContent: experienceStudioCenterContent,
  // NEW: Route-based content mappings
  routeContentMappings: experienceStudioRouteContentMappings,
};
