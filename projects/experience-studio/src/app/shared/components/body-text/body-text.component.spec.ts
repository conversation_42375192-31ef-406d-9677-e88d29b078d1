import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { BodyTextComponent } from './body-text.component';
import { Component, DebugElement } from '@angular/core';

@Component({
  standalone: true,
  imports: [BodyTextComponent],
  template: `<awe-body-text [type]="type">{{ content }}</awe-body-text>`
})
class TestHostComponent {
  type = 'body-test';
  content = 'Test content';
}

describe('BodyTextComponent', () => {
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let bodyTextEl: DebugElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestHostComponent]
    }).compileComponents();

    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    hostFixture.detectChanges();

    bodyTextEl = hostFixture.debugElement.query(By.css('.body-test'));
  });

  it('should create', () => {
    expect(hostComponent).toBeTruthy();
  });

  it('should display the content correctly', () => {
    hostComponent.content = 'Hello World';
    hostFixture.detectChanges();
    
    expect(bodyTextEl.nativeElement.textContent.trim()).toBe('Hello World');
  });

  it('should apply default type class', () => {
    expect(bodyTextEl.nativeElement.classList.contains('body-test')).toBeTruthy();
  });

  it('should apply custom type class when provided', () => {
    hostComponent.type = 'custom-type';
    hostFixture.detectChanges();

    const updatedEl = hostFixture.debugElement.query(By.css('p'));
    expect(updatedEl.nativeElement.classList.contains('custom-type')).toBeTruthy();
    expect(updatedEl.nativeElement.classList.contains('body-test')).toBeTruthy();
  });

  it('should have title attribute set to body-test', () => {
    expect(bodyTextEl.nativeElement.getAttribute('title')).toBe('body-test');
  });

  it('should have aria-live attribute set to polite', () => {
    expect(bodyTextEl.nativeElement.getAttribute('aria-live')).toBe('polite');
  });

  it('should have tabindex set to 0', () => {
    expect(bodyTextEl.nativeElement.getAttribute('tabindex')).toBe('0');
  });

  it('should maintain all classes when type changes', () => {
    hostComponent.type = 'new-type';
    hostFixture.detectChanges();

    const updatedEl = hostFixture.debugElement.query(By.css('p'));
    const classList = Array.from(updatedEl.nativeElement.classList);
    expect(classList).toContain('body-test');
    expect(classList).toContain('new-type');
    expect(classList.length).toBe(2);
  });
});




