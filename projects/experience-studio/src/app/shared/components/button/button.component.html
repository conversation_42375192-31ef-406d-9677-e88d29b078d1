<button
  title="button"
  [class]="
    [
      'btn',
      variant,
      size,
      label,
      pill === 'true' ? 'pill' : '',
      state,
      iconPosition === 'right' ? 'icon-right' : '',
      iconPosition === 'left' ? 'icon-left' : '',
      iconPosition === 'only' ? 'icon-only' : '',
      animation !== 'none' ? animation : '',
      loading ? 'loading' : '',
      loading && loadingType === 'skeleton' ? 'skeleton' : ''
    ].join(' ')
  "
  tabindex="0"
  (keydown)="onKeydown($event)"
  (click)="handleClick($event)"
  [disabled]="disabled || loading"
  aria-live="polite"
  [attr.aria-busy]="loading"
  [style.width]="width"
  [style.height]="height"
  [style.background]="gradient"
>
  <!-- Skeleton Loading -->
  <div *ngIf="loading && loadingType === 'skeleton'" class="skeleton-content">
    <div class="skeleton-icon" *ngIf="iconPosition === 'left' || iconPosition === 'only'"></div>
    <div class="skeleton-text" *ngIf="iconPosition !== 'only'"></div>
    <div class="skeleton-icon" *ngIf="iconPosition === 'right'"></div>
  </div>

  <!-- Spinner Loading -->
  <div *ngIf="loading && loadingType === 'spinner'" class="loading-spinner">
    <div class="spinner"></div>
    <span class="loading-text">{{ loadingText }}</span>
  </div>

  <!-- Regular Button Content -->
  <ng-container *ngIf="!loading">
    <!-- Left Icon (before label) -->
    <exp-icons
      *ngIf="(iconPosition === 'left' || iconPosition === 'only') && iconName"
      [iconName]="iconName"
      [iconColor]="iconColor"
    >
    </exp-icons>

    <!-- Button Label (only show if iconPosition is NOT 'only') -->
    <span *ngIf="iconPosition !== 'only'"
      >{{ label }}<ng-content></ng-content
    ></span>

    <!-- Right Icon (after label) -->
    <exp-icons
      *ngIf="iconPosition === 'right' && iconName"
      [iconName]="iconName"
      [iconColor]="iconColor"
    >
    </exp-icons>
  </ng-container>
</button>
