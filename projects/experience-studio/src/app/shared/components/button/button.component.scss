/// Breakpoint mixins
$breakpoints: (
  mobile: 320px,
  tablet: 768px,
  desktop: 1024px,
  large-desktop: 1440px
);

@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// Fluid typography function
@function fluid-type($min-vw, $max-vw, $min-size, $max-size) {
  $slope: ($max-size - $min-size) / ($max-vw - $min-vw);
  $base-size: $min-size - ($slope * $min-vw);

  @return clamp(#{$min-size}rem, #{$base-size}rem + #{$slope * 100}vw, #{$max-size}rem);
}

:host {
  display: inline-flex;
  position: relative;
  width: auto;
  min-width: fit-content;

  // Responsive width handling
  @media (max-width: 576px) {
    &.w-100 {
      width: 100% !important;

      button {
        width: 100%;
      }
    }
  }

  // Grid system integration
  &.col-12 {
    padding-right: var(--toggle-font-size);
    padding-left: var(--toggle-font-size);
  }

  @media (min-width: 576px) {
    &.col-sm-auto {
      width: auto;
      max-width: none;
    }
  }

  @media (min-width: 768px) {
    &.col-md-auto {
      width: auto;
      max-width: none;
    }
  }

  @media (min-width: 992px) {
    &.col-lg-auto {
      width: auto;
      max-width: none;
    }
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--toast-spacing-3x);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  width: auto;
  min-width: fit-content;
  font-family: inherit;
  white-space: nowrap; // Prevent text wrapping

  // Size variants with responsive padding
  &.small {
    padding: clamp(0.25rem, 1vw, 0.5rem) clamp(0.5rem, 2vw, 1rem);
    font-size: clamp(0.75rem, 1vw, 0.875rem);
  }

  &.medium {
    padding: clamp(0.5rem, 1.5vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
    font-size: clamp(0.875rem, 1.2vw, 1rem);
  }

  &.large {
    padding: clamp(0.75rem, 2vw, 1rem) clamp(1.5rem, 4vw, 2rem);
    font-size: clamp(1rem, 1.5vw, 1.25rem);
  }

  // Icon handling
  .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: var(--toast-border-radius);

    @media (max-width: 576px) {
      font-size: var(--toggle-height-cont); // Slightly larger on mobile for better touch targets
    }
  }

  // Loading states
  &.loading {
    cursor: wait;
    opacity: 0.7;
    min-width: max-content;
    white-space: nowrap;
  }

  // Base styles with relative units
  --button-min-width: 6.25rem; // 100px
  --button-padding-x: clamp(0.75rem, 2vw, 1.25rem);
  --button-padding-y: clamp(0.5rem, 1.5vw, 0.75rem);
  --button-border-radius: 0.25rem;
  --button-font-size: #{fluid-type(320, 1440, 0.875, 1)};
  --button-icon-size: clamp(1rem, 1.5vw, 1.25rem);
  --button-gap: clamp(0.25rem, 0.5vw, 0.5rem);

  font-size: var(--button-font-size);
  font-weight: var(--font-font-weight-medium);
  line-height: 1.5;
  padding: var(--button-padding-y) var(--button-padding-x);
  border-radius: var(--button-border-radius);
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  min-width: var(--button-min-width);

  // Skeleton Loading styles
  &.skeleton {
    cursor: progress;
    pointer-events: none;
    background: var(--button-secondary-bg-bg) !important;
    border: 1px solid var(--button-secondary-border-disable) !important;

    .skeleton-content {
      display: flex;
      align-items: center;
      gap: var(--spacing-2x);
      width: 100%;

      .skeleton-icon {
        width: var(--toast-border-radius);
        height: var(--toast-border-radius);
        border-radius: 50%;
        background: linear-gradient(
          90deg,
          var(--button-secondary-border-disable) 25%,
          var(--button-secondary-border-action) 37%,
          var(--button-secondary-border-disable) 63%
        );
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
      }

      .skeleton-text {
        height: var(-toast-font-size);
        flex: 1;
        border-radius: var(--toggle-padding-header);
        background: linear-gradient(
          90deg,
          var(--button-secondary-border-disable) 25%,
          var(--button-secondary-border-action) 37%,
          var(--button-secondary-border-disable) 63%
        );
        background-size: 400% 100%;
        animation: skeleton-loading 1.4s ease infinite;
      }
    }

    &.small .skeleton-content {
      .skeleton-icon {
        width: var(--toggle-margin-bottom);
        height: var(--toggle-margin-bottom);
      }
      .skeleton-text {
        height: var(--toggle-margin-bottom);
      }
    }

    &.large .skeleton-content {
      .skeleton-icon {
        width: var(--toggle-height-cont);
        height: var(--toggle-height-cont);
      }
      .skeleton-text {
        height: var(-toast-border-radius);
      }
    }
  }

  // Animation styles
  &.ripple {
    position: relative;
    overflow: hidden;

    .ripple {
      position: absolute;
      border-radius: 50%;
      transform: scale(0);
      background: rgba(255, 255, 255, 0.7);
      pointer-events: none;
      z-index: 1;
      animation: rippleAnimation 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  &.pulse {
    &:active:not(:disabled) {
      animation: pulse 0.3s ease-in-out;
    }
  }

  awe-icons {
    display: inline-block;
    align-items: center;
    justify-content: center;
    width: var(--button-icon-size);
    height: var(--button-icon-size);
    flex-shrink: 0;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  &:not(:has(awe-icons)) {
    padding: var(--button-padding-y) var(--button-padding-x);
  }

  span {
    flex: 1;
    display: flex;
    justify-content: center;
  }

  &:focus-visible {
    outline: var(--button-outline-width) solid var(--focused-selected-ring);
    outline-offset: var(--button-outline-offset);
  }

  /* Default Disabled State */
  &:disabled {
    background: var(--button-primary-bg-disable);
    color: var(--button-primary-label-disable);
    cursor: not-allowed;
  }

  /* Pill Style */
  &.pill {
    border-radius: 100vmax;
  }

  &.icon-right {
    flex-direction: row-reverse;
  }

  &.icon-left {
    awe-icons {
      margin-left: auto;
    }
  }

  /* Icon-Only Button */
  &.icon-only {
    --button-min-width: auto;
    aspect-ratio: 1;
    padding: calc(var(--button-padding-y) * 0.75);

    @include respond-to(tablet) {
      padding: var(--button-padding-y);
    }
  }

  /* Button Variants */
  &.primary {
    background-color: var(--button-primary-bg-action);
    color: var(--button-primary-label-action);

    &:hover {
      background-color: var(--button-primary-hover-action);
    }

    &.active {
      background-color: var(--button-primary-bg-active);

      &:hover {
        background-color: var(--button-primary-hover-action);
      }
    }

    &.disabled {
      background-color: var(--button-primary-bg-disable);
      color: var(--button-primary-label-disable);
    }

    &.warning {
      background-color: var(--button-primary-bg-warning);
      color: var(--button-primary-label-warning);

      &:hover {
        background-color: var(--button-primary-hover-warning);
      }
    }

    &.danger {
      background-color: var(--button-primary-bg-danger);
      color: var(--button-primary-label-danger);

      &:hover {
        background-color: var(--button-primary-hover-danger);
      }
    }
  }

  &.secondary {
    background-color: var(--button-secondary-bg-bg);
    border: 1px solid var(--button-secondary-border-action);
    color: var(--button-secondary-label-action);

    &:hover {
      border-color: var(--focused-selected-ring);
    }

    &.active {
      border-color: var(--button-secondary-border-action);
      &:hover {
        background-color: var(--focused-selected-bg);
        border-color: var(--focused-selected-ring);
      }
    }

    &.disabled {
      background-color: var(--button-primary-bg-subtle);
      border-color: var(--button-secondary-border-disable);
      color: var(--button-secondary-label-disable);
      &:hover {
        background-color: #ffff;
      }
    }

    &.warning {
      border-color: var(--button-primary-bg-warning);
      color: var(--button-primary-bg-warning);

      &:hover {
        background-color: var(--button-warning-hover-bg);
      }
    }

    &.danger {
      border-color: var(--button-secondary-label-warning);
      color: var(--button-secondary-label-warning);

      &:hover {
        background-color: var(--button-danger-hover-bg);
      }
    }
  }

  // Maintain touch target size on mobile
  @include respond-to(mobile) {
    min-height: 2.75rem; // 44px minimum touch target
    margin-top: 8px;
  }
}

@keyframes rippleAnimation {
  0% {
    transform: scale(0);
    opacity: 0.5;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Responsive loading states
.loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--button-gap);
  width: 100%;
  min-width: 100%;

  .spinner {
    width: var(--button-icon-size);
    height: var(--button-icon-size);
    border: 2px solid currentColor;
    border-radius: 50%;
    border-right-color: transparent;
    animation: spin 0.75s linear infinite;
    flex-shrink: 0;
  }

  .loading-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// Skeleton loading with fluid dimensions
.skeleton-content {
  .skeleton-icon {
    width: var(--button-icon-size);
    height: var(--button-icon-size);
  }

  .skeleton-text {
    height: calc(var(--button-font-size) * 1.2);
  }
}

// Responsive hover and focus states
@media (hover: hover) {
  button:hover:not(:disabled) {
    transform: translateY(-1px);
  }
}

// High-density screen optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  button {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

// Core variables
:root {
  // Base sizes
  --btn-font-size-sm: clamp(0.75rem, 1.5vw, 0.875rem);
  --btn-font-size-md: clamp(0.875rem, 1.75vw, 1rem);
  --btn-font-size-lg: clamp(1rem, 2vw, 1.25rem);

  // Padding (horizontal, vertical)
  --btn-padding-sm: clamp(0.5rem, 1.5vw, 0.75rem) clamp(0.25rem, 1vw, 0.5rem);
  --btn-padding-md: clamp(0.75rem, 2vw, 1rem) clamp(0.5rem, 1.25vw, 0.75rem);
  --btn-padding-lg: clamp(1rem, 2.5vw, 1.5rem) clamp(0.75rem, 1.5vw, 1rem);

  // Icon sizes
  --btn-icon-size-sm: clamp(16px, 2vw, 18px);
  --btn-icon-size-md: clamp(18px, 2.5vw, 24px);
  --btn-icon-size-lg: clamp(24px, 3vw, 32px);

  // Border radius
  --btn-border-radius: clamp(4px, 0.5vw, 8px);
  --btn-border-radius-pill: 9999px;

  // Spacing
  --btn-gap: clamp(0.5rem, 1vw, 1rem);
  --btn-icon-gap: clamp(0.25rem, 0.75vw, 0.5rem);
}

.awe-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--btn-icon-gap);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  width: 100%;
  text-align: center;
  overflow: hidden;
  white-space: nowrap; // Prevent text wrapping

  // Size variants
  &.small {
    font-size: var(--btn-font-size-sm);
    padding: var(--btn-padding-sm);

    .awe-icons {
      width: var(--btn-icon-size-sm);
      height: var(--btn-icon-size-sm);
    }
  }

  &.medium {
    font-size: var(--btn-font-size-md);
    padding: var(--btn-padding-md);

    .awe-icons {
      width: var(--btn-icon-size-md);
      height: var(--btn-icon-size-md);
    }
  }

  &.large {
    font-size: var(--btn-font-size-lg);
    padding: var(--btn-padding-lg);

    .awe-icons {
      width: var(--btn-icon-size-lg);
      height: var(--btn-icon-size-lg);
    }
  }

  // Border radius
  border-radius: var(--btn-border-radius);
  &[pill="true"] {
    border-radius: var(--btn-border-radius-pill);
  }

  // Variants
  &.primary {
    background: var(--primary-color);
    color: var(--white);

    &:hover:not(:disabled) {
      background: var(--primary-hover);
    }

    &:active:not(:disabled) {
      background: var(--primary-active);
    }
  }

  &.secondary {
    background: var(--secondary-color);
    color: var(--text-color);
    border: 1px solid var(--border-color);

    &:hover:not(:disabled) {
      background: var(--secondary-hover);
    }

    &:active:not(:disabled) {
      background: var(--secondary-active);
    }
  }

  // States
  &.active {
    background: var(--primary-active);
  }

  &.disabled,
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.danger {
    background: var(--danger-color);
    color: var(--white);

    &:hover:not(:disabled) {
      background: var(--danger-hover);
    }
  }

  &.warning {
    background: var(--warning-color);
    color: var(--text-color);

    &:hover:not(:disabled) {
      background: var(--warning-hover);
    }
  }

  // Loading states
  &.loading {
    position: relative;
    cursor: wait;

    .button-content {
      visibility: hidden;
    }

    &.spinner::after {
      content: '';
      position: absolute;
      width: clamp(16px, 2.5vw, 24px);
      height: clamp(16px, 2.5vw, 24px);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: var(--white);
      animation: spin 0.8s linear infinite;
    }

    &.skeleton {
      background: linear-gradient(
        90deg,
        var(--skeleton-start) 0%,
        var(--skeleton-end) 50%,
        var(--skeleton-start) 100%
      );
      background-size: 200% 100%;
      animation: skeleton-loading 1.5s infinite;
    }
  }

  // Icon positioning
  &.icon-left {
    flex-direction: row;
  }

  &.icon-right {
    flex-direction: row-reverse;
  }

  &.icon-only {
    padding: clamp(0.5rem, 1.5vw, 0.75rem);
    aspect-ratio: 1;

    .awe-icons {
      margin: 0;
    }
  }

  // Animations
  &.ripple {
    .ripple {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.5);
      transform: scale(0);
      animation: ripple 0.6s linear;
    }
  }

  &.pulse {
    &:active:not(:disabled) {
      animation: pulse 0.3s ease-in-out;
    }
  }
}

// Responsive breakpoints
@media (min-width: 768px) {
  .awe-button {
    width: auto;

    &[fullWidthOnMobile="false"] {
      width: auto;
    }
  }
}

// Animations
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

// Touch target optimization
@media (hover: none) and (pointer: coarse) {
  .awe-button {
    min-height: 44px; // Minimum touch target size

    &.icon-only {
      min-width: 44px;
    }
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .awe-button {
    &.primary {
      border: 2px solid var(--text-color);
    }

    &.secondary {
      border: 2px solid var(--text-color);
    }
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .awe-button {
    transition: none;

    &.ripple .ripple {
      display: none;
    }

    &.pulse:active:not(:disabled) {
      animation: none;
    }

    &.loading.spinner::after {
      animation: none;
    }

    &.loading.skeleton {
      animation: none;
      background: var(--skeleton-start);
    }
  }
}




// Hover Effect System - Add this to your button.component.scss

// Mixins for reusable hover effects
@mixin hover-effect($name, $props) {
  &.hover-#{$name} {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    @each $prop, $value in $props {
      &:hover {
        #{$prop}: #{$value};
      }
    }
  }
}

// Define hover effects with their properties
@mixin hover-effects() {
  // Scale effect
  @include hover-effect('scale', (
    'transform': 'scale(1.05)'
  ));
  
  // Glow effect
  @include hover-effect('glow', (
    'box-shadow': '0 0 15px rgba(var(--primary-rgb, 0, 123, 255), 0.5)'
  ));
  
  // Lift effect
  @include hover-effect('lift', (
    'transform': 'translateY(-3px)',
    'box-shadow': '0 4px 8px rgba(0, 0, 0, 0.1)'
  ));
  
  // Slide background effect
  @include hover-effect('slide-bg', ());
  
  // Expand border effect
  @include hover-effect('expand-border', ());
  
  // Text shift effect
  @include hover-effect('text-shift', ());
  
  // Color transition effects
  @include hover-effect('color-shift', ());
}

// Apply all hover effects to button
button {
  @include hover-effects();
  
  // For advanced effects like slide-bg that require pseudo-elements
  &.hover-slide-bg {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 0;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.1);
      transition: width 0.3s ease;
      z-index: 0;
    }
    
    &:hover::before {
      width: 100%;
    }
    
    span, awe-icons {
      position: relative;
      z-index: 1;
    }
  }
  
  &.hover-expand-border {
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 0;
      height: 2px;
      background-color: currentColor;
      transition: width 0.3s ease, left 0.3s ease;
    }
    
    &:hover::after {
      width: 100%;
      left: 0;
    }
  }
  
  &.hover-text-shift {
    &:hover span {
      transform: translateX(3px);
    }
    
    span {
      transition: transform 0.3s ease;
    }
  }
  
 
}

// Custom hover effect with parameters
@mixin custom-hover-effect($name, $property, $value) {
  &.hover-custom-#{$name} {
    transition: #{$property} 0.3s ease;
    
    &:hover {
      #{$property}: #{$value};
    }
  }
}

// Apply custom hover effects 
button {
  @include custom-hover-effect('rotate', 'transform', 'rotate(5deg)');
  @include custom-hover-effect('opacity', 'opacity', '0.8');
  @include custom-hover-effect('saturate', 'filter', 'saturate(150%)');
}
