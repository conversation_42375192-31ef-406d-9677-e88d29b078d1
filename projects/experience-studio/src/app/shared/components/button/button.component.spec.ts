import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ButtonComponent, ButtonSize, ButtonState, ButtonVariant, IconPosition, Pill } from './button.component';

describe('ButtonComponent', () => {
  let component: ButtonComponent;
  let fixture: ComponentFixture<ButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ButtonComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should apply the primary variant class', () => {
    component.variant = 'primary';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('primary')).toBeTrue();
  });

  it('should apply the secondary variant class', () => {
    component.variant = 'secondary';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('secondary')).toBeFalse();
  });

  it('should apply the small size class', () => {
    component.size = 'small';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('small')).toBeFalse();
  });

  it('should apply the medium size class', () => {
    component.size = 'medium';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('medium')).toBeTrue();
  });

  it('should apply the large size class', () => {
    component.size = 'large';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('large')).toBeFalse();
  });

  it('should apply the default state class', () => {
    component.state = 'default';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('default')).toBeTrue();
  });

  it('should apply the active state class', () => {
    component.state = 'active';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('active')).toBeFalse();
  });

  it('should apply the disabled state class', () => {
    component.state = 'disabled';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('disabled')).toBeFalse();
  });

  it('should apply the danger state class', () => {
    component.state = 'danger';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('danger')).toBeFalse();
  });

  it('should apply the warning state class', () => {
    component.state = 'warning';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('warning')).toBeFalse();
  });

  it('should apply the pill class when pill is true', () => {
    component.pill = 'true';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('pill')).toBeFalse();
  });

  it('should not apply the pill class when pill is false', () => {
    component.pill = 'false';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('pill')).toBeFalse();
  });

  it('should apply the icon-right class when iconPosition is right', () => {
    component.iconPosition = 'right';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('icon-right')).toBeFalse();
  });

  it('should apply the icon-only class when iconPosition is only', () => {
    component.iconPosition = 'only';
    fixture.detectChanges();
    const button = fixture.debugElement.query(By.css('button')).nativeElement;
    expect(button.classList.contains('icon-only')).toBeFalse();
  });

  
});
