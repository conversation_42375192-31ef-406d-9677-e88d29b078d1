
import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  ChangeDetectionStrategy,
  EventEmitter,
  Output,
  ViewEncapsulation,
  HostBinding,
  ElementRef,
  Renderer2,
} from '@angular/core';
import { Router } from '@angular/router';
import { IconsComponent } from '../icons/icons.component';

export type ButtonVariant = 'primary' | 'secondary';
export type ButtonSize = 'small' | 'medium' | 'large';
export type ButtonState =
  | 'default'
  | 'active'
  | 'disabled'
  | 'danger'
  | 'warning';
export type IconPosition = 'left' | 'right' | 'only';
export type Pill = 'true' | 'false';
export type ButtonAnimation = 'ripple' | 'pulse' | 'none';
export type LoadingType = 'spinner' | 'skeleton' | 'none';
// Add the hover effect type
export type HoverEffect =
  | 'none'
  | 'scale'
  | 'glow'
  | 'lift'
  | 'slide-bg'
  | 'expand-border'
  | 'text-shift'
  | 'color-shift'
  | 'custom-rotate'
  | 'custom-opacity'
  | 'custom-saturate';

@Component({
  selector: 'awe-button',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  host: {
    '[class.col-12]': 'true',
    '[class.col-sm-auto]': 'true',
    '[class.col-md-auto]': 'true',
    '[class.col-lg-auto]': 'true',
    '[class.align-self-stretch]': 'true'
  }
})
export class ButtonComponent {
  @Input() label = '';
  @Input() variant: ButtonVariant = 'primary';
  @Input() size: ButtonSize = 'medium';
  @Input() state: ButtonState = 'default';
  @Input() disabled = false;
  @Input() loading = false;
  @Input() loadingType: LoadingType = 'spinner';
  @Input() iconPosition: IconPosition = 'left';
  @Input() iconColor:
    | 'action'
    | 'danger'
    | 'disable'
    | 'neutralIcon'
    | 'success'
    | 'warning'
    | 'whiteIcon'
    | 'blue'
    | 'pink'
    | 'vscodeblue'
    | '' = '';
  @Input() pill: Pill = 'false';
  @Input() url: string | undefined;
  @Input() route: string | undefined;
  @Input() iconName = '';
  @Input() animation: ButtonAnimation = 'none';
  @Input() loadingText = 'Loading...';
  @Input() skeletonWidth = '120px';

  // New Inputs for dynamic width, height, and gradient
  @Input() width: string = 'auto';
  @Input() height: string = 'auto';
  @Input() gradient: string = '';

  // Add the hover effect input
  @Input() hoverEffect: HoverEffect = 'none';
  // Add custom hover properties (optional)
  @Input() hoverCustomProperty: string = '';
  @Input() hoverCustomValue: string = '';

  // Responsive classes
  @Input() fullWidthOnMobile = true;
  @Input() alignSelf: 'start' | 'center' | 'end' | 'stretch' = 'stretch';

  @HostBinding('class') get hostClasses() {
    return {
      'loading': this.loading,
      'skeleton': this.loading && this.loadingType === 'skeleton',
      'ripple': this.animation === 'ripple',
      [`align-self-${this.alignSelf}`]: true,
      'w-100': this.fullWidthOnMobile
    };
  }

  private rippleElement: HTMLElement | null = null;

  constructor(
    private router: Router,
    private elementRef: ElementRef,
    private renderer: Renderer2
  ) {}

  @Output() userClick = new EventEmitter<Event>();

  ngOnInit(): void {
    this.applyHoverEffect();
  }

  // Method to apply hover effect classes to the button element
  private applyHoverEffect(): void {
    if (this.hoverEffect && this.hoverEffect !== 'none') {
      setTimeout(() => {
        const button = this.elementRef.nativeElement.querySelector('button');
        if (button) {
          this.renderer.addClass(button, `hover-${this.hoverEffect}`);
        }
      });
    }

    // Apply custom hover effect if provided
    if (this.hoverCustomProperty && this.hoverCustomValue) {
      setTimeout(() => {
        const button = this.elementRef.nativeElement.querySelector('button');
        if (button) {
          this.renderer.setStyle(button, '--hover-custom-property', this.hoverCustomProperty);
          this.renderer.setStyle(button, '--hover-custom-value', this.hoverCustomValue);
          this.renderer.addClass(button, 'hover-custom');
        }
      });
    }
  }

  onKeydown(event: KeyboardEvent): void {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.navigate();
    }
  }

  handleHeadingClick() {
    console.log('Heading clicked or Enter/Space pressed');
  }

  navigate(): void {
    if (this.route) {
      console.log('routing is not yet done');
    } else if (this.url) {
      // Open the external URL in a new tab
      window.open(this.url, '_blank');
    }
  }

  handleClick(event: Event): void {
    if (this.disabled || this.loading) {
      event.preventDefault();
      return;
    }

    if (this.animation === 'ripple') {
      this.createRippleEffect(event as MouseEvent);
    }

    this.userClick.emit(event);
    this.navigate();
  }

  private createRippleEffect(event: MouseEvent): void {
    if (this.rippleElement) {
      this.renderer.removeChild(this.elementRef.nativeElement.querySelector('button'), this.rippleElement);
    }

    const button = this.elementRef.nativeElement.querySelector('button');
    const diameter = Math.max(button.clientWidth, button.clientHeight);
    const radius = diameter / 2;

    const rect = button.getBoundingClientRect();
    const left = event.clientX - rect.left - radius;
    const top = event.clientY - rect.top - radius;

    const ripple = this.renderer.createElement('span');
    this.renderer.addClass(ripple, 'ripple');
    this.renderer.setStyle(ripple, 'width', `${diameter}px`);
    this.renderer.setStyle(ripple, 'height', `${diameter}px`);
    this.renderer.setStyle(ripple, 'left', `${left}px`);
    this.renderer.setStyle(ripple, 'top', `${top}px`);

    this.rippleElement = ripple;
    this.renderer.appendChild(button, ripple);

    const animationDuration = 600;
    setTimeout(() => {
      if (this.rippleElement) {
        this.renderer.removeChild(button, this.rippleElement);
        this.rippleElement = null;
      }
    }, animationDuration);
  }
}
