<div
  class="awe-card"
  [ngClass]="cardClasses"
  (mouseenter)="onMouseEnter()"
  (mouseleave)="onMouseLeave()"
  (click)="onClick()"
  [attr.role]="'article'"
  [attr.tabindex]="0"
>
  <div class="awe-card__header" *ngIf="hasHeader">
    <ng-content select="[header]"></ng-content>
  </div>

  <div class="awe-card__content">

    <!-- Gen AI Learning Path Variant -->
    <div class="gen-ai-learning-path-content" *ngIf="variant === 'gen-ai-learning-path'">
      <div class="gen-ai-learning-path-image" *ngIf="imageTop">
        <img [src]="imageTop" [alt]="title || 'Gen AI Learning Path image'" class="gen-ai-learning-path-top-image" />
      </div>

      <div class="gen-ai-learning-path-text-container">
        <div class="gen-ai-learning-path-title-timestamp">
          <h2 class="gen-ai-learning-path-title" *ngIf="title">{{ title }}</h2>
          <p class="gen-ai-learning-path-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        </div>

        <div class="gen-ai-learning-path-read-more" *ngIf="readMoreText">
          {{ readMoreText }}
        </div>

        <div class="gen-ai-learning-path-divider"></div>

        <div class="gen-ai-learning-path-footer">
          <img
            [src]="footerImage"
            [alt]="footerImageAlt || 'Footer image'"
            class="gen-ai-learning-path-footer-image"
            *ngIf="footerImage"
          />
          <span class="gen-ai-learning-path-footer-name" *ngIf="footerName">{{ footerName }}</span>
          <span class="gen-ai-learning-path-footer-icon" *ngIf="footerIcon">
            <exp-icons [iconName]="footerIcon" color="#858AAD"></exp-icons>
          </span>
          <span class="gen-ai-learning-path-footer-count" *ngIf="footerCount">{{ footerCount }}</span>
        </div>
      </div>
    </div>

    <!-- Card Horizontal Gen AI Variant -->
    <div class="card-horizontal-gen-ai-content" *ngIf="variant === 'card-horizontal-gen-ai'">
      <div class="horizontal-gen-ai-container">
        <div class="card-horizontal-gen-ai-image" *ngIf="imageTop">
          <img [src]="imageTop" [alt]="title || 'Card Horizontal Gen AI image'" class="card-horizontal-gen-ai-top-image" />
        </div>

        <div class="card-horizontal-gen-ai-text-container">
          <div class="card-horizontal-gen-ai-title-timestamp">
            <h2 class="card-horizontal-gen-ai-title" *ngIf="title">{{ title }}</h2>
            <p class="card-horizontal-gen-ai-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
          </div>

          <div class="card-horizontal-gen-ai-read-more" *ngIf="readMoreText">
            {{ readMoreText }}
          </div>

          <div class="card-horizontal-gen-ai-divider"></div>

          <div class="card-horizontal-gen-ai-footer">
            <img
              [src]="footerImage"
              [alt]="footerImageAlt || 'Footer image'"
              class="card-horizontal-gen-ai-footer-image"
              *ngIf="footerImage"
            />
            <span class="card-horizontal-gen-ai-footer-name" *ngIf="footerName">{{ footerName }}</span>
            <span class="card-horizontal-gen-ai-footer-icon" *ngIf="footerIcon">
              <exp-icons [iconName]="footerIcon" color="#858AAD"></exp-icons>
            </span>
            <span class="card-horizontal-gen-ai-footer-count" *ngIf="footerCount">{{ footerCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Feature Post Variant -->
    <div class="feature-post-content" *ngIf="variant === 'feature-post'">
      <div class="feature-post-image" *ngIf="imageTop">
        <img [src]="imageTop" [alt]="title || 'Feature post image'" class="top-image" />
        <div class="top-label" *ngIf="topLabel">{{ topLabel }}</div>
      </div>

      <div class="text-container">
        <div class="title-timestamp">
          <h2 class="feature-post-title" *ngIf="title">{{ title }}</h2>
          <span class="timestamp" *ngIf="timestamp">{{ timestamp }}</span>
        </div>

        <p class="feature-post-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        <p class="feature-note" *ngIf="note">{{ note }}</p>
      </div>
    </div>

    <!-- Default Card Content (excluded for specific variants) -->
    <div
      class="awe-card__text"
      *ngIf="variant !== 'feature-post' && variant !== 'gen-ai-learning-path' && variant !== 'card-horizontal-gen-ai'"
    >
      <div class="awe-card_details">
        <!-- Elderwand-agent special layout -->
        <div class="title-rating-wrapper" *ngIf="variant === 'elderwand-agent' && title">
          <h2 class="awe-card__title">{{ title }}</h2>
          <div class="rating-display" *ngIf="rating">
            <span class="star-icon">
              <exp-icons [iconName]="ratingIconName || 'star'" color="#FFD700"></exp-icons>
            </span>
            <span class="rating-value">{{ rating }}</span>
          </div>
        </div>

        <!-- Title for other variants -->
        <h2 class="awe-card__title" *ngIf="variant !== 'elderwand-agent' && title">{{ title }}</h2>
        <p class="awe-card__subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        <ng-content select="[content]"></ng-content>
      </div>

      <!-- User Info for elderwand-agent -->
      <div class="user-info" *ngIf="userCount && variant === 'elderwand-agent'">
        <div class="user-views">
          <span class="user-icon">
            <exp-icons [iconName]="userIconName || 'user'" color="#858AAD"></exp-icons>
          </span>
          <span class="user-count">{{ userCount }}</span>
        </div>

        <div
          class="platform-badge"
          *ngIf="platformName && variant === 'elderwand-agent'"
          [ngStyle]="{
            'background-color': platformBadgeBgColor,
            'color': platformBadgeTextColor
          }"
        >
          <exp-icons *ngIf="platformIconName" [iconName]="platformIconName" class="platform-icon"></exp-icons>
          {{ platformName }}
        </div>
      </div>

      <!-- Optional Icons -->
      <div class="icon-section" *ngIf="leftIcons?.length">
        <exp-icons
          *ngFor="let icon of leftIcons"
          [iconName]="icon"
          class="icon-wrapper circular-icon"
          (click)="handleIconClick?.(icon)"
        ></exp-icons>
      </div>
    </div>

    <!-- Media Image -->
    <div class="awe-card__media" *ngIf="image">
      <img
        [src]="image"
        [alt]="title || 'Card image'"
        class="awe-card__image"
      />
    </div>
  </div>

  <!-- Footer Content Slot -->
  <div class="awe-card__footer" *ngIf="hasFooter">
    <ng-content select="[footer]"></ng-content>
  </div>
</div>
