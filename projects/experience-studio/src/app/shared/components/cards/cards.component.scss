// Variables
:root {
  --awe-card-border-radius: 6px;
  --awe-card-padding-small: 16px;
  --awe-card-padding-medium: 24px;
  --awe-card-padding-large: 32px;
  --awe-card-shadow: 0 2px 4px rgba(0, 0, 0, 0.16);
  --awe-card-shadow-elevated: 0 8px 16px rgba(0, 0, 0, 0.16);
}

.awe-card {
  position: relative;
  display: flex;
  flex-direction: column;
  border-radius: var(--awe-card-border-radius);
  overflow: hidden;
  transition: all 0.3s ease;  

  // Sizes
  &--small {
    width: 100%;
    min-height: 133px;
    padding: var(--awe-card-padding-small);
  }

  &--medium {
    width: 100%;
    min-height: 200px;
    padding: var(--awe-card-padding-medium);
  }

  &--auto{
    width: 100%;
    height: 100%;
    padding: 20px;
  }

  &--large {
    width: 100%;
    min-height: 285px;
    padding: var(--awe-card-padding-large);
  }

  &--auto{
    width: 100%;
    height: auto;
    padding: 20px;
  }

  // Themes
  &--light {
    background: var(--Neutral-N-100, #ededf3);
    color: var(--Neutral-N-1000, #292c3d);
    box-shadow: var(--awe-card-shadow);
  }

  &--dark {
    background: var(--Neutral-N-1050, #14161f);
    color: var(--Neutral-N-100, #ededf3);
  }

  &--eldewand-studio{
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.90);
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.30) 0%, rgba(255, 255, 255, 0.42) 100%);
    box-shadow: 0px 0px 16px 0px rgba(225, 225, 225, 0.25);
    backdrop-filter: blur(25px);
  }
  
  &--elderwand-agent {
    border-radius: 16px;
    background: #fff;
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.05);
    padding: 24px;
  }

  // Variants
  &--feature {
    padding-right: 0 !important;
    padding-bottom: 0 !important;

    .awe-card__content {
      display: flex;
      gap: 24px;
    }

    .awe-card__text {
      flex: 1;
      max-width: 60%;
    }

    .awe-card__media {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  &--info {
    text-align: center;
    padding-right: 0 !important;
    padding-bottom: 0 !important;

    .awe-card__media {
      margin: -24px -24px 24px;
    }
  }

  &--basic{
    padding-right: 0 !important;
    padding-bottom: 0 !important;
  }

  &--elderwand-studio {
    .awe-card {
      width: 100%;
      height: 100%;
      padding: 24px;
  
      &__content {
        display: flex;
        flex-wrap: nowrap; // Prevent wrapping
        gap: 16px;
        min-height:200px;
        padding-top: 16px;
        padding-left: 8px;
        overflow: hidden;
  
        .awe-card__text,
        .awe-card__media {
          flex: 0 0 50%; // Fixed 50% width
          max-width: 50%;
        }
  
        .awe-card__text {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
  
          .awe-card_details {
            width: 100%;
            height: 50%;
            overflow: unset;

            .awe-card__title{
              align-self: stretch;
              color: #1D1D1D;
              font-family: Mulish;
              font-size: 40px;
              font-style: normal;
              font-weight: 700;
              line-height: 120%; /* 48px */
            }

            .awe-card__subtitle{
              color: #595959;
              font-family: Mulish;
              font-size: 20px;
              font-style: normal;
              font-weight: 500;
              line-height: 150%; /* 30px */       
            }
          }
  
          .icon-section {
            width: 100%;
            height: 50%;
            display: flex;
            justify-content: start;
            align-items: end;
            gap: 12px;
  
            .icon-wrapper {
              width: 32px;
              height: 32px;
              flex-shrink: 0;
              aspect-ratio: 1 / 1;
              cursor: pointer;
  
              &:hover {
                fill: linear-gradient(180deg, #C2C3FF 0%, #7D7FD9 100%);
              }
            }
          }
        }
  
        .awe-card__media {
          display: flex;
          align-items: center;
          justify-content: center;
  
          img,
          .awe_elderwand_imagesrc {
            width: 100%;
            height: 100%;
            object-fit: contain;
            display: block;
          }
        }
      }
    }
  
    // Optional: For very small screens, allow horizontal scroll if needed
    @media (max-width: 768px) {
      .awe-card__content {
        overflow-x: auto; // Optional for strict layout
      }
    }
  }
  
  &--elderwand-agent {
    position: relative;
    padding: 24px;
  
    .awe-card__content {
      display: flex;
      flex-direction: column;
    }
  
    .awe-card__title {
      color: #333;
      font-family: Mulish, sans-serif;
      font-size: 20px;
      font-weight: 700;
      line-height: 120%;
      margin-bottom: 8px;
      padding-left: 6px;
    }
  
    .awe-card__subtitle {
      color: #52577A;
      font-family: Mulish, sans-serif;
      font-size: 16px;
      font-weight: 600;
      line-height: 150%;
      margin-top: 15px;
      padding-left: 6px;
    }
  
    .title-rating-wrapper {
      display: flex;
      align-items: center;
      flex-wrap: wrap; /* Allow wrapping on smaller screens */
    }
  
    .awe-card__title {
      margin-right: 8px; /* Adjust as needed */
      flex: 1; /* Allow title to take available space */
    }
  
    .rating-display {
      display: flex;
      align-items: center;
      margin-left: auto; /* Push rating to the right */
    }
  
    .star-icon {
      margin-right: 4px; /* Adjust as needed */
      color: #FFD700;
      font-size: 16px;
    }
  
    .rating-value {
      color: #333;
      font-family: Inter, sans-serif;
      font-weight: 600;
      font-size: 16px;
    }
  
    .user-info {
      display: flex;
      align-items: center;
      margin-top: 30px;
      padding-left: 6px;

      .user-views{
        display: flex;
        align-items: flex-start;
      }
    }
  
    .user-icon {
      margin-right: 4px;
      color: #595959;
      display: flex;
      align-items: center;
    }
  
    .user-count {
      color: #858AAD;
      font-family: Mulish, sans-serif;
      font-size: 14px;
      font-weight: 700;
      padding-bottom: 4px;
      margin-top: 0.5px;
    }
  
    .platform-badge {
      margin-left: auto; /* Push to the right */
      // margin-top: 10px; /* Add some spacing */
      padding: 2px 14px;
      border-radius: 6px;
      font-family: Mulish, sans-serif;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  
    /* Responsive adjustments */
    @media (max-width: 768px) {
      padding: 16px;
  
      .awe-card__title {
        font-size: 18px;
      }
  
      .awe-card__subtitle {
        font-size: 14px;
      }
  
      .rating-display {
        margin-left: 0; /* Reset margin for smaller screens */
        margin-top: 8px; /* Add some spacing */
      }
  
      .platform-badge {
        margin-left: 0; /* Reset margin for smaller screens */
        // margin-top: 8px; /* Add some spacing */
      }
  
      .user-info {
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;
      }
  
     
    }
  
    @media (max-width: 480px) {
      padding: 12px;
  
      .awe-card__title {
        font-size: 16px;
      }
  
      .awe-card__subtitle {
        font-size: 12px;
      }
  
      .rating-display {
        margin-top: 4px; /* Add some spacing */
      }
  
      .platform-badge {
        margin-top: 4px; /* Add some spacing */
      }
    }
  }
  
  &--feature-post {
    display: flex;
    padding: 0;
    flex-direction: column;
    justify-content: center;
    gap: 24px;
    align-self: stretch;
    width: 426px;
    min-height: 174px;

    .feature-post-content {
      width: 100%;
      display: flex;
      flex-direction: column;

      .feature-post-image {
        position: relative;
        height: 200px;
        flex-shrink: 0;
        align-self: stretch;

        .top-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px 12px 0 0;
          filter: brightness(0.8);
        }

        .top-label {
          position: absolute;
          top: 16px;
          right: 16px;
          display: flex;
          height: 26px;
          padding: 4px 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          color: var(--Text-Subtitle, #292C3D);
          font-family: Mulish;
          font-size: 12px;
          font-weight: 600;
          line-height: 150%;
          background: rgba(255, 255, 255, 0.9);
          border-radius: 4px;
        }
      }

      .text-container {
        display: flex;
        padding: 24px;
        flex-direction: column;
        justify-content: center;
        align-self: stretch;

        .title-timestamp {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          margin-bottom: 8px;

          .feature-post-title {
            color: var(--Text-Title, #14161F);
            font-family: Mulish;
            font-size: 24px;
            font-weight: 700;
            line-height: 150%;
            margin: 0;
          }

          .timestamp {
            color: var(--Text-Caption, #666D99);
            font-family: Mulish;
            font-size: 14px;
            font-weight: 400;
            line-height: 150%;
          }
        }

        .feature-post-subtitle {
          height: 48px;
          color: var(--Text-Subtitle, #292C3D);
          font-family: Mulish;
          font-size: 16px;
          font-weight: 400;
          // line-height: 24px;
          margin: 0;
        }

        .feature-note {
          color: var(--Neutral-N-500, #858AAD);
          font-family: Mulish;
          font-size: 12px;
          font-style: italic;
          font-weight: 400;
          line-height: 150%;
          margin: 0;
        }
      }
    }
  }

  // Elements
  &__content {
    flex: 1;
    position: relative;
    z-index: 1;
  }

  &__text {
    position: relative;
    z-index: 2;
  }

  &__title {
    font-family: var(--font-font-family-heading);
    font-size: 28px;
    font-weight: var(--font-font-weight-bold);
    line-height: 120%;
    margin: 0 0 8px;
  }

  &__subtitle {
    font-family: var(--font-font-family-heading);
    font-size: 14px;
    font-weight: var(--font-font-weight-regular);
    line-height: 150%;
    margin: 0;
    opacity: 0.8;
  }

  &__media {
    position: relative;
    z-index: 1;
  }

  &__image {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
    object-position: center;
  }

  &__footer {
    margin-top: auto;
    padding-top: var(--awe-card-padding-small);
  }

  // Image positioning
  &--image-right {
    .awe-card__media {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      width: 45%;
      overflow: hidden;
      margin: 0;
      padding: 0;

      .awe-card__image {
        position: absolute;
        right: 0;
        bottom: 0;
        height: 100%;
        width: auto;
        max-width: none;
        object-fit: cover;
        object-position: right bottom;
      }
    }

    .awe-card__text {
      width: 55%;
      padding-right: 16px;
    }
  }

  &--image-left {
    .awe-card__media {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      width: 45%;
      overflow: hidden;
      margin: 0;
      padding: 0;

      .awe-card__image {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 100%;
        width: auto;
        max-width: none;
        object-fit: cover;
        object-position: left bottom;
      }
    }

    .awe-card__text {
      margin-left: 45%;
      width: 55%;
      padding-left: 16px;
    }
  }

  // States
  &--elevated {
    box-shadow: var(--awe-card-shadow-elevated);
  }

  // Animations
  &--fadeIn.awe-card--animated {
    animation: aweCardFadeIn 0.3s ease-in-out;
  }

  &--zoomIn.awe-card--animated {
    animation: aweCardZoomIn 0.3s ease-out;
  }

  &--flip.awe-card--animated {
    animation: aweCardFlip 0.6s ease-in-out;
  }
}

// Animation keyframes
@keyframes aweCardFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes aweCardZoomIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes aweCardFlip {
  from {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  to {
    transform: perspective(400px) rotateY(0);
    opacity: 1;
  }
}


.awe-card--gen-ai-learning-path {
  display: flex;
  flex-direction: row;
  background-color: #f5f6fa;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  padding: 0;
  border: 1px solid #e0e0e0;
  font-family: 'Segoe UI', sans-serif;
  max-width: 947px;
  width: 576px;

  .gen-ai-learning-path-image {
    flex: 1 1 40%;
    display: flex;
    align-items: center;
    justify-content: center;
      width: 576px;

    .gen-ai-learning-path-top-image {
      width: 576px;
      height: 228px;
      object-fit: cover;
      border-top-left-radius: 12px;
    }
  }

  .gen-ai-learning-path-text-container {
    flex: 1 1 60%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #f5f6fa;
        width: 576px;

    .gen-ai-learning-path-title-timestamp {
      margin-bottom: 12px;

      .gen-ai-learning-path-title {
        font-size: 1.5rem;
        font-weight: 500;
        color: #292C3D;
        margin: 0 0 8px 0;
        line-height: 140%;
      }

      .gen-ai-learning-path-subtitle {
        font-size: 14px;
        color: #33364D;
        margin: 0;
        line-height: 1.4;
        font-family: 'Mulish';
      }
    }

    .gen-ai-learning-path-read-more {
      font-size: 1rem;
      background: linear-gradient(90.1deg, #F96CAB -14.75%, #4244C2 34.27%);
      background-clip: text;
      color: transparent;
     
      font-weight: 500;
      cursor: pointer;
      margin-bottom: 12px;
    }

    .gen-ai-learning-path-divider {
      height: 1px;
      background-color: #dcdcdc;
      margin: 12px 0;
    }

    .gen-ai-learning-path-footer {
      display: flex;
      align-items: center;
      gap: 8px;

      .gen-ai-learning-path-footer-image {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
      }

      .gen-ai-learning-path-footer-name {
        font-size: 14px;
        color: #292C3D;
        font-weight: 500;
        opacity: 60%;
        font-family: 'Mulish';
        line-height: 140%;
      }

      .gen-ai-learning-path-footer-icon {
        display: flex;
        align-items: center;
        margin-left: auto;
        color: #858aad;

        awe-icons {
          vertical-align: middle;
        }
      }

      .gen-ai-learning-path-footer-count {
        font-size: 14px;
        font-weight: 400;
        font-family: 'Mulish';
        width: 26px;
        height: 20px;
        color: #292C3D;
        opacity: 60%;

      }
    }
  }
}


.awe-card--card-horizontal-gen-ai {
  display: flex;
  flex-direction: row;
  background-color: #f5f6fa;
  border-radius:15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  padding: 0;
  border: 1px solid #e0e0e0;
  font-family: 'Segoe UI', sans-serif;
  max-width: 947px;
  width: 576px;
  height: 228px;

  .card-horizontal-gen-ai-image {
  
   
    width: auto;

    .card-horizontal-gen-ai-side-image {
      width: 100%;
      height: 100%;  
      border-top-left-radius: 11px;
      border-bottom-left-radius: 11px;
    }
  }

  .card-horizontal-gen-ai-text-container {
    flex: 1 1 60%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #f5f6fa;
    padding-left: 16px;
    padding-bottom: 20px;
    padding-top: 10px;

    .card-horizontal-gen-ai-title-timestamp {
      margin-bottom: 12px;

      .card-horizontal-gen-ai-title {
        font-size: 1.5rem;
        font-weight: 500;
        color: #292C3D;
        margin: 0 0 8px 0;
        line-height: 140%;
      }

      .card-horizontal-gen-ai-subtitle {
        font-size: 14px;
        color: #33364D;
        margin: 0;
        line-height: 1.4;
        font-family: 'Mulish';
        width: 316px;
      }
    }

    .card-horizontal-gen-ai-read-more {
      font-size: 1rem;
      background: linear-gradient(90.1deg, #F96CAB -14.75%, #4244C2 34.27%);
      background-clip: text;
      color: transparent;

      font-weight: 500;
      cursor: pointer;
      margin-bottom: 12px;
    }

    .card-horizontal-gen-ai-divider {
      height: 1px;
      background-color: #dcdcdc;
      margin: 12px 0;
      width: 316px;
    }

    .card-horizontal-gen-ai-footer {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 316px;

      .card-horizontal-gen-ai-footer-image {
        width: 32px;
        height: 32px;
        border-radius: 50%;
       
      }

      .card-horizontal-gen-ai-footer-name {
        font-size: 14px;
        color: #292C3D;
        font-weight: 500;
        opacity: 60%;
        font-family: 'Mulish';
        line-height: 140%;
      }

      .card-horizontal-gen-ai-footer-icon {
        display: flex;
        align-items: center;
        margin-left: auto;
        color: #858aad;

        awe-icons {
          vertical-align: middle;
        }
      }

      .card-horizontal-gen-ai-footer-count {
        font-size: 14px;
        font-weight: 400;
        font-family: 'Mulish';
        width: 26px;
        height: 20px;
        color: #292C3D;
        opacity: 60%;
      }
    }
  }
}

.horizontal-gen-ai-container{
  display: flex;
}


