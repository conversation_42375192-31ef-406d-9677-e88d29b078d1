import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';
import { CardsComponent, CardSize, CardTheme, CardVariant, CardAnimation, CardAnimationTrigger } from './cards.component';
import { IconsComponent } from '../icons/icons.component';
import { CommonModule } from '@angular/common';

describe('CardsComponent', () => {
  let component: CardsComponent;
  let fixture: ComponentFixture<CardsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CardsComponent, IconsComponent, CommonModule]
    }).compileComponents();

    fixture = TestBed.createComponent(CardsComponent);
    component = fixture.componentInstance;
  });

  describe('Component Initialization', () => {
    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should have default input values', () => {
      expect(component.size).toBe('medium');
      expect(component.theme).toBe('light');
      expect(component.variant).toBe('basic');
      expect(component.animationTrigger).toBe('hover');
      expect(component.imagePosition).toBe('right');
      expect(component.hasHeader).toBe(false);
      expect(component.hasFooter).toBe(false);
      expect(component.elevated).toBe(false);
      expect(component.leftIcons).toEqual([]);
      expect(component.platformBadgeBgColor).toBe('#F6F9B6');
      expect(component.platformBadgeTextColor).toBe('#805300');
      expect(component.handleIconClick).toBeNull();
    });
  });

  describe('Card Classes Generation', () => {
    it('should generate correct classes for default configuration', () => {
      const classes = component.cardClasses;
      expect(classes['awe-card']).toBe(true);
      expect(classes['awe-card--medium']).toBe(true);
      expect(classes['awe-card--light']).toBe(true);
      expect(classes['awe-card--basic']).toBe(true);
      expect(classes['awe-card--elevated']).toBe(false);
      expect(classes['awe-card--has-header']).toBe(false);
      expect(classes['awe-card--has-footer']).toBe(false);
      expect(classes['awe-card--has-image']).toBe(false);
    });

    it('should apply size classes correctly', () => {
      const sizes: CardSize[] = ['small', 'medium', 'large', 'auto'];
      
      sizes.forEach(size => {
        component.size = size;
        const classes = component.cardClasses;
        expect(classes[`awe-card--${size}`]).toBe(true);
      });
    });

    it('should apply theme classes correctly', () => {
      const themes: CardTheme[] = ['light', 'dark', 'eldewand-studio', 'elderwand-agent'];
      
      themes.forEach(theme => {
        component.theme = theme;
        const classes = component.cardClasses;
        expect(classes[`awe-card--${theme}`]).toBe(true);
      });
    });

    it('should apply variant classes correctly', () => {
      const variants: CardVariant[] = ['basic', 'feature', 'info', 'elderwand-studio', 'elderwand-agent', 'feature-post'];
      
      variants.forEach(variant => {
        component.variant = variant;
        const classes = component.cardClasses;
        expect(classes[`awe-card--${variant}`]).toBe(true);
      });
    });

    it('should apply elevated class when elevated is true', () => {
      component.elevated = true;
      const classes = component.cardClasses;
      expect(classes['awe-card--elevated']).toBe(true);
    });

    it('should apply header/footer classes when has flags are true', () => {
      component.hasHeader = true;
      component.hasFooter = true;
      const classes = component.cardClasses;
      expect(classes['awe-card--has-header']).toBe(true);
      expect(classes['awe-card--has-footer']).toBe(true);
    });

    it('should apply image classes when image is provided', () => {
      component.image = 'test-image.jpg';
      component.imagePosition = 'left';
      const classes = component.cardClasses;
      expect(classes['awe-card--has-image']).toBe(true);
      expect(classes['awe-card--image-left']).toBe(true);
    });

    it('should apply animation classes when animation is configured', () => {
      component.animation = 'fadeIn';
      const classes = component.cardClasses;
      expect(classes['awe-card--fadeIn']).toBe(true);
    });
  });

  describe('Animation Behavior', () => {
    it('should return false for isAnimated by default', () => {
      expect(component.isAnimated).toBe(false);
    });

    it('should handle hover animation trigger', () => {
      component.animationTrigger = 'hover';
      component.onMouseEnter();
      expect(component.isAnimated).toBe(true);
      
      component.onMouseLeave();
      expect(component.isAnimated).toBe(false);
    });

    it('should handle click animation trigger', () => {
      component.animationTrigger = 'click';
      component.onClick();
      expect(component.isAnimated).toBe(true);
      
      component.onClick();
      expect(component.isAnimated).toBe(false);
    });

    it('should not respond to hover events when trigger is click', () => {
      component.animationTrigger = 'click';
      component.onMouseEnter();
      expect(component.isAnimated).toBe(false);
    });

    it('should not respond to click events when trigger is hover', () => {
      component.animationTrigger = 'hover';
      component.onClick();
      expect(component.isAnimated).toBe(false);
    });

    it('should apply animated class when animation is active', () => {
      component.animation = 'zoomIn';
      component.animationTrigger = 'hover';
      component.onMouseEnter();
      
      const classes = component.cardClasses;
      expect(classes['awe-card--animated']).toBe(true);
    });
  });

  describe('DOM Rendering - Basic Structure', () => {
    it('should render main card container with correct attributes', () => {
      fixture.detectChanges();
      
      const cardElement = fixture.debugElement.query(By.css('.awe-card'));
      expect(cardElement).toBeTruthy();
      expect(cardElement.nativeElement.getAttribute('role')).toBe('article');
      expect(cardElement.nativeElement.getAttribute('tabindex')).toBe('0');
    });

    it('should apply CSS classes to card container', () => {
      component.size = 'large';
      component.theme = 'dark';
      component.elevated = true;
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.awe-card'));
      expect(cardElement.nativeElement).toHaveClass('awe-card--large');
      expect(cardElement.nativeElement).toHaveClass('awe-card--dark');
      expect(cardElement.nativeElement).toHaveClass('awe-card--elevated');
    });
  });

  describe('Header and Footer Rendering', () => {
    it('should not render header when hasHeader is false', () => {
      component.hasHeader = false;
      fixture.detectChanges();

      const headerElement = fixture.debugElement.query(By.css('.awe-card__header'));
      expect(headerElement).toBeNull();
    });

    it('should render header when hasHeader is true', () => {
      component.hasHeader = true;
      fixture.detectChanges();

      const headerElement = fixture.debugElement.query(By.css('.awe-card__header'));
      expect(headerElement).toBeTruthy();
    });

    it('should not render footer when hasFooter is false', () => {
      component.hasFooter = false;
      fixture.detectChanges();

      const footerElement = fixture.debugElement.query(By.css('.awe-card__footer'));
      expect(footerElement).toBeNull();
    });

    it('should render footer when hasFooter is true', () => {
      component.hasFooter = true;
      fixture.detectChanges();

      const footerElement = fixture.debugElement.query(By.css('.awe-card__footer'));
      expect(footerElement).toBeTruthy();
    });
  });

  describe('Feature Post Variant', () => {
    beforeEach(() => {
      component.variant = 'feature-post';
    });

    it('should render feature post content for feature-post variant', () => {
      fixture.detectChanges();

      const featurePostContent = fixture.debugElement.query(By.css('.feature-post-content'));
      expect(featurePostContent).toBeTruthy();
    });

    it('should render top image when imageTop is provided', () => {
      component.imageTop = 'feature-image.jpg';
      component.title = 'Test Title';
      fixture.detectChanges();

      const featureImage = fixture.debugElement.query(By.css('.feature-post-image img'));
      expect(featureImage).toBeTruthy();
      expect(featureImage.nativeElement.src).toContain('feature-image.jpg');
      expect(featureImage.nativeElement.alt).toBe('Test Title');
    });

    it('should render top label when provided', () => {
      component.imageTop = 'feature-image.jpg';
      component.topLabel = 'Breaking News';
      fixture.detectChanges();

      const topLabel = fixture.debugElement.query(By.css('.top-label'));
      expect(topLabel).toBeTruthy();
      expect(topLabel.nativeElement.textContent.trim()).toBe('Breaking News');
    });

    it('should render title and timestamp', () => {
      component.title = 'Feature Post Title';
      component.timestamp = '2023-12-01';
      fixture.detectChanges();

      const title = fixture.debugElement.query(By.css('.feature-post-title'));
      const timestamp = fixture.debugElement.query(By.css('.timestamp'));
      
      expect(title).toBeTruthy();
      expect(title.nativeElement.textContent.trim()).toBe('Feature Post Title');
      expect(timestamp).toBeTruthy();
      expect(timestamp.nativeElement.textContent.trim()).toBe('2023-12-01');
    });

    it('should render subtitle and note when provided', () => {
      component.subtitle = 'Feature Subtitle';
      component.note = 'Feature Note';
      fixture.detectChanges();

      const subtitle = fixture.debugElement.query(By.css('.feature-post-subtitle'));
      const note = fixture.debugElement.query(By.css('.feature-note'));
      
      expect(subtitle).toBeTruthy();
      expect(subtitle.nativeElement.textContent.trim()).toBe('Feature Subtitle');
      expect(note).toBeTruthy();
      expect(note.nativeElement.textContent.trim()).toBe('Feature Note');
    });

    it('should not render regular card text content for feature-post variant', () => {
      fixture.detectChanges();

      const cardText = fixture.debugElement.query(By.css('.awe-card__text'));
      expect(cardText).toBeNull();
    });

    it('should use default alt text when title is not provided', () => {
      component.imageTop = 'feature-image.jpg';
      fixture.detectChanges();

      const featureImage = fixture.debugElement.query(By.css('.feature-post-image img'));
      expect(featureImage.nativeElement.alt).toBe('Feature post image');
    });
  });

  describe('Elderwand Agent Variant', () => {
    beforeEach(() => {
      component.variant = 'elderwand-agent';
    });

    it('should render title-rating wrapper for elderwand-agent variant', () => {
      component.title = 'Agent Title';
      component.rating = 4.5;
      fixture.detectChanges();

      const titleRatingWrapper = fixture.debugElement.query(By.css('.title-rating-wrapper'));
      expect(titleRatingWrapper).toBeTruthy();
    });

    it('should not render title-rating wrapper when title is not provided', () => {
      fixture.detectChanges();

      const titleRatingWrapper = fixture.debugElement.query(By.css('.title-rating-wrapper'));
      expect(titleRatingWrapper).toBeNull();
    });

    it('should render rating display with star icon', () => {
      component.title = 'Agent Title';
      component.rating = 4.5;
      fixture.detectChanges();

      const ratingDisplay = fixture.debugElement.query(By.css('.rating-display'));
      const ratingValue = fixture.debugElement.query(By.css('.rating-value'));
      const starIcon = fixture.debugElement.query(By.css('.star-icon awe-icons'));

      expect(ratingDisplay).toBeTruthy();
      expect(ratingValue.nativeElement.textContent.trim()).toBe('4.5');
      expect(starIcon).toBeTruthy();
      expect(starIcon.componentInstance.iconName).toBe('star');
    });

    it('should use custom rating icon when provided', () => {
      component.title = 'Agent Title';
      component.rating = 4.5;
      component.ratingIconName = 'custom-star';
      fixture.detectChanges();

      const starIcon = fixture.debugElement.query(By.css('.star-icon awe-icons'));
      expect(starIcon.componentInstance.iconName).toBe('custom-star');
    });

    it('should render user info section when userCount is provided', () => {
      component.userCount = 150;
      fixture.detectChanges();

      const userInfo = fixture.debugElement.query(By.css('.user-info'));
      const userCount = fixture.debugElement.query(By.css('.user-count'));
      const userIcon = fixture.debugElement.query(By.css('.user-icon awe-icons'));

      expect(userInfo).toBeTruthy();
      expect(userCount.nativeElement.textContent.trim()).toBe('150');
      expect(userIcon).toBeTruthy();
      expect(userIcon.componentInstance.iconName).toBe('user');
    });

    it('should use custom user icon when provided', () => {
      component.userCount = 150;
      component.userIconName = 'custom-user';
      fixture.detectChanges();

      const userIcon = fixture.debugElement.query(By.css('.user-icon awe-icons'));
      expect(userIcon.componentInstance.iconName).toBe('custom-user');
    });
  });

  describe('Default Card Content', () => {
    it('should render default title for non-elderwand-agent variants', () => {
      component.variant = 'basic';
      component.title = 'Card Title';
      fixture.detectChanges();

      const title = fixture.debugElement.query(By.css('.awe-card__title'));
      expect(title).toBeTruthy();
      expect(title.nativeElement.textContent.trim()).toBe('Card Title');
    });

    it('should render subtitle when provided', () => {
      component.subtitle = 'Card Subtitle';
      fixture.detectChanges();

      const subtitle = fixture.debugElement.query(By.css('.awe-card__subtitle'));
      expect(subtitle).toBeTruthy();
      expect(subtitle.nativeElement.textContent.trim()).toBe('Card Subtitle');
    });

    it('should render card text section for non-feature-post variants', () => {
      component.variant = 'basic';
      fixture.detectChanges();

      const cardText = fixture.debugElement.query(By.css('.awe-card__text'));
      expect(cardText).toBeTruthy();
    });
  });

  describe('Left Icons', () => {
    it('should not render icon section when leftIcons is empty', () => {
      component.leftIcons = [];
      fixture.detectChanges();

      const iconSection = fixture.debugElement.query(By.css('.icon-section'));
      expect(iconSection).toBeNull();
    });

    it('should render icon section when leftIcons has items', () => {
      component.leftIcons = ['heart', 'share'];
      fixture.detectChanges();

      const iconSection = fixture.debugElement.query(By.css('.icon-section'));
      expect(iconSection).toBeTruthy();
    });

    it('should render correct number of icons', () => {
      component.leftIcons = ['heart', 'share', 'bookmark'];
      fixture.detectChanges();

      const icons = fixture.debugElement.queryAll(By.css('.icon-section awe-icons'));
      expect(icons.length).toBe(3);
    });

    it('should pass correct icon names to icon components', () => {
      component.leftIcons = ['heart', 'share'];
      fixture.detectChanges();

      const icons = fixture.debugElement.queryAll(By.css('.icon-section awe-icons'));
      expect(icons[0].componentInstance.iconName).toBe('heart');
      expect(icons[1].componentInstance.iconName).toBe('share');
    });

    it('should apply correct CSS classes to icons', () => {
      component.leftIcons = ['heart'];
      fixture.detectChanges();

      const icon = fixture.debugElement.query(By.css('.icon-section awe-icons'));
      expect(icon.nativeElement).toHaveClass('icon-wrapper');
      expect(icon.nativeElement).toHaveClass('circular-icon');
    });

    it('should call handleIconClick when icon is clicked and handler is provided', () => {
      const mockHandler = jasmine.createSpy('handleIconClick');
      component.leftIcons = ['heart'];
      component.handleIconClick = mockHandler;
      fixture.detectChanges();

      const icon = fixture.debugElement.query(By.css('.icon-section awe-icons'));
      icon.nativeElement.click();

      expect(mockHandler).toHaveBeenCalledWith('heart');
    });

    it('should not error when icon is clicked and handler is null', () => {
      component.leftIcons = ['heart'];
      component.handleIconClick = null;
      fixture.detectChanges();

      const icon = fixture.debugElement.query(By.css('.icon-section awe-icons'));
      
      expect(() => {
        icon.nativeElement.click();
      }).not.toThrow();
    });
  });

  describe('Media/Image Rendering', () => {
    it('should not render media section when image is not provided', () => {
      component.image = undefined;
      fixture.detectChanges();

      const mediaSection = fixture.debugElement.query(By.css('.awe-card__media'));
      expect(mediaSection).toBeNull();
    });

    it('should render media section when image is provided', () => {
      component.image = 'card-image.jpg';
      fixture.detectChanges();

      const mediaSection = fixture.debugElement.query(By.css('.awe-card__media'));
      const imageElement = fixture.debugElement.query(By.css('.awe-card__image'));

      expect(mediaSection).toBeTruthy();
      expect(imageElement).toBeTruthy();
      expect(imageElement.nativeElement.src).toContain('card-image.jpg');
    });

    it('should use title as alt text when available', () => {
      component.image = 'card-image.jpg';
      component.title = 'Card Title';
      fixture.detectChanges();

      const imageElement = fixture.debugElement.query(By.css('.awe-card__image'));
      expect(imageElement.nativeElement.alt).toBe('Card Title');
    });

    it('should use default alt text when title is not available', () => {
      component.image = 'card-image.jpg';
      fixture.detectChanges();

      const imageElement = fixture.debugElement.query(By.css('.awe-card__image'));
      expect(imageElement.nativeElement.alt).toBe('Card image');
    });
  });

  describe('Event Handling', () => {
    it('should handle mouse enter event', () => {
      spyOn(component, 'onMouseEnter');
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.awe-card'));
      cardElement.nativeElement.dispatchEvent(new Event('mouseenter'));

      expect(component.onMouseEnter).toHaveBeenCalled();
    });

    it('should handle mouse leave event', () => {
      spyOn(component, 'onMouseLeave');
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.awe-card'));
      cardElement.nativeElement.dispatchEvent(new Event('mouseleave'));

      expect(component.onMouseLeave).toHaveBeenCalled();
    });

    it('should handle click event', () => {
      spyOn(component, 'onClick');
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.awe-card'));
      cardElement.nativeElement.click();

      expect(component.onClick).toHaveBeenCalled();
    });
  });

  describe('Complex Scenarios', () => {
    it('should handle multiple variants and configurations together', () => {
      component.variant = 'elderwand-agent';
      component.title = 'AI Agent';
      component.rating = 4.8;
      component.userCount = 250;
      component.platformName = 'Claude';
      component.leftIcons = ['heart', 'share'];
      component.image = 'agent-image.jpg';
      component.elevated = true;
      fixture.detectChanges();

      const titleRatingWrapper = fixture.debugElement.query(By.css('.title-rating-wrapper'));
      const userInfo = fixture.debugElement.query(By.css('.user-info'));
      const platformBadge = fixture.debugElement.query(By.css('.platform-badge'));
      const iconSection = fixture.debugElement.query(By.css('.icon-section'));
      const mediaSection = fixture.debugElement.query(By.css('.awe-card__media'));

      expect(titleRatingWrapper).toBeTruthy();
      expect(userInfo).toBeTruthy();
      expect(platformBadge).toBeTruthy();
      expect(iconSection).toBeTruthy();
      expect(mediaSection).toBeTruthy();
    });

    it('should handle feature-post variant with all content', () => {
      component.variant = 'feature-post';
      component.imageTop = 'feature.jpg';
      component.topLabel = 'Featured';
      component.title = 'Feature Title';
      component.timestamp = '2023-12-01';
      component.subtitle = 'Feature Subtitle';
      component.note = 'Feature Note';
      fixture.detectChanges();

      const featureImage = fixture.debugElement.query(By.css('.feature-post-image'));
      const topLabel = fixture.debugElement.query(By.css('.top-label'));
      const title = fixture.debugElement.query(By.css('.feature-post-title'));
      const timestamp = fixture.debugElement.query(By.css('.timestamp'));
      const subtitle = fixture.debugElement.query(By.css('.feature-post-subtitle'));
      const note = fixture.debugElement.query(By.css('.feature-note'));

      expect(featureImage).toBeTruthy();
      expect(topLabel).toBeTruthy();
      expect(title).toBeTruthy();
      expect(timestamp).toBeTruthy();
      expect(subtitle).toBeTruthy();
      expect(note).toBeTruthy();
    });

    it('should maintain accessibility attributes', () => {
      fixture.detectChanges();

      const cardElement = fixture.debugElement.query(By.css('.awe-card'));
      expect(cardElement.nativeElement.getAttribute('role')).toBe('article');
      expect(cardElement.nativeElement.getAttribute('tabindex')).toBe('0');
    });
  });

  describe('Content Projection', () => {
    let element: HTMLElement;

    beforeEach(() => {
      element = fixture.nativeElement;
      fixture.detectChanges();
    });

    it('should project content into default content area', () => {
      const contentContainer = document.createElement('div');
      contentContainer.setAttribute('content', '');
      contentContainer.textContent = 'Test Content';
      fixture.componentRef.location.nativeElement.appendChild(contentContainer);
      fixture.detectChanges();

      expect(element.textContent).toContain('Test Content');
    });

    it('should project header content when hasHeader is true', () => {
      component.hasHeader = true;
      const headerContainer = document.createElement('div');
      headerContainer.setAttribute('header', '');
      headerContainer.textContent = 'Test Header';
      fixture.componentRef.location.nativeElement.appendChild(headerContainer);
      fixture.detectChanges();

      expect(element.textContent).toContain('Test Header');
    });

    it('should project footer content when hasFooter is true', () => {
      component.hasFooter = true;
      const footerContainer = document.createElement('div');
      footerContainer.setAttribute('footer', '');
      footerContainer.textContent = 'Test Footer';
      fixture.componentRef.location.nativeElement.appendChild(footerContainer);
      fixture.detectChanges();

      expect(element.textContent).toContain('Test Footer');
    });
  });

  });

