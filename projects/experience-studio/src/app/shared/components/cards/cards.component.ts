import { CommonModule } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { IconsComponent } from '../icons/icons.component';

export type CardSize = 'small' | 'medium' | 'large' | 'auto';
export type CardTheme = 'light' | 'dark' | 'eldewand-studio' | 'elderwand-agent';
export type CardVariant = 'basic' | 'feature' | 'info' | 'elderwand-studio' | 'elderwand-agent' | 'feature-post'|'gen-ai-learning-path'|'card-horizontal-gen-ai';
export type CardAnimation = 'fadeIn' | 'zoomIn' | 'flip';
export type CardAnimationTrigger = 'hover' | 'click';

@Component({
  selector: 'awe-cards',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  templateUrl: './cards.component.html',
  styleUrls: ['./cards.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class CardsComponent {
  @Input() size: CardSize = 'medium';
  @Input() theme: CardTheme = 'light';
  @Input() variant: CardVariant = 'basic';
  @Input() animation?: CardAnimation;
  @Input() animationTrigger: CardAnimationTrigger = 'hover';
  @Input() image?: string;
  @Input() imageTop?: string;
  @Input() timestamp?: string;
  @Input() note?: string;
  @Input() topLabel?: string;
  @Input() imagePosition: 'left' | 'right' = 'right';
  @Input() hasHeader = false;
  @Input() hasFooter = false;
  @Input() title?: string;
  @Input() subtitle?: string;
  @Input() elevated = false;
  @Input() leftIcons: string[] = [];
  @Input() rating?: number;
  @Input() platformName?: string;
  @Input() userCount?: number;
  @Input() userIconName?: string;
  @Input() ratingIconName?: string;
  @Input() platformIconName?: string;
  @Input() platformBadgeBgColor: string = '#F6F9B6';
  @Input() platformBadgeTextColor: string = '#805300';
  @Input() handleIconClick: ((icon: string) => void) | null = null;
  @Input() readMoreText?: string;
@Input() footerImage?: string;
@Input() footerImageAlt?: string;
@Input() footerName?: string;
@Input() footerIcon?: string;
@Input() footerCount?: number;


  private _isHovered = false;
  private _isClicked = false;

  get isAnimated(): boolean {
    if (this.animationTrigger === 'hover') {
      return this._isHovered;
    }
    return this._isClicked;
  }

  get cardClasses(): { [key: string]: boolean } {
    return {
      'awe-card': true,
      [`awe-card--${this.size}`]: true,
      [`awe-card--${this.theme}`]: true,
      [`awe-card--${this.variant}`]: true,
      'awe-card--elevated': this.elevated,
      'awe-card--has-header': this.hasHeader,
      'awe-card--has-footer': this.hasFooter,
      'awe-card--has-image': !!this.image,
      [`awe-card--image-${this.imagePosition}`]: !!this.image,
      [`awe-card--${this.animation}`]: !!this.animation,
      'awe-card--animated': this.isAnimated,
    };
  }

  onMouseEnter(): void {
    if (this.animationTrigger === 'hover') {
      this._isHovered = true;
    }
  }

  onMouseLeave(): void {
    if (this.animationTrigger === 'hover') {
      this._isHovered = false;
    }
  }

  onClick(): void {
    if (this.animationTrigger === 'click') {
      this._isClicked = !this._isClicked;
    }
  }
}
