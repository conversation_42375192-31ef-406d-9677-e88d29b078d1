import { Component, ElementRef, Input, OnInit, OnDestroy, ViewChild, ChangeDetectionStrategy, ChangeDetectorRef, DestroyRef, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ThemeService } from '../../services/theme-service/theme.service';
import { FileTreePersistenceService } from '../../services/file-tree-persistence.service';
import { FileContentCacheService } from '../../services/cache/file-content-cache.service';
import { CacheMetricsService } from '../../services/cache/cache-metrics.service';
import { FileOpeningService } from '../../services/file-opening.service';
import { IconsComponent } from '../icons/icons.component';
import { debounceTime, distinctUntilChanged, Subject } from 'rxjs';

// Import the new CodeEditorComponent and its required types
import { CodeEditorComponent, CodeLanguage, CodeEditorTheme } from '@shared/components/code-editor/code-editor.component';

// Interfaces remain the same
export interface FileModel {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileModel[];
  expanded?: boolean;
  fileName?: string;
  path?: string;
}

export interface FileInput {
  fileName: string;
  content: string;
}

export interface SearchMatch {
  lineNumber: number;
  lineContent: string;
  matchStart: number;
  matchEnd: number;
  matchText: string;
}

export interface FileSearchResult {
  file: FileModel;
  matches: SearchMatch[];
  totalMatches: number;
  expanded?: boolean;
}

@Component({
  selector: 'app-code-viewer',
  standalone: true,
  // Ensure CodeEditorComponent is imported
  imports: [CommonModule, FormsModule, IconsComponent, CodeEditorComponent],
  templateUrl: './code-viewer.component.html',
  styleUrl: './code-viewer.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CodeViewerComponent implements OnInit, OnDestroy {
  // ViewChild to get a reference to the new CodeEditorComponent instance
  @ViewChild(CodeEditorComponent) private codeEditor?: CodeEditorComponent;
  @Input() showFileExplorer = true;

  currentTheme: 'light' | 'dark';

  activeFiles: FileModel[] = [];
  activeFile: FileModel | null = null;
  selectedFilePath: string | null = null;
  isLoading: boolean = true;
  searchQuery: string = '';
  showWelcomeMessage: boolean = true;

  fileSearchOptions = {
    caseSensitive: false,
    wholeWord: false
  };

  searchResults: FileSearchResult[] = [];
  totalSearchMatches = 0;
  totalSearchFiles = 0;

  private searchSubject = new Subject<string>();
  private _files: FileModel[] = [];
  private _originalFiles: FileModel[] = [];

  // The @Input setter for files remains largely the same
  @Input() set files(value: FileModel[] | FileInput[]) {
    this.isLoading = true;
    this.showWelcomeMessage = !value || (Array.isArray(value) && value.length === 0);

    if (!value || !Array.isArray(value) || value.length === 0) {
      this._originalFiles = [];
      this._files = [];
      this.isLoading = false;
      return;
    }

    const firstItem = value[0];
    let fileModels: FileModel[] = [];

    if ('type' in firstItem && firstItem.type === 'file') {
      fileModels = value as FileModel[];
    } else if ('fileName' in firstItem && 'content' in firstItem) {
      fileModels = this.transformFilesToFileModels(value as FileInput[]);
    } else if ('path' in firstItem && 'code' in firstItem) {
      fileModels = this.transformFileDataToFileModels(value as any[]);
    } else {
      fileModels = this.transformFilesToFileModels(value as FileInput[]);
    }

    const preservedFileModels = this.preserveExistingContent(fileModels);
    this._originalFiles = this.buildFileTree(preservedFileModels);
    this._files = [...this._originalFiles];
    this.warmCacheWithFiles(preservedFileModels);
    this.selectFirstFileIfAvailable();
    this.isLoading = false;
  }

  get files(): FileModel[] {
    return this._files;
  }

  @Input() theme: 'light' | 'dark' = 'dark';

  // No longer need a direct editor instance here, it's managed by the child component
  private editor: any = null;

  // Injected services
  private readonly fileContentCache = inject(FileContentCacheService);
  private readonly cacheMetrics = inject(CacheMetricsService);
  private readonly fileOpeningService = inject(FileOpeningService);
  private readonly destroyRef = inject(DestroyRef);
  private readonly themeService = inject(ThemeService);
  private readonly fileTreePersistenceService = inject(FileTreePersistenceService);
  private readonly cdr = inject(ChangeDetectorRef);

  constructor() {
    this.currentTheme = this.themeService.getCurrentTheme();
    this.setupFileOpeningSubscription();
  }

  ngOnInit(): void {
    this.themeService.themeObservable
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(theme => {
        this.currentTheme = theme;
        this.cdr.markForCheck();
      });

    this.searchSubject
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(searchTerm => {
        this.performSearch(searchTerm);
      });
  }

  ngOnDestroy(): void {
    this.saveAllCurrentContent();
  }

  /**
   * NEW: Handler for when the child CodeEditorComponent is ready.
   * We save the editor instance to use for advanced features like search navigation.
   */
  onEditorReady(editorInstance: any): void {
    this.editor = editorInstance;
    this.selectFirstFileIfAvailable();
  }

  /**
   * NEW: Handler for content changes from the CodeEditorComponent.
   * Updates the content of the currently active file.
   */
  onContentChanged(newContent: string): void {
    if (this.activeFile) {
      this.activeFile.content = newContent;
      // Also update the corresponding file in the main tree structure
      this.updateFileContentInTree(this.activeFile.name || this.activeFile.fileName || '', newContent);
    }
  }

  selectFile(file: FileModel): void {
    if (file.type !== 'file') return;

    this.selectedFilePath = file.name;

    const existingFileIndex = this.activeFiles.findIndex(f => f.name === file.name);
    if (existingFileIndex !== -1) {
      this.setActiveFile(this.activeFiles[existingFileIndex]);
      return;
    }

    // Save current file's content before switching
    if (this.activeFile) {
        this.saveCurrentContent();
    }

    this.activeFiles.push(file);
    this.setActiveFile(file);
    this.showWelcomeMessage = false;
    this.cdr.markForCheck();
  }

  setActiveFile(file: FileModel): void {
    this.activeFile = file;
    this.cdr.markForCheck();
  }

  closeTab(file: FileModel): void {
    const index = this.activeFiles.findIndex(f => f.name === file.name);
    if (index === -1) return;

    // Save content before closing
    if (this.activeFile === file) {
        this.saveCurrentContent();
    }

    this.activeFiles.splice(index, 1);

    if (this.activeFile === file) {
      if (this.activeFiles.length > 0) {
        const newIndex = Math.max(0, index - 1);
        this.setActiveFile(this.activeFiles[newIndex]);
      } else {
        this.activeFile = null;
        this.showWelcomeMessage = true;
      }
    }
    this.cdr.markForCheck();
  }

  // Helper to get the language for the code editor component
  getFileLanguage(fileName: string | undefined): CodeLanguage {
    if (!fileName) return 'plaintext';
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
        case 'py': return 'python';
        case 'js': return 'javascript';
        case 'ts': case 'tsx': return 'typescript';
        case 'json': return 'json';
        case 'sql': return 'sql';
        case 'html': return 'html';
        case 'css': case 'scss': return 'css';
        case 'md': return 'markdown';
        case 'yml': case 'yaml': return 'yaml';
        case 'xml': return 'xml';
        default: return 'plaintext';
    }
  }

  navigateToMatch(file: FileModel, match: SearchMatch): void {
    this.selectFile(file);
    // Use a timeout to ensure the editor has switched its content before highlighting
    setTimeout(() => {
      this.highlightMatchInEditor(match);
    }, 100);
  }

  private highlightMatchInEditor(match: SearchMatch): void {
    // This logic remains the same, as it relies on the editor instance
    if (!this.editor) return;

    this.editor.revealLineInCenter(match.lineNumber);
    const selection = {
      startLineNumber: match.lineNumber,
      startColumn: match.matchStart + 1,
      endLineNumber: match.lineNumber,
      endColumn: match.matchEnd + 1
    };
    this.editor.setSelection(selection);
    this.editor.focus();

    // Add temporary highlighting
    const decorations = this.editor.deltaDecorations([], [{
      range: new this.editor.monaco.Range(selection.startLineNumber, selection.startColumn, selection.endLineNumber, selection.endColumn),
      options: { className: 'search-match-highlight', isWholeLine: false }
    }]);

    setTimeout(() => {
      this.editor.deltaDecorations(decorations, []);
    }, 3000);
  }

  public saveCurrentContent(): void {
    if (this.activeFile && this.codeEditor) {
        const currentContent = this.codeEditor.getValue();
        this.activeFile.content = currentContent;
        this.updateFileContentInTree(this.activeFile.name || this.activeFile.fileName || '', currentContent);
    }
  }

  // Most of the file handling, searching, and tree-building logic below this line
  // remains unchanged as it is not directly related to the editor instance itself.
  // ... (All other methods like buildFileTree, findFirstFile, searchInFileContents, etc., remain here)

  // --- Start of unchanged methods ---

  private setupFileOpeningSubscription(): void {
    this.fileOpeningService.fileOpenRequests.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(request => {
      this.openFileByPath(request.filePath);
    });
  }

  private warmCacheWithFiles(files: FileModel[]): void {
    const cacheFiles = files
      .filter(file => file.type === 'file' && file.content)
      .map(file => ({
        filePath: file.name || file.fileName || '',
        content: typeof file.content === 'string' ? file.content : JSON.stringify(file.content, null, 2),
        lastModified: Date.now(),
        metadata: {
          language: this.getFileLanguage(file.name || file.fileName || ''),
          type: file.type
        }
      }));

    if (cacheFiles.length > 0) {
      this.fileContentCache.batchCacheFiles(cacheFiles);
      if (cacheFiles.length > 0) {
        this.fileContentCache.preloadRelatedFiles(cacheFiles[0].filePath, cacheFiles);
      }
    }
  }

  private selectFirstFileIfAvailable(): void {
    if (this.editor && this._files.length > 0) {
      const firstFile = this.findFirstFile(this._files);
      if (firstFile) {
        this.selectFile(firstFile);
        this.showWelcomeMessage = false;
      }
    }
  }

  private findFirstFile(files: FileModel[]): FileModel | null {
    const appFile = this.findAppTsxFile(files);
    if (appFile) return appFile;

    const firstFile = files.find(f => f.type === 'file');
    if (firstFile) return firstFile;

    for (const folder of files.filter(f => f.type === 'folder' && f.children)) {
      const fileInFolder = this.findFirstFile(folder.children!);
      if (fileInFolder) return fileInFolder;
    }
    return null;
  }

  private findAppTsxFile(files: FileModel[]): FileModel | null {
    if (!files || files.length === 0) return null;
    const appFilePatterns = [
      { pattern: /^app\.tsx$/i, priority: 1 }, { pattern: /^app\.jsx$/i, priority: 2 },
      { pattern: /^main\.tsx$/i, priority: 5 }, { pattern: /^main\.jsx$/i, priority: 6 },
      { pattern: /^index\.tsx$/i, priority: 7 }, { pattern: /^index\.jsx$/i, priority: 8 },
    ];
    const foundFiles: Array<{ file: FileModel; priority: number; path: string }> = [];
    const searchFiles = (fileList: FileModel[], currentPath: string = ''): void => {
      for (const file of fileList) {
        const fullPath = currentPath ? `${currentPath}/${file.name}` : file.name || '';
        if (file.type === 'file') {
          for (const { pattern, priority } of appFilePatterns) {
            if (pattern.test(file.name || '')) {
              foundFiles.push({ file, priority, path: fullPath });
              break;
            }
          }
        } else if (file.type === 'folder' && file.children) {
          searchFiles(file.children, fullPath);
        }
      }
    };
    searchFiles(files);
    if (foundFiles.length === 0) return null;
    foundFiles.sort((a, b) => a.priority - b.priority);
    const srcFiles = foundFiles.filter(f => f.path.toLowerCase().includes('/src/') || f.path.toLowerCase().startsWith('src/'));
    return (srcFiles.length > 0 ? srcFiles[0] : foundFiles[0]).file;
  }

  getFileIcon(fileName: string): string {
    if (!fileName) return 'awe_txt';
    if (fileName.endsWith('.component.ts')) return 'awe_angular';
    if (fileName.endsWith('.component.html')) return 'awe_html';
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'ts': return 'awe_react_ts';
      case 'tsx': return 'awe_react_ic';
      case 'jsx': return 'awe_react_ic';
      case 'js': return 'awe_js';
      case 'html': return 'awe_html';
      case 'css': return 'awe_css';
      case 'scss': return 'awe_scss';
      case 'json': return 'awe_json';
      case 'md': return 'awe_md';
      default: return 'awe_txt';
    }
  }

  openFileByPath(filePath: string): void {
    if (!filePath) return;
    const normalizedPath = this.fileOpeningService.normalizeFilePath(filePath);
    const file = this.findFileByPath(this._files, normalizedPath);
    if (file) {
      this.selectFile(file);
    }
  }

  toggleFolder(folder: FileModel): void {
    folder.expanded = !folder.expanded;
    this.cdr.markForCheck();
  }

  private findFileByName(files: FileModel[], fileName: string): FileModel | null {
    const normalizedSearchPath = this.normalizeFilePath(fileName);
    for (const file of files) {
      if (file.type === 'file') {
        const normalizedFilePath = this.normalizeFilePath(file.name || file.fileName || '');
        if (normalizedFilePath === normalizedSearchPath) return file;
      }
      if (file.type === 'folder' && file.children) {
        const found = this.findFileByName(file.children, fileName);
        if (found) return found;
      }
    }
    return null;
  }

  private normalizeFilePath(filePath: string): string {
    if (!filePath) return '';
    return filePath.replace(/\\/g, '/').replace(/^\/+/, '').replace(/\/+$/, '').toLowerCase();
  }

  private saveAllCurrentContent(): void {
      if (this.activeFile && this.codeEditor) {
          this.onContentChanged(this.codeEditor.getValue());
      }
  }

  private updateFileContentInTree(fileName: string, content: string): void {
    const file = this.findFileByName(this._files, fileName);
    if (file) {
      file.content = content;
    }
  }

  private preserveExistingContent(newFileModels: FileModel[]): FileModel[] {
    if (this.activeFiles.length === 0) {
      return newFileModels;
    }
    // Create a map of existing file content from active files
    const contentMap = new Map<string, string>();
    this.activeFiles.forEach(af => {
        if (af.name && af.content) {
            contentMap.set(this.normalizeFilePath(af.name), af.content);
        }
    });

    return newFileModels.map(newFile => {
      const normalizedPath = this.normalizeFilePath(newFile.name || newFile.fileName || '');
      if (contentMap.has(normalizedPath)) {
        return {
          ...newFile,
          content: contentMap.get(normalizedPath)
        };
      }
      return newFile;
    });
  }

  replaceAllFiles(newFiles: FileModel[] | FileInput[]): void {
    this._originalFiles = [];
    this._files = [];
    this.activeFiles = [];
    this.activeFile = null;
    this.showWelcomeMessage = true;
    this.files = newFiles;
  }

  private transformFilesToFileModels(files: FileInput[]): FileModel[] {
    if (!files || !Array.isArray(files)) return [];
    return files.map(file => ({ name: file.fileName, type: 'file', content: file.content }));
  }

  private transformFileDataToFileModels(files: any[]): FileModel[] {
    if (!files || !Array.isArray(files)) return [];
    return files.map(file => ({
      name: file.path || file.fileName || 'Unknown file',
      type: 'file',
      content: file.code || file.content || '',
      fileName: file.path || file.fileName
    }));
  }

  private buildFileTree(files: FileModel[]): FileModel[] {
    const root: FileModel = { name: 'root', type: 'folder', children: [], expanded: true };
    if (!files || !Array.isArray(files)) return [];

    files.forEach(file => {
      if (!file || typeof file.name !== 'string') return;
      const pathParts = file.name.replace(/\\/g, '/').split('/').filter(p => p);
      let currentFolder = root;
      pathParts.forEach((part, index) => {
        if (index === pathParts.length - 1) {
          if (!currentFolder.children?.find(c => c.type === 'file' && c.name === part)) {
            currentFolder.children?.push({
              name: part, type: 'file', content: file.content, fileName: file.name
            });
          }
        } else {
          let nextFolder = currentFolder.children?.find(c => c.type === 'folder' && c.name === part);
          if (!nextFolder) {
            nextFolder = { name: part, type: 'folder', children: [], expanded: index < 2 };
            currentFolder.children?.push(nextFolder);
          }
          currentFolder = nextFolder;
        }
      });
    });
    this.sortFileTreeRecursively(root);
    return root.children || [];
  }

  private sortFileTreeRecursively(folder: FileModel): void {
    if (!folder.children) return;
    folder.children.sort((a, b) => {
      if (a.type !== b.type) return a.type === 'folder' ? -1 : 1;
      return (a.name || '').localeCompare(b.name || '');
    });
    folder.children.forEach(child => {
      if (child.type === 'folder') this.sortFileTreeRecursively(child);
    });
  }

  public filterFiles(): void {
    this.searchSubject.next(this.searchQuery);
  }

  private performSearch(searchQuery: string): void {
    if (!searchQuery) {
      this.searchResults = [];
      this.totalSearchMatches = 0;
      this.totalSearchFiles = 0;
      this.cdr.markForCheck();
      return;
    }
    this.searchResults = this.searchInFileContents(searchQuery);
    this.totalSearchMatches = this.searchResults.reduce((total, result) => total + result.totalMatches, 0);
    this.totalSearchFiles = this.searchResults.length;
    this.cdr.markForCheck();
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private searchInFileContents(searchQuery: string): FileSearchResult[] {
    const results: FileSearchResult[] = [];
    const allFiles = this.getAllFiles(this._originalFiles);
    for (const file of allFiles) {
      if (file.type !== 'file' || !file.content) continue;
      const matches = this.findMatchesInContent(file.content, searchQuery);
      if (matches.length > 0) {
        results.push({
          file, matches, totalMatches: matches.length, expanded: true
        });
      }
    }
    return results;
  }

  private findMatchesInContent(content: string, searchQuery: string): SearchMatch[] {
    const matches: SearchMatch[] = [];
    const lines = content.split('\n');
    const searchTerm = this.fileSearchOptions.caseSensitive ? searchQuery : searchQuery.toLowerCase();

    lines.forEach((line, lineIndex) => {
      const lineContent = this.fileSearchOptions.caseSensitive ? line : line.toLowerCase();
      if (this.fileSearchOptions.wholeWord) {
        const regex = new RegExp(`\\b${this.escapeRegExp(searchTerm)}\\b`, this.fileSearchOptions.caseSensitive ? 'g' : 'gi');
        let match;
        while ((match = regex.exec(line)) !== null) {
          matches.push({
            lineNumber: lineIndex + 1, lineContent: line, matchStart: match.index,
            matchEnd: match.index + match[0].length, matchText: match[0]
          });
        }
      } else {
        let startIndex = 0;
        let matchIndex;
        while ((matchIndex = lineContent.indexOf(searchTerm, startIndex)) !== -1) {
          matches.push({
            lineNumber: lineIndex + 1, lineContent: line, matchStart: matchIndex,
            matchEnd: matchIndex + searchTerm.length, matchText: line.substring(matchIndex, matchIndex + searchTerm.length)
          });
          startIndex = matchIndex + 1;
        }
      }
    });
    return matches;
  }

  private getAllFiles(files: FileModel[]): FileModel[] {
    const allFiles: FileModel[] = [];
    const extractFiles = (fileList: FileModel[]) => {
      for (const file of fileList) {
        if (file.type === 'file') allFiles.push(file);
        else if (file.type === 'folder' && file.children) extractFiles(file.children);
      }
    };
    extractFiles(files);
    return allFiles;
  }

  toggleFileSearchOption(option: keyof typeof this.fileSearchOptions): void {
    this.fileSearchOptions[option] = !this.fileSearchOptions[option];
    if (this.searchQuery) this.filterFiles();
  }

  clearFileSearch(): void {
    this.searchQuery = '';
    this.searchResults = [];
    this.totalSearchMatches = 0;
    this.totalSearchFiles = 0;
    this.cdr.markForCheck();
  }

  onFileSearchKeydown(event: KeyboardEvent): void {
    if (event.key === 'Escape') this.clearFileSearch();
  }

  getFileSearchResultsText(): string {
    if (this.totalSearchMatches === 0) return 'No results found';
    return `${this.totalSearchMatches} results in ${this.totalSearchFiles} files`;
  }

  getSearchResultsClass(): string {
    return this.totalSearchMatches === 0 ? 'no-results' : 'has-results';
  }

  toggleSearchResult(result: FileSearchResult): void {
    result.expanded = !result.expanded;
    this.cdr.markForCheck();
  }

  onImageError(event: Event): void {
    const img = event.target as HTMLImageElement;
    if (img) img.src = 'assets/icons/awe_txt.svg';
  }

  private findFileByPath(files: FileModel[], filePath: string): FileModel | null {
    if (!files || !filePath) return null;
    for (const file of files) {
      const normalizedFileName = this.normalizeFilePath(file.fileName || file.name || '');
      if (file.type === 'file' && normalizedFileName === filePath) {
        return file;
      }
      if (file.type === 'folder' && file.children) {
        const found = this.findFileByPath(file.children, filePath);
        if (found) return found;
      }
    }
    return null;
  }

  // Methods required by code-window component
  refreshOpenFiles(): void {
    // Refresh the currently open files
    this.cdr.markForCheck();
  }

  getAllCurrentFiles(): FileModel[] {
    // Return all currently loaded files
    return this._files;
  }
}
