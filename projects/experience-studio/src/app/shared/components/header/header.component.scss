@import '../../styles/grid.scss';

.outer-box {
  width: 100%;
  height: var(--header-height, var(--spacing-14x));
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  transition: all 0.3s ease;
  position: relative;

  &.light {
    background-color: var(--text-white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  &.dark {
    background-color: var(--spinner-text-color);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

   &.transparent {
    background-color: transparent;
    color: inherit; // or specify a color that works well with transparency
  }
  .container {
    width: 100%;
    max-width: 100%;
    // padding: 0 var(--spacing-3x);
    position: relative;
  }

  .row {
    margin: 0;
    width: 100%;
    position: relative;
  }

  // Center content wrapper
  .center-content-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  // Center content
  .center-content {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }

  // Mobile styles (default)
  .col-12 {
    display: flex;
    align-items: center;
    justify-content: center;
    // padding: var(--spacing-2x) 0;
    text-align: center;
  }

  // Tablet styles (md)
  @media (min-width: 768px) {
    // .container {
      // padding: 0 var(--spacing-3x);
    // }

    .col-md-2 {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      // padding: 0 var(--spacing-2x);
    }

    .col-md-8 {
      display: flex;
      align-items: center;
      justify-content: center;
      // padding: 0 var(--spacing-2x);
    }
  }

  // Desktop styles (lg)
  @media (min-width: 992px) {
    // .container {
      // padding: 0 var(--spacing-4x);
    // }

    .col-lg-auto {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      // padding: 0 var(--spacing-3x);
    }

    .col-lg {
      display: flex;
      align-items: center;
      justify-content: center;
      // padding: 0 var(--spacing-3x);
    }
  }

  // Ensure content alignment
  :host ::ng-deep {
    >* {
      margin: 0;
    }
  }
}

.right-section .avatar-container img {
  border-radius: var(--3x);
}

.right-section {
  display: flex;
  align-items: center;
  // gap: var(--spacing-5x);
  justify-content: flex-end;
  // padding-right: var(--spacing-3x);
}

/* Ensure child elements are displayed inline */
.right-section>* {
  display: flex;
  align-items: center;
  // gap: var(--spacing-6x)
}

.right-section {
  display: flex;
  align-items: center;
  // gap: var(--spacing-4x);
}

.inner-box {
  width: 100%;
  height: var(--spacing-14x);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
}

.left-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  // gap: var(--spacing-5x);
}

.center-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.right-section {
  display: flex;
  align-items: center;
  // gap: var(--spacing-5x);
  justify-content: flex-end;
  // padding-right: var(--spacing-3x);
}

.center-section>* {
  display: flex;
  align-items: center;
  // gap: var(--spacing-6x)
}

.left-section>* {
  display: flex;
  align-items: center;
  // gap: var(--spacing-6x)
}