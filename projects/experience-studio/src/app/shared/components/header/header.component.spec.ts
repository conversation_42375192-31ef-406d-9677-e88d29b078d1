import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HeaderComponent } from './header.component';

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HeaderComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default theme as light', () => {
    expect(component.theme).toBe('light');
  });

  it('should set theme correctly', () => {
    component.theme = 'dark';
    expect(component.theme).toBe('dark');

    component.theme = 'transparent';
    expect(component.theme).toBe('transparent');
  });

  it('should set container class correctly', () => {
    const testClass = 'test-class';
    component.containerClass = testClass;
    expect(component.containerClass).toBe(testClass);
  });
});