import { ChangeDetectionStrategy, Component, Input, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'awe-header',
  imports: [CommonModule],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  encapsulation: ViewEncapsulation.None,
})
export class HeaderComponent {
  @Input() theme: 'light' | 'dark' | 'transparent' = 'light';
  // @Input() height: string = 'var(--spacing-14x)';
  @Input() containerClass: string = '';
}
