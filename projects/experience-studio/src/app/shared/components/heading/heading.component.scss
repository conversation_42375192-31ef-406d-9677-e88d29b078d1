.heading {
  margin: 0;
  padding: 0;
  font-weight: var(--font-font-weight-regular);
  line-height: var(--font-line-height-heading-regular);

  &.display {
    font-size: var(--font-font-size-display);
    line-height: var(--font-line-height-display-large); 
    font-family: var(--font-font-family-display);
  }

  &.s1 {
    font-size: var(--font-font-size-subtitle-1); 
    line-height: var(--font-line-height-subtitle-large); 
    font-family: var(--font-font-family-subtitle);
  }

  &.s2 {
    font-size: var(--font-font-size-subtitle-2); 
    line-height: var(--font-line-height-subtitle-medium); 
    font-family: var(--font-font-family-subtitle);
  }

  &.h1 {
    font-size: var(--font-font-size-heading-1); 
    line-height: var(--font-line-height-heading-xl); 
    font-family: var(--font-font-family-heading);
  }

  &.h2 {
    font-size: var(--font-font-size-heading-2); 
    line-height: var(--font-line-height-heading-l); 
    font-family: var(--font-font-family-heading);
  }

  &.h3 {
    font-size: var(--font-font-size-heading-3); 
    line-height: var(--font-line-height-heading-m); 
    font-family: var(--font-font-family-heading);
  }

  &.h4 {
    font-size: var(--font-font-size-heading-4); 
    line-height: var(--font-line-height-heading-r); 
    font-family: var(--font-font-family-heading);
  }
  &.h5 {
    font-size: var(--font-font-size-heading-5); 
    line-height: var(--font-line-height-heading-s); 
    font-family: var(--font-font-family-heading);
  }
  &.h6 {
    font-size: var(--font-font-size-heading-6); 
    line-height: var(--font-line-height-heading-xs); 
    font-family: var(--font-font-family-heading);
  }

  &.bold {
    font-weight: var(--font-font-weight-bold); 
  }

  &.regular {
    font-weight: var(--font-font-weight-regular); 
  }


}
