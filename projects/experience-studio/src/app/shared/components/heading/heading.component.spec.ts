import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HeadingComponent, Variant, Type } from './heading.component';
import { Component } from '@angular/core';

@Component({
  selector: 'test-host',
  template: `<awe-heading variant="h1" type="bold"></awe-heading>`,
  standalone: true,
  imports: [HeadingComponent],
})
class TestHostComponent {
  variant: Variant = 'h1';
  type: Type = 'regular';
}

describe('HeadingComponent', () => {
  let component: HeadingComponent;
  let fixture: ComponentFixture<HeadingComponent>;
  let hostFixture: ComponentFixture<TestHostComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestHostComponent], 
    }).compileComponents();

    fixture = TestBed.createComponent(HeadingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    hostFixture = TestBed.createComponent(TestHostComponent);
    hostFixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should apply "h1" and "regular" classes', () => {
    const hostComponent = hostFixture.componentInstance;
    const headingElement = hostFixture.nativeElement.querySelector('p');

    hostComponent.variant = 'h1';
    hostComponent.type = 'regular';
    hostFixture.detectChanges();

    expect(headingElement.classList.contains('heading')).toBeTrue();
    expect(headingElement.classList.contains('h1')).toBeTrue();
    expect(headingElement.classList.contains('regular')).toBeFalse();
  });

  it('should apply "h2" and "bold" classes', () => {
    const hostComponent = hostFixture.componentInstance;
    const headingElement = hostFixture.nativeElement.querySelector('p');

    
    hostComponent.variant = 'h2';
    hostComponent.type = 'bold';
    hostFixture.detectChanges();

    expect(headingElement.classList.contains('h2')).toBeFalse();
    expect(headingElement.classList.contains('bold')).toBeTrue();
  });

  it('should apply "s1" and "regular" classes', () => {
    const hostComponent = hostFixture.componentInstance;
    const headingElement = hostFixture.nativeElement.querySelector('p');

    hostComponent.variant = 's1';
    hostComponent.type = 'regular';
    hostFixture.detectChanges();

    expect(headingElement.classList.contains('s1')).toBeFalse();
    expect(headingElement.classList.contains('regular')).toBeFalse();
  });

  it('should apply "display" and "bold" classes', () => {
    const hostComponent = hostFixture.componentInstance;
    const headingElement = hostFixture.nativeElement.querySelector('p');

    hostComponent.variant = 'display';
    hostComponent.type = 'bold';
    hostFixture.detectChanges();

    expect(headingElement.classList.contains('display')).toBeFalse();
    expect(headingElement.classList.contains('bold')).toBeTrue();
  });
});
