import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

export type Variant = 'display' | 's1' | 's2' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
export type Type = 'regular' | 'bold';

@Component({
  selector: 'awe-heading',
  imports: [CommonModule],
  templateUrl: './heading.component.html',
  styleUrl: './heading.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HeadingComponent {
  @Input() variant: Variant = 'h1';
  @Input() type: Type = 'regular';
}
