<div class="prompt-bar" [ngClass]="[theme, variantClass]">
  <!-- Hidden file input -->
  <input
    #fileInput
    type="file"
    style="display: none"
    accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"
    (change)="onFileSelected($event)"
  />

  <!-- File Error Message -->
  <div class="error-message" *ngIf="fileError">{{ fileError }}</div>

  <!-- Accessible Textarea -->
  <textarea
    class="prompt-text"
    [(ngModel)]="text"
    [placeholder]="placeholderText"
    (ngModelChange)="onTextChange()"
    (keydown)="handleKeyDown($event)"
    role="textbox"
    [attr.aria-placeholder]="defaultText"
    aria-multiline="true"
  >
  </textarea>

  <!-- Selected Files Display -->
  <div class="selected-files" *ngIf="selectedFiles.length > 0">
    <div class="file-item" *ngFor="let file of selectedFiles">
      <div class="file-preview" (click)="showFilePreview(file)">
        <img [src]="file.url" [alt]="file.name" />
        <span class="file-name">{{ truncateFileName(file.name) }}</span>
      </div>
      <exp-icons
        iconName="awe_close"
        (click)="removeFile(file.id)"
        role="button"
        tabindex="0"
        iconColor="blue"
        [attr.aria-label]="'Remove ' + file.name"
      ></exp-icons>
    </div>
  </div>

  <!-- Icons Container -->
  <div class="prompt-icons-container">
    <!-- Left Icons -->
    <div class="icon-group left">
      <ng-container *ngFor="let icon of leftIcons; let i = index">
        <exp-icons
          [iconName]="icon.name"
          [iconColor]="getIconColor(icon)"
          (click)="onIconClick('left', i)"
          (keydown.enter)="onIconClick('left', i)"
          tabindex="0"
          role="button"
          [attr.aria-disabled]="icon.status === 'disable'"
          [attr.aria-label]="
            'Left icon: ' +
            icon.name +
            (icon.status === 'disable' ? ' (disabled)' : '')
          "
        >
        </exp-icons>
      </ng-container>
    </div>

    <!-- Right Icons -->
    <div class="icon-group right">
      <ng-container *ngFor="let icon of rightIcons; let i = index">
        <exp-icons
          [iconName]="icon.name"
          [iconColor]="getIconColor(icon)"
          (click)="onIconClick('right', i)"
          (keydown.enter)="onIconClick('right', i)"
          [disabled]="!text"
          tabindex="0"
          role="button"
          [attr.aria-label]="'Right icon: ' + icon.name"
        >
        </exp-icons>
      </ng-container>
    </div>
  </div>

  <!-- Custom Content Area -->
  <ng-content></ng-content>
</div>

<!-- Image Preview Overlay -->
<div class="preview-overlay" *ngIf="showPreview" (click)="closePreview()">
  <div class="preview-content" (click)="$event.stopPropagation()">
    <div class="preview-header">
      <span class="preview-title">{{ previewFile?.name }}</span>
      <exp-icons
        iconName="awe_cross"
        (click)="closePreview()"
        role="button"
        tabindex="0"
        aria-label="Close preview"
      ></exp-icons>
    </div>
    <div class="preview-body">
      <img [src]="previewFile?.url" [alt]="previewFile?.name" />
    </div>
  </div>
</div>
