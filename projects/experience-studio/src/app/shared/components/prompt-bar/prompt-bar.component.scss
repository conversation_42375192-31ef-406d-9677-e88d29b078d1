.prompt-bar {
  display: flex;
  flex-direction: column;
  width: var(--prompt-enhance-width);
  padding: var(--spacing-3x);
  border-radius: var(--4x);
  border: var(--prompt-border-spacing1px) solid var(--spinner-text-color);
  background: var(--surface);
  backdrop-filter: blur(var(--backdrop-blur));
  &.chat-bot {
    width: 100%;
  }
  .prompt-text {
    font-family: var(--font-font-family-heading);
    font-size: var(--font-font-size-body);
    font-weight: var(--font-font-weight-semi-bold);
    line-height: var(--font-line-height-subtitle-medium);
    color: var(--text-subtitle);
    width: 100%;
    height: auto;
    min-height: var(--prompt-enhance-min-height);
    max-height: var(--prompt-enhance-max-height);
    resize: none;
    overflow-y: auto;
    border: none;
    background: transparent;
    outline: none;
    padding: var(--spacing-3x);

    &::placeholder {
      color: var(--text-black);
    }

    &:focus {
      min-height: var(--prompt-enhance-min-height);
      max-height: var(--prompt-enhance-max-height);
    }

    /* Custom scrollbar styles for light theme */
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: var(--surface-light);
    }

    &::-webkit-scrollbar-thumb {
      background-color: var(--text-subtitle);
      border-radius: 10px;
      border: 2px solid var(--surface-light);
    }
  }

  .prompt-icons-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-2x);
  }

  .icon-group {
    display: flex;
    gap: var(--spacing-4x);
    cursor: pointer;

    &.left {
      justify-content: flex-start;
    }

    &.right {
      justify-content: flex-end;
    }

    exp-icons {
      &[aria-disabled="true"] {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }

  .selected-files {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2x);
    padding: var(--spacing-2x);
    margin-top: var(--spacing-2x); // Changed from margin-bottom to margin-top

    .file-item {
      position: relative;
      display: flex;
      align-items: center;
      background: var(--surface-light);
      border-radius: var(--3x);
      padding: var(--spacing-1x);
      border: 1px solid var(--input-border-color);
      transition: border-color 0.3s ease;

      .file-preview {
        display: flex;
        align-items: center;
        gap: var(--spacing-2x);
        cursor: pointer;
        padding: var(--spacing-2x);
        padding-right: var(--spacing-4x);

        img {
          width: 40px;
          height: 40px;
          object-fit: cover;
          border-radius: var(--2x);
        }

        .file-name {
          font-size: var(--font-font-size-small);
          color: var(--text-subtitle);
          max-width: 150px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: inline-block;
          max-width: 15ch; // Limit to roughly 15 characters
        }
      }

      exp-icons {
        right: var(--spacing-1x);
        cursor: pointer;
        color: var(--text-subtitle); // Default color for light theme
        transition: color 0.3s ease;

        &:hover {
          color: var(--danger);
        }
      }
    }
  }

  // Dark theme overrides
  &.dark {
    border: var(--1x) solid var(--button-primary-bg-disable);
    background: var(--footer-bg-dark);

    .prompt-text {
      color: var(--text-white);

      &::placeholder {
        color: var(--text-white);
      }

      /* Custom scrollbar styles for dark theme */
      &::-webkit-scrollbar {
        width: 8px;
      }

      &::-webkit-scrollbar-track {
        background: var(--surface-dark);
      }

      &::-webkit-scrollbar-thumb {
        background-color: var(--text-white);
        border-radius: 10px;
        border: 2px solid var(--surface-dark);
      }
    }

    .selected-files {
      .file-item {
        background: var(--surface-dark);
        border-color: var(--text-white); // White border for dark theme

        .file-name {
          color: var(--text-white);
        }

        exp-icons {
          color: var(--text-white); // White color for close icon in dark theme

          &:hover {
            color: var(--danger);
          }
        }
      }
    }
  }

  // Tablet and smaller screens (max-width: 768px)
  @media (max-width: 768px) {
    width: 500px;
    padding: var(--spacing-3x);

    .prompt-text {
      font-size: var(--font-font-size-small); // Decreased text size
      padding: var(--spacing-2x);
    }

    .icon-group {
      gap: var(--spacing-2x);

      exp-icons {
        transform: scale(0.9); // Decreased icon size
        svg {
          width: 21px; // Set SVG icon size to 19px
          height: 21px;
        }
      }
    }
  }

  // Mobile and smaller screens (max-width: 480px)
  @media (max-width: 480px) {
    width: 300px;
    padding: var(--spacing-2x);
    border-radius: var(--3x);

    .prompt-text {
      font-size: var(--font-font-size-label); // Decreased text size
      line-height: var(--font-line-height-body-small);
    }

    .icon-group {
      flex-direction: row;
      gap: var(--spacing-1x);

      exp-icons {
        transform: scale(0.8); // Decreased icon size
        svg {
          width: 19px; // Set SVG icon size to 19px
          height: 19px;
        }
      }
    }
  }

  .enhance-icons {
    display: flex;
    align-items: center;
    gap: var(--spacing-2x);

    exp-icons {
      cursor: pointer;
      transition: all 0.3s ease;

      &[iconName="awe_undo"] {
        color: var(--text-subtitle);

        &:hover {
          color: var(--action-primary-hover);
        }
      }
    }
  }
}

// Preview overlay styles
.preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .preview-content {
    background: var(--surface);
    border-radius: var(--2x);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: var(--shadow-large);

    .preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--spacing-3x);
      border-bottom: 1px solid var(--border-color);

      .preview-title {
        font-size: var(--font-font-size-body);
        font-weight: var(--font-font-weight-semi-bold);
        color: var(--text-subtitle);
      }

      exp-icons {
        cursor: pointer;
        &:hover {
          color: var(--danger);
        }
      }
    }

    .preview-body {
      padding: var(--spacing-3x);
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        max-width: 100%;
        max-height: 70vh;
        object-fit: contain;
      }
    }
  }
}

.error-message {
  color: red;
  font-size: 12px;
  margin-top: 4px;
}

// Design System Selection Container Styles
.design-system-container {
  display: flex;
  height: 112px;
  padding: 24px 16px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: var(--2x, 4px);
  background: var(--Light---60, rgba(240, 240, 245, 0.50));
  backdrop-filter: blur(45px);
  margin: var(--spacing-3x) 0;

  .design-system-option {
    display: flex;
    width: 320px;
    padding: 8px;
    align-items: center;
    gap: 24px;
    flex-shrink: 0;
    border-radius: var(--3x, 8px);
    border: 1px solid var(--Dark---30, rgba(20, 27, 31, 0.12));
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: var(--Light---70, rgba(240, 240, 245, 0.70));
    }

    exp-icons {
      width: 48px;
      height: 48px;
      flex-shrink: 0;
      aspect-ratio: 1/1;
    }

    .option-title {
      color: var(--Text-Subtitle, #292C3D);
      font-family: Mulish;
      font-size: 18px;
      font-style: normal;
      font-weight: 600;
      line-height: 27px;
    }
  }

}

 .device-icons-container {
  display: flex;
  padding: 2px;
  justify-content: center;
  align-items: center;
  gap: 12px;
  border-radius: var(--2x, 4px);
  border: 1px solid rgba(0, 0, 0, 0.20);
  background: var(--Light---30, rgba(240, 240, 245, 0.25));
  margin-left: 8px; /* Add some spacing between the pills container and this new container */
}