import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { IconsComponent } from '../icons/icons.component';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

@Component({
  selector: 'exp-prompt-bar',
  standalone: true,
  imports: [IconsComponent, CommonModule, FormsModule],
  templateUrl: './prompt-bar.component.html',
  styleUrls: ['./prompt-bar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class PromptBarComponent implements OnInit, OnDestroy {

  @Input() defaultText = '';
  @Input() leftIcons: {
    name: string;
    status: 'active' | 'default' | 'disable';
  }[] = [];
  @Input() rightIcons: {
    name: string;
    status: 'active' | 'default' | 'disable';
  }[] = [];
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() animatedTexts: string[] = [];
  @Input() staticText = '';
  @Input() variant: 'default' | 'chat-bot' = 'default';
  @Input() set textValue(value: string) {
    if (value !== this._textValue) {
      this._textValue = value;
      this.text = value;
      this.cdr.detectChanges();
    }
  }
  get textValue(): string {
    return this._textValue;
  }
  private _textValue = '';

  @Output() textValueChange = new EventEmitter<string>();

  @Output() iconClicked = new EventEmitter<{
    name: string;
    side: string;
    index: number;
  }>();

  text = '';
  private animationInterval: number | undefined;
  placeholderText = '';
  errorMessage = '';

  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  selectedFiles: SelectedFile[] = [];
  showPreview = false;
  previewFile: SelectedFile | null = null;


  // Add event emitters for file operations
  @Output() filesSelected = new EventEmitter<File[]>();
  @Output() fileRemoved = new EventEmitter<string>();
  @Output() filePreviewClosed = new EventEmitter<void>();

  // Add new properties for file validation
  readonly acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  readonly maxAllowedFiles = 1; // Maximum number of files allowed
  fileError = '';

  constructor(private cdr: ChangeDetectorRef) {}

  ngOnInit(): void {
    this.text = this.textValue || '';

    if (this.animatedTexts.length > 0) {
      this.startTextAnimation();
    } else {
      this.placeholderText = this.defaultText || this.staticText;
    }

    // Initialize the upload icon status
    this.updateUploadIconStatus();

    this.cdr.detectChanges();
  }
    get variantClass(): string {
    return this.variant === 'chat-bot' ? 'chat-bot' : '';
  }
  startTextAnimation(): void {
    let index = 0;
    const animate = () => {
      if (this.animatedTexts.length > 0) {
        this.placeholderText = `${this.staticText} ${this.animatedTexts[index]}`;
        index = (index + 1) % this.animatedTexts.length;
        this.cdr.detectChanges();
      }
      this.animationInterval = requestAnimationFrame(() =>
        setTimeout(animate, 2000)
      );
    };
    animate();
  }

  updatePlaceholder() {
    this.placeholderText = `${this.staticText} ${
      this.animatedTexts.length > 0 ? this.animatedTexts[0] : ''
    }`;
  }

  ngOnDestroy(): void {
    if (this.animationInterval) {
      cancelAnimationFrame(this.animationInterval);
    }
  }

  trackByFn(index: number, item: { name: string; status: string }) {
    return `${index}-${item.name}`;
  }

  getIconColor(icon: {
    name: string;
    status?: string;
  }):
    | ''
    | 'disable'
    | 'action'
    | 'danger'
    | 'neutralIcon'
    | 'success'
    | 'warning'
    | 'whiteIcon'
    | 'blue' {
    if (icon.status === 'disable') {
      return 'disable';
    } else if (icon.status === 'active') {
      return this.text.trim().length > 0
        ? ''
        : this.theme === 'dark'
        ? 'blue'
        : '';
    } else if (icon.status === 'default') {
      return this.theme === 'dark' ? 'blue' : '';
    }
    return '';
  }

  onIconClick(side: 'left' | 'right', index: number): void {
    const clickedIcon =
      side === 'left' ? this.leftIcons[index] : this.rightIcons[index];

    // Only proceed if the icon is not disabled
    if (clickedIcon.status === 'disable') {
      return;
    }

    if (clickedIcon.name === 'awe_enhanced_alternate') {
      // Only allow file upload if max files not reached
      if (!this.isMaxFilesReached()) {
        this.handleEnhancedAlternate();
      } else {
        this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
        this.cdr.detectChanges();
      }
    } else {
      this.iconClicked.emit({ name: clickedIcon.name, side, index });
    }
  }

  onTextChange() {
    this.validateText();
    this.adjustTextareaHeight();
    this._textValue = this.text;
    this.textValueChange.emit(this.text);
    this.cdr.detectChanges();
  }

  validateText(): void {
    if (!this.text.trim()) {
      this.errorMessage = 'Input cannot be empty!';
    } else if (this.text.length > 2000) {
      this.errorMessage = 'Input exceeds the maximum allowed length!';
    } else {
      this.errorMessage = '';
    }
  }

  // Modify the adjustTextareaHeight method to reset properly
// Modify the adjustTextareaHeight method to synchronize all prompt bars
adjustTextareaHeight(): void {
  const textAreas = document.querySelectorAll('.prompt-text');

  // First determine the maximum required height
  let maxHeight = 0;
  textAreas.forEach((textArea) => {
    const htmlTextArea = textArea as HTMLTextAreaElement;
    if (htmlTextArea) {
      // Temporarily set to auto to get the true scrollHeight
      const originalHeight = htmlTextArea.style.height;
      htmlTextArea.style.height = 'auto';
      maxHeight = Math.max(maxHeight, htmlTextArea.scrollHeight);
      // Reset to original height
      htmlTextArea.style.height = originalHeight;
    }
  });

  // Now apply the maximum height to all textareas
  textAreas.forEach((textArea) => {
    const htmlTextArea = textArea as HTMLTextAreaElement;
    if (htmlTextArea) {
      if (!this.text.trim()) {
        htmlTextArea.style.height = ''; // Reset to CSS default for empty text
      } else {
        htmlTextArea.style.height = `${maxHeight}px`;
      }
    }
  });

  this.cdr.detectChanges();
}

  @Output() enterPressed = new EventEmitter<void>();

  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Let it insert a newline
        return;
      } else {
        // Prevent default newline
        event.preventDefault();
        this.enterPressed.emit();
      }
    }
  }

  handleEnhancedAlternate(): void {
    this.fileInput.nativeElement.click();
  }

  validateFile(file: File): boolean {
    // Check if it's actually a file and not a folder
    if (!file.type) {
      this.fileError = 'Folders cannot be uploaded';
      return false;
    }

    // Check if it's an image file
    if (!this.acceptedImageTypes.includes(file.type)) {
      this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed';
      return false;
    }

    // Check file size (optional, set to 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      this.fileError = 'File size must be less than 5MB';
      return false;
    }

    this.fileError = '';
    return true;
  }

  // Check if maximum number of files has been reached
  isMaxFilesReached(): boolean {
    return this.selectedFiles.length >= this.maxAllowedFiles;
  }

  // Get the status of the upload icon based on file selection
  getUploadIconStatus(): 'default' | 'disable' {
    return this.isMaxFilesReached() ? 'disable' : 'default';
  }

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      // Check if max files already reached
      if (this.isMaxFilesReached()) {
        this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
        this.cdr.detectChanges();
        input.value = ''; // Reset input
        return;
      }

      const validFiles: File[] = [];

      Array.from(input.files).forEach(file => {
        if (this.validateFile(file)) {
          validFiles.push(file);
        }
      });

      if (validFiles.length > 0) {
        // Only take the first valid file if multiple are selected
        const filesToAdd = validFiles.slice(0, this.maxAllowedFiles - this.selectedFiles.length);

        const newFiles = filesToAdd.map(file => {
          const url = URL.createObjectURL(file);
          return {
            id: Math.random().toString(36).substr(2, 9),
            name: file.name,
            url: url,
            type: file.type
          };
        });

        this.selectedFiles = [...this.selectedFiles, ...newFiles];
        this.filesSelected.emit(filesToAdd);

        // Update the status of the upload icon in leftIcons array
        this.updateUploadIconStatus();
      }
      this.cdr.detectChanges();
    }
    input.value = ''; // Reset input
  }

  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    this.fileRemoved.emit(fileId);

    // Update the status of the upload icon in leftIcons array
    this.updateUploadIconStatus();

    this.cdr.detectChanges();
  }

  // Update the status of the upload icon in leftIcons array
  updateUploadIconStatus(): void {
    // Find the upload icon in the leftIcons array
    const uploadIconIndex = this.leftIcons.findIndex(icon => icon.name === 'awe_enhanced_alternate');

    if (uploadIconIndex !== -1) {
      // Update the status based on whether max files are reached
      this.leftIcons[uploadIconIndex].status = this.getUploadIconStatus();
    }
  }

  showFilePreview(file: SelectedFile): void {
    this.previewFile = file;
    this.showPreview = true;
    this.cdr.detectChanges();
  }

  closePreview(): void {
    this.showPreview = false;
    this.previewFile = null;
    this.filePreviewClosed.emit();
    this.cdr.detectChanges();
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const extension = filename.slice(filename.lastIndexOf('.'));
    const nameWithoutExt = filename.slice(0, filename.lastIndexOf(''));

    if (filename.length <= maxLength) {
      return filename;
    }

    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 1);
    return `${truncatedName}...${extension}`;
  }

}
