<!--
  The loader container.
  - role="status" and aria-live="polite" are for accessibility, informing screen readers that content is loading.
  - [ngStyle] binds dynamic styles for size, color, and animation duration.
-->
<div class="loader-container" [ngStyle]="getLoaderStyles()" role="status" aria-live="polite"
  aria-label="Loading content">
  <div class="loader-icon">
    <svg viewBox="0 0 16 16">
      <!--
              The 'g' element groups the SVG paths.
              [attr.clip-path] uses a unique ID to avoid conflicts when multiple loaders are rendered.
            -->
      <g [attr.clip-path]="'url(#' + clipPathId + ')'">
        <path d="M8 0V4" opacity="0.9" />
        <path d="M8 16V12" opacity="0.5" />
        <path d="M3.29773 1.52783L5.64887 4.7639" opacity="0.9" />
        <path d="M12.7023 1.52783L10.3511 4.7639" opacity="0.1" />
        <path d="M12.7023 14.472L10.3511 11.236" opacity="0.4" />
        <path d="M3.29773 14.472L5.64887 11.236" opacity="0.6" />
        <path d="M15.6085 5.52783L11.8043 6.7639" opacity="0.2" />
        <path d="M0.391602 10.472L4.19583 9.23598" opacity="0.7" />
        <path d="M15.6085 10.4722L11.8043 9.2361" opacity="0.3" />
        <path d="M0.391602 5.52783L4.19583 6.7639" opacity="0.8" />
      </g>
      <defs>
        <!--
                  Defines the clipPath. [attr.id] ensures a unique ID for each loader instance.
                  The clipPath is used to create the partial arcs of the loader.
                -->
        <clipPath [attr.id]="clipPathId">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </div>
</div>
