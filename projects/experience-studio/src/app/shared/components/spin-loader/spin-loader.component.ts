import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-spin-loader',
  imports: [CommonModule],
  standalone: true,
  templateUrl: './spin-loader.component.html',
  styleUrl: './spin-loader.component.scss'
})
export class SpinLoaderComponent {
  /** */
  //    * Defines the size (width and height) of the loader in pixels.
  //  * @default 12
  //  */
  @Input() size: number = 12;

  /**
   * Sets the color of the loader lines. Can be any valid CSS color string.
   * @default '#d3d3d3' (light grey)
   */
  @Input() color: string = '#d3d3d3';

  /**
   * Controls the duration of one full spin animation cycle in seconds.
   * @default 1
   */
  @Input() animationDuration: number = 1;

  // Unique ID for the SVG clip-path to prevent conflicts when multiple loaders are on the page
  clipPathId!: string;

  constructor() { }

  ngOnInit(): void {
    // Generate a unique ID for the clip-path. This ensures multiple loaders work correctly on one page.
    this.clipPathId = 'clip' + Math.random().toString(36).substring(2, 9);
  }

  /**
   * Returns dynamic CSS styles for the loader container based on component inputs.
   * This allows for customizable size, color, and animation duration.
   */
  getLoaderStyles() {
    return {
      'width.px': this.size,
      'height.px': this.size,
      'color': this.color,
      'animation-duration': `${this.animationDuration}s` // Dynamically set animation duration
    };
  }
}

