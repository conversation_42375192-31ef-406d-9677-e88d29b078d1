import { createLogger } from '../utils/logger';
import { Injectable, inject, signal, computed, DestroyRef, NgZone } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import Lenis from 'lenis';

/**
 * Lenis configuration interface for Experience Studio
 */
export interface LenisConfig {
  enabled?: boolean;
  lerp?: number;
  duration?: number;
  easing?: (t: number) => number;
  orientation?: 'vertical' | 'horizontal';
  gestureOrientation?: 'vertical' | 'horizontal' | 'both';
  smoothWheel?: boolean;
  wheelMultiplier?: number;
  touchMultiplier?: number;
  infinite?: boolean;
  autoResize?: boolean;
  prevent?: (node: Element) => boolean;
  virtualScroll?: (e: any) => boolean;
  overscroll?: boolean;
  autoRaf?: boolean;
}

/**
 * Lenis Smooth Scroll Service for Experience Studio
 * 
 * Provides smooth scrolling functionality using the Lenis library
 * with Angular 19+ patterns including signals, inject(), and proper lifecycle management.
 * 
 * Features:
 * - Automatic initialization and cleanup
 * - Route-aware scroll management
 * - Performance optimized configuration
 * - Integration with existing scroll components
 * - Theme-aware styling support
 */
@Injectable({
  providedIn: 'root'
})
export class LenisSmoothScrollService {
  // Angular 19+ Dependency Injection
  private readonly router = inject(Router);
  private readonly ngZone = inject(NgZone);
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('LenisSmoothScrollService');

  // Lenis instance and state management using signals
  private lenisInstance: Lenis | null = null;
  private rafId: number | null = null;

  // Reactive state using Angular 19+ signals
  private readonly isInitialized = signal<boolean>(false);
  private readonly isScrolling = signal<boolean>(false);
  private readonly scrollProgress = signal<number>(0);
  private readonly scrollDirection = signal<'up' | 'down'>('down');
  private readonly velocity = signal<number>(0);
  private readonly smoothness = signal<number>(0);
  private readonly frameRate = signal<number>(60);

  // Computed properties for external consumption
  public readonly state = computed(() => ({
    isInitialized: this.isInitialized(),
    isScrolling: this.isScrolling(),
    scrollProgress: this.scrollProgress(),
    scrollDirection: this.scrollDirection(),
    velocity: this.velocity(),
    smoothness: this.smoothness(),
    frameRate: this.frameRate()
  }));

  // Default configuration optimized for Experience Studio
  private readonly defaultConfig: LenisConfig = {
    enabled: true,
    lerp: 0.1, // Smooth interpolation
    duration: 1.2, // Animation duration in seconds
    easing: (t: number) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // Custom easing
    orientation: 'vertical',
    gestureOrientation: 'vertical',
    smoothWheel: true,
    wheelMultiplier: 1,
    touchMultiplier: 1,
    infinite: false,
    autoResize: true,
    overscroll: true,
    autoRaf: false, // We'll handle RAF manually for better control
    // Prevent smooth scrolling on specific elements
    prevent: (node: Element) => {
      // Preserve existing scroll behavior for chat windows and code editors
      return node.hasAttribute('data-lenis-prevent') ||
             node.classList.contains('chat-messages') ||
             node.classList.contains('monaco-editor') ||
             node.classList.contains('code-content') ||
             node.closest('.chat-messages') !== null ||
             node.closest('.monaco-editor') !== null ||
             node.closest('[data-lenis-prevent]') !== null;
    }
  };

  constructor() {
    this.setupRouterIntegration();
  }

  /**
   * Initialize Lenis with custom configuration
   */
  public initialize(config: Partial<LenisConfig> = {}): void {
    if (this.isInitialized()) {
      this.logger.warn('🔄 Lenis already initialized, destroying previous instance');
      this.destroy();
    }

    const mergedConfig = { ...this.defaultConfig, ...config };

    if (!mergedConfig.enabled) {
      this.logger.info('🚫 Lenis smooth scroll disabled via configuration');
      return;
    }

    try {
      // Run outside Angular zone for better performance
      this.ngZone.runOutsideAngular(() => {
        this.lenisInstance = new Lenis({
          lerp: mergedConfig.lerp,
          duration: mergedConfig.duration,
          easing: mergedConfig.easing,
          orientation: mergedConfig.orientation,
          gestureOrientation: mergedConfig.gestureOrientation,
          smoothWheel: mergedConfig.smoothWheel,
          wheelMultiplier: mergedConfig.wheelMultiplier,
          touchMultiplier: mergedConfig.touchMultiplier,
          infinite: mergedConfig.infinite,
          autoResize: mergedConfig.autoResize,
          prevent: mergedConfig.prevent,
          virtualScroll: mergedConfig.virtualScroll as any,
          overscroll: mergedConfig.overscroll,
          autoRaf: mergedConfig.autoRaf
        });

        this.setupEventListeners();
        this.startRafLoop();
      });

      this.isInitialized.set(true);
      this.logger.info('✅ Lenis smooth scroll initialized successfully');
    } catch (error) {
      this.logger.error('❌ Failed to initialize Lenis:', error);
    }
  }

  /**
   * Destroy Lenis instance and cleanup
   */
  public destroy(): void {
    if (this.lenisInstance) {
      this.lenisInstance.destroy();
      this.lenisInstance = null;
    }

    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
      this.rafId = null;
    }

    this.isInitialized.set(false);
    this.isScrolling.set(false);
    this.scrollProgress.set(0);
    this.velocity.set(0);

    this.logger.info('🧹 Lenis smooth scroll destroyed and cleaned up');
  }

  /**
   * Scroll to a specific target with options
   */
  public scrollTo(target: string | number | HTMLElement, options: {
    offset?: number;
    lerp?: number;
    duration?: number;
    easing?: (t: number) => number;
    immediate?: boolean;
    lock?: boolean;
    force?: boolean;
    onComplete?: () => void;
  } = {}): void {
    if (!this.lenisInstance) {
      this.logger.warn('⚠️ Cannot scroll: Lenis not initialized');
      return;
    }

    this.lenisInstance.scrollTo(target, options);
  }

  /**
   * Stop smooth scrolling
   */
  public stop(): void {
    if (this.lenisInstance) {
      this.lenisInstance.stop();
    }
  }

  /**
   * Resume smooth scrolling
   */
  public start(): void {
    if (this.lenisInstance) {
      this.lenisInstance.start();
    }
  }

  /**
   * Get current scroll position
   */
  public getScrollPosition(): number {
    return this.lenisInstance?.scroll || 0;
  }

  /**
   * Get scroll limit (maximum scroll value)
   */
  public getScrollLimit(): number {
    return this.lenisInstance?.limit || 0;
  }

  /**
   * Check if currently scrolling
   */
  public getIsScrolling(): boolean {
    if (!this.lenisInstance) return false;
    const scrolling = this.lenisInstance.isScrolling;
    return typeof scrolling === 'boolean' ? scrolling : Boolean(scrolling);
  }

  /**
   * Setup event listeners for Lenis
   */
  private setupEventListeners(): void {
    if (!this.lenisInstance) return;

    this.lenisInstance.on('scroll', (e: any) => {
      this.ngZone.run(() => {
        this.isScrolling.set(e.isScrolling);
        this.scrollProgress.set(e.progress);
        this.scrollDirection.set(e.direction > 0 ? 'down' : 'up');
        this.velocity.set(Math.abs(e.velocity));
      });
    });
  }

  /**
   * Start the optimized RAF loop for Lenis with performance monitoring
   */
  private startRafLoop(): void {
    let lastTime = performance.now();
    let frameCount = 0;
    let lastFpsUpdate = lastTime;
    let smoothnessBuffer: number[] = [];

    const raf = (time: number) => {
      if (this.lenisInstance) {
        // Calculate frame rate (update every 500ms for better performance)
        frameCount++;
        if (time - lastFpsUpdate >= 500) {
          const fps = Math.round((frameCount * 1000) / (time - lastFpsUpdate));
          this.ngZone.run(() => {
            this.frameRate.set(fps);
          });
          frameCount = 0;
          lastFpsUpdate = time;
        }

        // Calculate smoothness metric with rolling average
        const deltaTime = time - lastTime;
        const targetFrameTime = 16.67; // 60fps target
        const frameScore = Math.max(0, 100 - Math.abs(deltaTime - targetFrameTime) * 3);

        // Use rolling average for smoother metrics
        smoothnessBuffer.push(frameScore);
        if (smoothnessBuffer.length > 10) {
          smoothnessBuffer.shift();
        }

        const avgSmoothness = smoothnessBuffer.reduce((a, b) => a + b, 0) / smoothnessBuffer.length;

        // Update smoothness less frequently for better performance
        if (frameCount % 5 === 0) {
          this.ngZone.run(() => {
            this.smoothness.set(Math.round(avgSmoothness));
          });
        }

        this.lenisInstance.raf(time);
        lastTime = time;
        this.rafId = requestAnimationFrame(raf);
      }
    };
    this.rafId = requestAnimationFrame(raf);
  }

  /**
   * Setup router integration for scroll management
   */
  private setupRouterIntegration(): void {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        // Reset scroll position on route change
        setTimeout(() => {
          this.scrollTo(0, { immediate: true });
        }, 100);
      });
  }
}
