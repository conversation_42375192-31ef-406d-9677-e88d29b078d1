import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { AuthTokenService } from '@shared/auth/services/auth-token.service';
import { AuthConfig } from '@shared/auth/interfaces/auth-config.interface';
import { AuthService } from '@shared/auth/services/auth.service';
import { Router } from '@angular/router';
import { environment } from '../environments/environment';
import { CentralizedRedirectService } from '@shared/services/centralized-redirect.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'marketing';

  constructor(
    private authTokenService: AuthTokenService,
    private tokenStorage: TokenStorageService,
    private authService: AuthService,
    private router: Router,
    private centralizedRedirectService: CentralizedRedirectService,
  ) {
    // Set marketing app auth config immediately in constructor
    const authConfig: AuthConfig = {
      apiAuthUrl: environment.consoleApiAuthUrl,
      redirectUrl: `${window.location.origin}/`, // Marketing app root URL - works for Docker
      postLoginRedirectUrl: '/',
      appName: 'marketing',
    };
    this.authService.setAuthConfig(authConfig);
  }

  ngOnInit(): void {
    // Check for returnUrl parameter and store it as intended destination
    const urlParams = new URLSearchParams(window.location.search);
    const returnUrl = urlParams.get('returnUrl');
    if (returnUrl) {
      console.log('Found returnUrl parameter:', returnUrl);
      // Store it in sessionStorage and cookie for the centralized redirect service
      sessionStorage.setItem('intendedDestination', returnUrl);
      this.setCookie('intendedDestination', returnUrl); // 1 hour
    }

    // Check for authorization code from SSO and handle it first
    const authCode = urlParams.get('code');
    if (authCode) {
      console.log('Marketing app: Found authorization code, processing token exchange...');
      this.authService.exchangeCodeForToken(authCode).subscribe({
        next: (tokenPair) => {
          console.log('Marketing app: Token exchange successful:', tokenPair);
          // After successful token exchange, handle the redirect
          this.centralizedRedirectService.handlePostLoginRedirect();
        },
        error: (error) => {
          console.error('Marketing app: Token exchange failed:', error);
          // Redirect to login with error
          this.router.navigate(['/login'], {
            state: {
              error: 'Failed to complete authentication. Please try again.',
              errorType: 'sso_error'
            }
          });
        }
      });
      return; // Stop further processing until code is handled
    }

    // Start token monitoring
    this.authTokenService.startTokenCheck();
  }

  ngOnDestroy() {
    this.authTokenService.stopTokenCheck();
  }

  /**
   * Set a cookie with cross-app compatibility
   */
  private setCookie(name: string, value: string, days: number = 1): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    
    // Build cookie string with appropriate settings for different environments
    let cookieString = `${name}=${encodeURIComponent(value)};expires=${expires.toUTCString()};path=/`;
    
    // Add domain and security settings based on environment
    if (this.isLocalDevelopment()) {
      // For localhost development
      cookieString += ';SameSite=Lax';
    } else {
      // For production - add domain and security
      const domain = this.getCookieDomain();
      if (domain) {
        cookieString += `;domain=${domain}`;
      }
      cookieString += ';SameSite=Lax;Secure';
    }
    
    document.cookie = cookieString;
    console.log(`Marketing app: Cookie set: ${name}=${value} (${this.isLocalDevelopment() ? 'local' : 'production'})`);
  }

  /**
   * Check if we're running in local development mode
   */
  private isLocalDevelopment(): boolean {
    return window.location.port !== '' && 
           ['4200', '4201', '4202', '4203', '4204'].includes(window.location.port);
  }

  /**
   * Get appropriate cookie domain for current environment
   */
  private getCookieDomain(): string | null {
    const hostname = window.location.hostname;
    
    // For localhost, don't set domain
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      return null;
    }
    
    // For production, use the domain
    return hostname;
  }
} 