import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { routes } from './app.routes';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { AuthInterceptor } from '@shared/auth/interceptors/auth.interceptor';
import { ENVIRONMENT_CONFIG } from '@shared';
import { environment } from '../environments/environment';
import {
  LucideAngularModule,
  AlertCircle,
  X,
  Eye,
  EyeOff
} from 'lucide-angular';
import { MarkdownModule } from 'ngx-markdown';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(withInterceptorsFromDi()),
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true },
    {
      provide: ENVIRONMENT_CONFIG,
      useValue: {
        consoleApi: environment.consoleApi,
        consoleApiV2: environment.consoleApiV2,
        consoleEmbeddingApi: environment.consoleEmbeddingApi,
        consoleInstructionApi: environment.consoleInstructionApi,
        baseUrl: environment.baseUrl,
        apiUrl: environment.baseUrl,
      },
    },
    importProvidersFrom(
      LucideAngularModule.pick({
        AlertCircle,
        X,
        Eye,
        EyeOff
      }),
      MarkdownModule.forRoot(),
    ),
  ]
}; 