import { Routes } from '@angular/router';
import { AuthGuard } from '@shared/auth/guards/auth.guard';
import { LoginComponent } from '@shared/auth/components/login/login.component';
import { CallbackComponent } from '@shared/auth/components/callback/callback.component';

export const routes: Routes = [
  {
    path: '',
    loadComponent: () =>
      import('./pages/marketplace/marketplace.component').then(
        (m) => m.MarketplaceComponent,
      ),
  },
  { path: 'login', component: LoginComponent },
  { path: 'callback', component: CallbackComponent },
  {
    path: '**',
    redirectTo: '/',
  },
];
