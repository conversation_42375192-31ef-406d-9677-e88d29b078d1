<div class="marketplace-container">
  <!-- Hero Section using reusable component -->
  <app-hero-banner
    [title]="heroConfig.title"
    [brandText]="heroConfig.brandText"
    [subtitle]="heroConfig.subtitle"
    [description]="heroConfig.description"
    [imageSrc]="heroConfig.imageSrc"
    [imageAlt]="heroConfig.imageAlt"
    [actions]="heroConfig.actions"
    [statusMessage]="authStatusMessage"
    [showStatus]="showAuthStatus"
    [backgroundColor]="heroConfig.backgroundColor"
    [textColor]="heroConfig.textColor"
    [layout]="heroConfig.layout"
  ></app-hero-banner>
  
  <!-- Marketplace Agents Section -->
  <app-marketplace-agents-section></app-marketplace-agents-section>
  
  <!-- <app-agents></app-agents> -->
  <!-- <div class="row"><app-studios class="col-5"></app-studios></div> -->
  <app-clients-section></app-clients-section>
  <app-analytics></app-analytics>
  <app-testimonials-section></app-testimonials-section>
  <app-news-blogs></app-news-blogs>
  <app-marketplace-footer></app-marketplace-footer>
</div>
