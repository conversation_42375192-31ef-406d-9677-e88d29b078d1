<section class="analytics">
  <!-- Coming Soon Overlay -->
  <div class="coming-soon-overlay">
    <div class="coming-soon-content">
      <div class="coming-soon-icon">
        <div class="pulse-ring"></div>
        <div class="pulse-ring-2"></div>
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
        </svg>
      </div>
      <h2 class="coming-soon-title">Coming Soon</h2>
      <p class="coming-soon-subtitle">Live analytics dashboard will be available soon</p>
      <div class="coming-soon-dots">
        <span class="dot"></span>
        <span class="dot"></span>
        <span class="dot"></span>
      </div>
    </div>
  </div>
  <!-- Header Section -->
  <div class="analytics-header">
    <div class="title-wrapper">
      <awe-icons iconName="stars" color="#F96CAB"></awe-icons>
      <h1>Live Analytics</h1>
    </div>
  </div>

  <!-- Stats Row -->
  <div class="stats-row row">
    <div class="col-8 d-flex">
      <div class="row">
        <div class="col-6">
          <div class="stat-card row">
            <div class="left-content col-3 p-0">
              <div class="stat-icon blue">
                <fa-icon [icon]="faStar"></fa-icon>
              </div>
              <div class="stat-content">
                <h3>Agents Currently Live</h3>
                <div class="stat-value">100 +</div>
              </div>
              <div class="stat-change positive">
                <fa-icon [icon]="faArrowUp"></fa-icon>
                <span>+12 New Added</span>
              </div>
            </div>
            <div class="right-content col-9 p-0">
              <canvas class="stat-chart"></canvas>
            </div>
          </div>
        </div>
        <div class="col-3">
          <div class="stat-card">
            <div class="stat-icon orange">
              <fa-icon [icon]="faChartBar"></fa-icon>
            </div>
            <div class="stat-content">
              <h3>Total Workflow</h3>
              <div class="stat-value">300</div>
              <div class="stat-change positive">
                <fa-icon [icon]="faArrowUp"></fa-icon>
                <span>+3% Current</span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-3">
          <div class="stat-card">
            <div class="stat-icon pink">
              <fa-icon [icon]="faStar"></fa-icon>
            </div>
            <div class="stat-content">
              <h3>Total Agents</h3>
              <div class="stat-value">600</div>
              <div class="stat-change positive">
                <fa-icon [icon]="faArrowUp"></fa-icon>
                <span>+5% Current</span>
              </div>
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="map-card">
            <div class="map-header">
              <h3>{{ mapData.title }}</h3>
              <div class="success-rate">
                {{ mapData.successRate }} Agent Success Rate
              </div>
            </div>
            <div class="user-count">{{ mapData.userCount }}</div>
            <div class="world-map">
              <!-- Map dots rendered via CSS -->
            </div>
          </div>
        </div>
        <div class="col-6">
          <div class="latencies-card">
            <h3>{{ latenciesData.title }}</h3>
            <table>
              <thead>
                <tr>
                  <th *ngFor="let header of latenciesData.headers">
                    {{ header }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let model of latenciesData.models">
                  <td>{{ model.name }}</td>
                  <td>{{ model.latency }}</td>
                  <td>
                    <span class="status-badge" [ngClass]="model.status.toLowerCase()">{{ model.status }}</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="col-4">
      <div class="code-card">
        <div class="code-header">
          <h3>{{ codeData.title }}</h3>
          <div class="efficiency">{{ codeData.efficiency }} Efficiency</div>
        </div>
        <div class="code-stats">
          <div class="total-lines">{{ codeData.totalLines }}</div>
          <div class="monthly-lines">
            {{ codeData.monthlyLines }} This Month
          </div>
        </div>
        <div class="code-preview">
          <pre>
            <code>
            <span class="line" *ngFor="let line of codeData.lines">
              <span class="line-number">{{ line.number }}</span>
              <span class="code-content">
                <ng-container [ngSwitch]="line.type">
                  <ng-container *ngSwitchCase="'import'">
                    <span class="keyword">import</span> {{ line.content }}
                  </ng-container>
                  <ng-container *ngSwitchCase="'module'">
                    <span class="keyword">from</span> <span class="module">{{ line.content }}</span>
                  </ng-container>
                  <ng-container *ngSwitchDefault>{{ line.content }}</ng-container>
                </ng-container>
              </span>
            </span>
          </code>
        </pre>
        </div>
      </div>
    </div>
  </div>
</section>