import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faStar, faChartBar, faArrowUp } from '@fortawesome/free-solid-svg-icons';
import { Chart } from 'chart.js/auto';
import { IconsComponent } from '@awe/play-comp-library';

interface StatCard {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative';
  icon: any;
  iconColor: string;
  hasChart: boolean;
}

interface MapData {
  title: string;
  successRate: string;
  userCount: string;
}

interface ModelLatency {
  name: string;
  latency: string;
  status: 'Good' | 'Medium' | 'Poor';
}

interface LatenciesData {
  title: string;
  headers: string[];
  models: ModelLatency[];
}

interface CodeLine {
  number: number;
  type: 'import' | 'module' | 'default';
  content: string;
}

interface CodeData {
  title: string;
  efficiency: string;
  totalLines: string;
  monthlyLines: string;
  lines: CodeLine[];
}

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss'],
  standalone: true,
  imports: [CommonModule, FontAwesomeModule, IconsComponent]
})
export class AnalyticsComponent implements OnInit {
  // Font Awesome Icons
  faStar = faStar;
  faChartBar = faChartBar;
  faArrowUp = faArrowUp;

  // Chart Data
  agentsChart: any;

  // Data Properties
  statsData: StatCard[] = [
    {
      title: 'Agents Currently Live',
      value: '100 +',
      change: '+12 New Added',
      changeType: 'positive',
      icon: this.faStar,
      iconColor: 'blue',
      hasChart: true
    },
    {
      title: 'Total Workflow',
      value: '300',
      change: '+3% Current',
      changeType: 'positive',
      icon: this.faChartBar,
      iconColor: 'orange',
      hasChart: false
    },
    {
      title: 'Total Agents',
      value: '600',
      change: '+5% Current',
      changeType: 'positive',
      icon: this.faStar,
      iconColor: 'pink',
      hasChart: false
    }
  ];

  mapData: MapData = {
    title: 'Live Users',
    successRate: '92%',
    userCount: '10,321'
  };

  latenciesData: LatenciesData = {
    title: 'Model Latencies',
    headers: ['Model', 'Avg.Latency', 'Status'],
    models: [
      { name: 'GPT-4', latency: '120ms', status: 'Good' },
      { name: 'Claude 3.5', latency: '89ms', status: 'Good' },
      { name: 'DALLE-3', latency: '320ms', status: 'Medium' },
      { name: 'Gemini Pro', latency: '105ms', status: 'Good' },
      { name: 'Mistral Large', latency: '88ms', status: 'Good' }
    ]
  };

  codeData: CodeData = {
    title: 'Lines of code Processed',
    efficiency: '96%',
    totalLines: '12.5M',
    monthlyLines: '+1.9 M Lines',
    lines: [
      { number: 1, type: 'import', content: 'React, { useState, useEffect } from "react";' },
      { number: 2, type: 'module', content: '"react";' },
      { number: 3, type: 'import', content: '{ motion } from "framer-motion";' },
      { number: 4, type: 'default', content: 'motion";' },
      { number: 5, type: 'import', content: '{' },
      { number: 6, type: 'default', content: 'LineChart,' },
      { number: 7, type: 'default', content: 'Line,' },
      { number: 8, type: 'default', content: 'XAxis,' },
      { number: 9, type: 'default', content: 'YAxis,' },
      { number: 10, type: 'default', content: 'Tooltip,' },
      { number: 11, type: 'default', content: 'ResponsiveContainer' },
      { number: 12, type: 'default', content: '}' },
      { number: 13, type: 'module', content: "'recharts';" },
      { number: 14, type: 'import', content: 'Globe from "react-globe.gl";' },
      { number: 15, type: 'import', content: '{ Card, CardContent } from "@/components/ui/card";' }
    ]
  };

  constructor() { }

  ngOnInit(): void {
    this.initializeAgentsChart();
  }

  private initializeAgentsChart() {
    const ctx = document.querySelector('.stat-chart') as HTMLCanvasElement;
    if (!ctx) return;

    this.agentsChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
        datasets: [{
          data: [65, 59, 80, 81, 56, 95],
          borderColor: '#6366F1',
          backgroundColor: 'rgba(99, 102, 241, 0.1)',
          fill: true,
          tension: 0.4,
          borderWidth: 2,
          pointRadius: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            display: false
          },
          y: {
            display: false
          }
        }
      }
    });
  }
} 