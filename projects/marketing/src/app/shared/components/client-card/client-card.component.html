<div class="client-card" [ngStyle]="cardStyles" [ngClass]="'client-' + variant">
  <!-- Company Logo -->
  <div class="client-logo">
    <img 
      [src]="displayClient.companyLogo" 
      [alt]="displayClient.companyName + ' logo'"
      class="logo-image"
      loading="lazy"
    />
  </div>

  <!-- Testimonial Quote -->
  <div class="client-testimonial">
    <p class="testimonial-text">"{{ displayClient.testimonial }}"</p>
  </div>

  <!-- Author Information -->
  <div class="client-author">
    <div class="author-avatar">
      <img 
        [src]="displayClient.authorAvatar" 
        [alt]="displayClient.authorName + ' avatar'"
        class="avatar-image"
        loading="lazy"
      />
    </div>
    <div class="author-info">
      <h4 class="author-name">{{ displayClient.authorName }}</h4>
      <p class="author-role">{{ displayClient.authorRole }}</p>
    </div>
  </div>
</div> 