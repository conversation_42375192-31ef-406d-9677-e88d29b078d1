import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface Client {
  id?: string | number;
  companyName: string;
  companyLogo: string;
  testimonial: string;
  authorName: string;
  authorRole: string;
  authorAvatar: string;
  backgroundColor?: string;
}

@Component({
  selector: 'app-client-card',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './client-card.component.html',
  styleUrl: './client-card.component.scss',
})
export class ClientCardComponent {
  @Input() client!: Client;
  @Input() variant: 'default' | 'compact' | 'featured' = 'default';
  @Input() backgroundColor: string = '#ffffff';
  @Input() textColor: string = '#333333';
  @Input() borderRadius: string = '12px';
  @Input() padding: string = '24px';
  @Input() shadow: string = '0 4px 16px rgba(0, 0, 0, 0.08)';
  @Input() maxWidth: string = '320px';

  // Individual inputs for backward compatibility
  @Input() companyName: string = '';
  @Input() companyLogo: string = '';
  @Input() testimonial: string = '';
  @Input() authorName: string = '';
  @Input() authorRole: string = '';
  @Input() authorAvatar: string = '';

  get displayClient(): Client {
    // Use client object if provided, otherwise use individual inputs
    if (this.client) {
      return this.client;
    }
    
    return {
      companyName: this.companyName,
      companyLogo: this.companyLogo,
      testimonial: this.testimonial,
      authorName: this.authorName,
      authorRole: this.authorRole,
      authorAvatar: this.authorAvatar,
      backgroundColor: this.backgroundColor,
    };
  }

  get cardStyles() {
    return {
      'background-color': this.displayClient.backgroundColor || this.backgroundColor,
      'color': this.textColor,
      'border-radius': this.borderRadius,
      'padding': this.padding,
      'box-shadow': this.shadow,
      'max-width': this.maxWidth,
    };
  }
} 