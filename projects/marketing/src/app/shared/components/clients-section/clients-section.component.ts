import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ClientCardComponent, Client } from '../client-card/client-card.component';

@Component({
  selector: 'app-clients-section',
  standalone: true,
  imports: [CommonModule, ClientCardComponent],
  templateUrl: './clients-section.component.html',
  styleUrl: './clients-section.component.scss',
})
export class ClientsSectionComponent {
  clients: Client[] = [
    {
      id: 1,
      companyName: 'CVS Health',
      companyLogo: 'assets/icons/cvs.png',
      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',
      authorName: '<PERSON>hn<PERSON><PERSON>',
      authorRole: 'Dev at CVS Health',
      authorAvatar: 'assets/icons/rebbeca.png'
    },
    {
      id: 2,
      companyName: 'AXOS',
      companyLogo: 'assets/icons/axos.png',
      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',
      authorName: '<PERSON><PERSON><PERSON><PERSON>',
      authorRole: 'Designer at AXOS',
      authorAvatar: 'assets/icons/rebbeca.png'
    },
    {
      id: 3,
      companyName: 'LoanDepot',
      companyLogo: 'assets/icons/loandepot.png',
      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',
      authorName: 'Cyhntya Rebecca',
      authorRole: 'Dev at HP',
      authorAvatar: 'assets/icons/rebbeca.png'
    },
    {
      id: 4,
      companyName: 'HP',
      companyLogo: 'assets/icons/hp.png',
      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',
      authorName: 'Cyhntya Rebecca',
      authorRole: 'Dev at HP',
      authorAvatar: 'assets/icons/rebbeca.png'
    },
    {
      id: 5,
      companyName: 'HP',
      companyLogo: 'assets/icons/hp.png',
      testimonial: 'Uses Growthly as the source of truth for all its product data, and to determine where the team should focus its time.',
      authorName: 'Cyhntya Rebecca',
      authorRole: 'Dev at HP',
      authorAvatar: 'assets/icons/rebbeca.png'
    }
  ];

  trackByClient(index: number, client: Client): any {
    return client.id || index;
  }
} 