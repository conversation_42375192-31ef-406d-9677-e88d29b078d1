<div 
  class="marketplace-agent-card" 
  [style.background-color]="cardBackgroundColor"
  [ngClass]="'card-' + variant"
  (click)="onCardClick()"
  [class.clickable]="displayAgent.onClick"
>
  <!-- Card Header with Title and Rating -->
  <div class="card-header">
    <h3 class="card-title">{{ displayAgent.title }}</h3>
    <div class="card-rating">
      <div class="rating-stars">
        <ava-icon
          *ngFor="let filled of getRatingStars(); let i = index"
          iconName="star"
          [iconColor]="filled ? '#fbbf24' : '#d1d5db'"
          iconSize="14"
        ></ava-icon>
      </div>
      <span class="rating-value">{{ displayAgent.rating }}</span>
    </div>
  </div>

  <!-- Card Description -->
  <div class="card-description">
    <p class="description-text">{{ displayAgent.description }}</p>
  </div>

  <!-- Card Footer with User Count and Studio Badge -->
  <div class="card-footer">
    <div class="user-count">
      <ava-icon 
        iconName="user" 
        iconColor="#6b7280" 
        iconSize="16"
      ></ava-icon>
      <span class="count-text">{{ displayAgent.userCount }}</span>
    </div>
    
    <div class="studio-badge" 
         [style.background-color]="studioConfig.backgroundColor"
         [style.color]="studioConfig.textColor"
         [style.border-color]="studioConfig.color">
      {{ displayAgent.studioType }}
    </div>
  </div>
</div> 