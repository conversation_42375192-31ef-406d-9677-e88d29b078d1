import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MarketplaceAgentCardComponent, MarketplaceAgent } from '../marketplace-agent-card/marketplace-agent-card.component';

@Component({
  selector: 'app-marketplace-agents-section',
  standalone: true,
  imports: [CommonModule, MarketplaceAgentCardComponent],
  templateUrl: './marketplace-agents-section.component.html',
  styleUrl: './marketplace-agents-section.component.scss',
})
export class MarketplaceAgentsSectionComponent {
  private baseAgents: MarketplaceAgent[] = [
    {
      id: 1,
      title: 'Brainstorm Product Idea',
      description: 'Effortlessly generate and refine product ideas with smart insights',
      rating: 4.5,
      userCount: 24,
      studioType: 'Product Studio',
      onClick: () => this.navigateToAgent(1)
    },
    {
      id: 2,
      title: 'Upgrade Angular',
      description: 'Seamlessly upgrade to the latest Angular version with optimised code migration',
      rating: 4.5,
      userCount: 24,
      studioType: 'Platform Studio',
      onClick: () => this.navigateToAgent(2)
    },
    {
      id: 3,
      title: 'Ruby to Spring boot',
      description: 'Effortlessly convert Ruby code to Spring Boot with optimised migration',
      rating: 4.5,
      userCount: 24,
      studioType: 'Data Studio',
      onClick: () => this.navigateToAgent(3)
    },
    {
      id: 4,
      title: 'API Test Generation',
      description: 'Automatically generate comprehensive API tests with intelligent test case creation',
      rating: 4.5,
      userCount: 32,
      studioType: 'QE Studio',
      onClick: () => this.navigateToAgent(4)
    },
    {
      id: 5,
      title: 'UI Component Library',
      description: 'Create reusable UI components with design system integration and documentation',
      rating: 4.3,
      userCount: 18,
      studioType: 'Experience Studio',
      onClick: () => this.navigateToAgent(5)
    },
    {
      id: 6,
      title: 'Database Migration Tool',
      description: 'Seamlessly migrate databases with schema validation and data integrity checks',
      rating: 4.7,
      userCount: 45,
      studioType: 'Data Studio',
      onClick: () => this.navigateToAgent(6)
    }
  ];

  // Duplicate agents for seamless scrolling
  agents: MarketplaceAgent[] = [
    ...this.baseAgents,
    ...this.baseAgents.map(agent => ({ ...agent, id: `${agent.id}-duplicate` }))
  ];

  trackByAgent(index: number, agent: MarketplaceAgent): any {
    return agent.id || index;
  }

  navigateToAgent(agentId: number): void {
    console.log('Navigating to agent:', agentId);
    // Implement navigation logic here
    // this.router.navigate(['/agent', agentId]);
  }
} 