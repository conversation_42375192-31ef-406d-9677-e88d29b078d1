<footer class="footer">
  <div class="container-fluid">
    <div class="row mb-5">
      <div
        class="col-12 col-sm-6 col-lg-3 mb-4 footer-section"
        *ngFor="let section of footerData.sections"
      >
        <awe-heading variant="s1" type="bold">{{ section?.title }}</awe-heading>
        <ul class="mt-3">
          <li *ngFor="let item of section.items" class="mb-3">
            <awe-heading variant="s2" type="regular">{{
              item?.text || item
            }}</awe-heading>
          </li>
        </ul>
      </div>
      <div class="col-12 col-sm-6 col-lg-3 mb-4 footer-section logo-section text-center">
        <img src="ascendion-logo.png" alt="Acendion Logo"  class="footer-logo"/>
      </div>
    </div>
    <div class="row footer-bottom">
      <div class="col-12">
        <div class="footer-content text-center">
          <div class="legal-links">
            <ng-container
              *ngFor="let link of footerData.legalLinks; let last = last"
            >
              <awe-heading variant="s2" type="regular">{{
                link?.text
              }}</awe-heading>
              <span *ngIf="!last" class="separator">|</span>
            </ng-container>
          </div>
          <div class="copyright">
            <awe-heading variant="s2" type="regular">© 2025 Ascendion. All Rights Reserved.</awe-heading>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>
 