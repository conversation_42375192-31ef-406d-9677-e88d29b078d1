import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { HeadingComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-marketplace-footer',
  standalone: true,
  imports: [CommonModule, HeadingComponent],
  templateUrl: './marketplace-footer.component.html',
  styleUrl: './marketplace-footer.component.scss',
})
export class MarketplaceFooterComponent {
  currentYear = new Date().getFullYear();
  footerData = {
    sections: [
      {
        title: 'What we do',
        items: [
          { text: 'Applied AI' },
          { text: 'Data' },
          { text: 'Experience' },
          { text: 'Platform Engineering' },
          { text: 'Product Engineering' },
          { text: 'Quality Engineering' },
          { text: 'Innovate With Salesforce' },
        ],
      },
      {
        title: 'Industries',
        items: [
          { text: 'Banking and Financial Service' },
          { text: 'Communication, Media, & Entertainment' },
          { text: 'Healthcare' },
          { text: 'High-Tech' },
          { text: 'Retail and Consumer Goods' },
          { text: 'Travel and Hospitality' },
        ],
      },
      {
        title: 'Quick Links',
        items: [
          { text: 'Home', route: '/home' },
          { text: 'Who we are', route: '/about' },
          { text: 'Insight and impact', route: '/insights' },
          { text: 'Careers India', route: '/careers/india' },
          { text: 'Careers North America', route: '/careers/north-america' },
          { text: 'Careers Europe', route: '/careers/europe' },
          { text: 'Careers Asia Pacific', route: '/careers/asia-pacific' },
          { text: 'Global Delivery Hubs', route: '/delivery-hubs' },
          { text: 'Contact Us', route: '/contact' },
        ],
      },
    ],
    legalLinks: [
      { text: 'Terms of use', route: '/terms' },
      { text: 'Privacy Policy', route: '/privacy' },
      { text: 'Accessibility Disclosure', route: '/accessibility' },
      { text: 'AO-enhances content notice', route: '/content-notice' },
    ],
  };
}
