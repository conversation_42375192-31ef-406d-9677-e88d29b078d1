export const agentsData = [
  {
    id: 1,
    title: 'Ruby to Springboot',
    description:
      'Effortlessly convert Ruby code to Spring Boot with optimized migration patterns',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 24,
  },
  {
    id: 2,
    title: 'Python Modernizer',
    description:
      'Transform legacy Python 2 code to Python 3 with automatic dependency updates',
    rating: 4.7,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 42,
  },
  {
    id: 3,
    title: 'React Refactorer',
    description:
      'Convert class components to functional components with hooks implementation',
    rating: 4.3,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 36,
  },
  {
    id: 4,
    title: 'Microservice Extractor',
    description:
      'Break down monolithic applications into scalable microservices architecture',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 29,
  },
  {
    id: 5,
    title: 'Insights Ascender',
    description:
      'Generate actionable business intelligence from complex datasets',
    rating: 4.6,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 53,
  },
  {
    id: 6,
    title: 'API Gateway Creator',
    description:
      'Design and implement robust API gateways with authentication and rate limiting',
    rating: 4.4,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 31,
  },
  {
    id: 7,
    title: 'Upgrade Angular',
    description:
      'Seamlessly migrate from AngularJS to latest Angular with automated code conversion',
    rating: 4.2,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 45,
  },
  {
    id: 8,
    title: 'CI/CD Pipeline Builder',
    description: 'Create continuous integration and deployment workflows',
    rating: 4.9,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 38,
  },
  {
    id: 9,
    title: 'Data Pipeline Creator',
    description:
      'Design efficient ETL processes for large-scale data operations',
    rating: 4.7,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 49,
  },
  {
    id: 10,
    title: 'Legacy Code Modernizer',
    description:
      'Transform outdated codebases to modern standards with minimal disruption',
    rating: 4.3,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 27,
  },
  {
    id: 11,
    title: 'UI/UX Enhancer',
    description:
      'Upgrade user interfaces with modern design patterns and improved user experience',
    rating: 4.6,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 35,
  },
  {
    id: 12,
    title: 'Database Migration Assistant',
    description:
      'Migrate between database systems with schema transformation and data integrity',
    rating: 4.5,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 41,
  },
  {
    id: 13,
    title: 'Serverless Transformer',
    description:
      'Convert traditional applications to serverless architecture for better scalability',
    rating: 4.4,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 32,
  },
  {
    id: 14,
    title: 'ML Model Deployer',
    description:
      'Streamline deployment of machine learning models to production environments',
    rating: 4.8,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 39,
  },
  {
    id: 15,
    title: 'GraphQL Converter',
    description:
      'Transform REST APIs to GraphQL for improved frontend data fetching',
    rating: 4.3,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 28,
  },
  {
    id: 16,
    title: 'Kubernetes Orchestrator',
    description:
      'Set up and configure containerized applications in Kubernetes clusters',
    rating: 4.7,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 46,
  },
  {
    id: 17,
    title: 'FE Performance Optimizer',
    description:
      'Boost web application performance with code splitting and lazy loading',
    rating: 4.5,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 33,
  },
  {
    id: 18,
    title: 'Cloud Cost Reducer',
    description:
      'Analyze and optimize cloud infrastructure costs while maintaining performance',
    rating: 4.9,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 51,
  },
  {
    id: 19,
    title: 'Security Hardener',
    description:
      'Identify and remediate security vulnerabilities in applications and infrastructure',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 47,
  },
  {
    id: 20,
    title: 'Analytics Integrator',
    description:
      'Implement comprehensive analytics tracking across web and mobile applications',
    rating: 4.4,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 34,
  },
  {
    id: 21,
    title: 'Mobile App Modernizer',
    description:
      'Transform legacy mobile applications with modern frameworks and libraries',
    rating: 4.6,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 43,
  },
  {
    id: 22,
    title: 'Documentation Generator',
    description:
      'Create comprehensive technical documentation from codebase analysis',
    rating: 4.2,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 29,
  },
  {
    id: 23,
    title: 'Test Coverage Enhancer',
    description:
      'Increase test coverage with automated unit and integration test generation',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 37,
  },
  {
    id: 24,
    title: 'BigData Transformer',
    description:
      'Optimize big data processing workflows and data lake architectures',
    rating: 4.7,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 48,
  },
  {
    id: 25,
    title: 'Hybrid Cloud Enabler',
    description:
      'Implement hybrid cloud solutions with seamless integration between providers',
    rating: 4.6,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 39,
  },
  {
    id: 26,
    title: 'IoT Platform Connector',
    description:
      'Integrate IoT devices with cloud platforms for data collection and analysis',
    rating: 4.3,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 31,
  },
  {
    id: 28,
    title: 'Responsive Design Converter',
    description:
      'Transform desktop-only websites to fully responsive mobile-friendly designs',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 36,
  },
  {
    id: 29,
    title: 'DevOps Culture Catalyst',
    description:
      'Transform development workflows with DevOps practices and tools integration',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 53,
  },
  {
    id: 30,
    title: 'Real-time Analytics Engine',
    description:
      'Implement streaming data processing for instantaneous analytics insights',
    rating: 4.7,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 45,
  },
  {
    id: 31,
    title: 'Vue to React Migrator',
    description:
      'Convert Vue.js applications to React with equivalent component structure',
    rating: 4.3,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 33,
  },
  {
    id: 32,
    title: 'Automated Schema Mapper',
    description:
      'Create mappings between different data schemas with intelligent pattern recognition',
    rating: 4.6,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 41,
  },
  {
    id: 33,
    title: 'Code Generator Infrastructure',
    description:
      'Convert manual infrastructure configurations to Terraform or CloudFormation',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 38,
  },
  {
    id: 34,
    title: 'A11y Compliance Enhancer',
    description:
      'Improve application accessibility to meet WCAG standards and guidelines',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 29,
  },
  {
    id: 35,
    title: 'API Documentation Generator',
    description:
      'Create comprehensive API documentation with interactive examples and testing',
    rating: 4.2,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 34,
  },
  {
    id: 36,
    title: 'Containerization Expert',
    description:
      'Convert traditional applications to containerized deployments with Docker',
    rating: 4.7,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 47,
  },
  {
    id: 37,
    title: 'Visualization Creator',
    description:
      'Transform complex data into intuitive visualizations and dashboards',
    rating: 4.6,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 44,
  },
  {
    id: 38,
    title: 'Multi-cloud Orchestrator',
    description:
      'Manage resources across multiple cloud providers with unified governance',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 39,
  },
  {
    id: 39,
    title: 'PHP to Node.js Converter',
    description:
      'Migrate PHP applications to Node.js with equivalent functionality and performance',
    rating: 4.3,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 28,
  },
  {
    id: 40,
    title: 'Dependency Updater',
    description:
      'Safely update project dependencies with automated compatibility testing',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 36,
  },
  {
    id: 41,
    title: 'Event-driven Architecture',
    description:
      'Transform synchronous systems to event-driven architectures with message queues',
    rating: 4.7,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 43,
  },
  {
    id: 42,
    title: 'Localization Implementer',
    description:
      'Add multilingual support to applications with comprehensive localization',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 31,
  },
  {
    id: 43,
    title: 'Legacy Database Modernizer',
    description:
      'Update legacy database schemas to modern patterns with zero downtime',
    rating: 4.6,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 37,
  },
  {
    id: 44,
    title: 'Dark Mode Implementer',
    description:
      'Add dark mode support to applications with automatic theme switching',
    rating: 4.3,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 42,
  },
  {
    id: 45,
    title: 'Performance Profiler',
    description:
      'Identify and resolve application performance bottlenecks with deep analysis',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 48,
  },
  {
    id: 46,
    title: 'Voice Interface Creator',
    description: 'Add voice command capabilities to applications with NLP',
    rating: 4.5,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 34,
  },
  {
    id: 47,
    title: 'SEO Optimizer',
    description:
      'Enhance web application search engine rankings with best practices implementation',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 45,
  },
  {
    id: 48,
    title: 'NoSQL Migrator',
    description:
      'Convert relational databases to NoSQL solutions with optimized data models',
    rating: 4.6,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 38,
  },
  {
    id: 49,
    title: 'PWA Converter',
    description:
      'Transform standard websites into offline-capable progressive web applications',
    rating: 4.7,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 51,
  },
  {
    id: 50,
    title: 'Workflow Automator',
    description:
      'Automate repetitive business processes with custom workflow solutions',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 46,
  },
  {
    id: 51,
    title: 'Mobile-First Converter',
    description:
      'Revamp web applications with mobile-first design principles and implementation',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 39,
  },
  {
    id: 52,
    title: 'API Performance Optimizer',
    description:
      'Enhance API response times and throughput with caching and optimization',
    rating: 4.6,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 33,
  },
  {
    id: 53,
    title: 'Data Anonymizer',
    description:
      'Implement data anonymization techniques for privacy compliance',
    rating: 4.5,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 41,
  },
  {
    id: 54,
    title: 'Cloud Security Auditor',
    description:
      'Perform comprehensive security audits of cloud infrastructure and applications',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 47,
  },
  {
    id: 55,
    title: 'Design System Implementer',
    description:
      'Create and implement consistent design systems across multiple applications',
    rating: 4.7,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 36,
  },
  {
    id: 56,
    title: 'Code Quality Enhancer',
    description:
      'Improve code quality with static analysis and best practices implementation',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 43,
  },
  {
    id: 57,
    title: 'Cache Strategy Optimizer',
    description:
      'Implement optimal caching strategies for improved application performance',
    rating: 4.6,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 31,
  },
  {
    id: 58,
    title: 'Data Validation Implementer',
    description:
      'Add comprehensive data validation with error handling throughout applications',
    rating: 4.4,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 37,
  },
  {
    id: 60,
    title: 'State Management Optimizer',
    description:
      'Improve frontend state management with modern patterns and libraries',
    rating: 4.5,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 38,
  },
  {
    id: 61,
    title: 'Java to Kotlin Converter',
    description:
      'Transform Java codebases to Kotlin with idiomatic code patterns',
    rating: 4.6,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 42,
  },
  {
    id: 62,
    title: 'Resource Optimization',
    description:
      'Reduce resource usage and costs in cloud environments with right-sizing',
    rating: 4.8,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 49,
  },
  {
    id: 63,
    title: 'Analytics Dashboard Creator',
    description:
      'Build interactive analytics dashboards with real-time data visualization',
    rating: 4.7,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C',
    },
    users: 52,
  },
  {
    id: 65,
    title: 'Static Site Generator',
    description:
      'Convert dynamic websites to static sites with improved performance and security',
    rating: 4.3,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 31,
  },
  {
    id: 67,
    title: 'Web Components Converter',
    description:
      'Transform framework-specific components to reusable web components',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 33,
  },
  {
    id: 68,
    title: 'Technical Debt Reducer',
    description:
      'Identify and remediate technical debt with systematic code improvement',
    rating: 4.7,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 47,
  },
  {
    id: 69,
    title: 'Logging Enhancement System',
    description:
      'Implement structured logging with centralized log management and analysis',
    rating: 4.5,
    studio: {
      name: 'Platform Studio',
      type: 'Platform Studio',
      backgroundColor: '  #FFFCEB',
      textColor: ' #8F6B00',
    },
    users: 39,
  },
  {
    id: 70,
    title: 'User Journey Analyzer',
    description:
      'Track and optimize user journeys with behavior analytics and funnel analysis',
    rating: 4.6,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 41,
  },
  {
    id: 28,
    title: 'Responsive Design Converter',
    description:
      'Transform desktop-only websites to fully responsive mobile-friendly designs',
    rating: 4.4,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B',
    },
    users: 36,
  },
];
