export const projectTeam = [
  {
    id: 1,
    title: 'Create Angular Component',
    type: 'flow',
    description: 'Generate modern, reusable Angular components with best practices and TypeScript integration',
    rating: 4.7,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B'
    },
    users: 245
  },
  {
    id: 2,
    title: 'Figma to Code',
    type: 'studio',
    description: 'Convert Figma designs into pixel-perfect, responsive code with component-based architecture',
    rating: 4.9,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B'
    },
    users: 312
  },
  {
    id: 3,
    title: 'Test Accessibility',
    type: 'studio',
    description: 'Evaluate and enhance web accessibility compliance with WCAG guidelines and automated testing',
    rating: 4.6,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B'
    },
    users: 178
  },
  {
    id: 4,
    title: 'Generate UI Design',
    type: 'studio',
    description: 'Create modern UI designs with design system integration and component-based architecture',
    rating: 4.8,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B'
    },
    users: 289
  },
  {
    id: 5,
    title: 'Mindmap',
    type: 'flow',
    description: 'Create interactive mindmaps for project planning and idea visualization with real-time collaboration',
    rating: 4.5,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B'
    },
    users: 156
  },
  {
    id: 6,
    title: 'React to Angular',
    type: 'flow',
    description: 'Convert React applications to Angular with automated component migration and state management',
    rating: 4.7,
    studio: {
      name: 'Data Studio',
      type: 'Data Studio',
      backgroundColor: '#DEFFEE',
      textColor: '#00806C'
    },
    users: 234
  },
  {
    id: 7,
    title: 'Generate Unit Test Case',
    type: 'flow',
    description: 'Automatically generate comprehensive unit tests with high coverage and best practices',
    rating: 4.8,
    studio: {
      name: 'Quality Studio',
      type: 'Quality Studio',
      backgroundColor: '#FBEDEC',
      textColor: '#C9372C'
    },
    users: 198
  },
  {
    id: 8,
    title: 'SWOT Analysis',
    type: 'flow',
    description: 'Perform detailed SWOT analysis with competitive insights and market trend evaluation',
    rating: 4.6,
    studio: {
      name: 'Experience Studio',
      type: 'Experience Studio',
      backgroundColor: '#FFF4F9',
      textColor: '#DC047B'
    },
    users: 167
  }
]; 