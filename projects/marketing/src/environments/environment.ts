// Helper function to safely get environment variables from window.env
// It will throw an error if a key is not found, ensuring all required envs are present.
const getRequiredEnv = (key: string): string => {
  // Extend the Window interface to include 'env'
  interface EnvWindow extends Window {
    env?: Record<string, string>;
  }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(`Environment variable '${key}' is not defined in window.env.`);
  }
  return String(value); // Ensure the value is returned as a string
};

// ---

// Dynamically retrieve the base URL first, as it's used for other URLs
const dynamicBaseUrl: string = getRequiredEnv('baseUrl');

// ---

// Environment configuration for Console Application
// All values are dynamically retrieved from window.env.
// If a variable is not found, an error will be thrown.
export const environment = {
  production: false, // This often remains a static build-time flag

  // Application URLs (constructed dynamically)
  elderWandUrl: getRequiredEnv('elderWandUrl'),
  experienceStudioUrl: getRequiredEnv('experienceStudioUrl'),
  productStudioUrl: getRequiredEnv('productStudioUrl'),
  consoleRedirectUrl: getRequiredEnv('consoleRedirectUrl'),
  consoleUrl: getRequiredEnv('consoleUrl'),
  consoleRedirectUri: getRequiredEnv('consoleRedirectUri'), // Often same as consoleRedirectUrl

  // API Configuration (constructed dynamically or directly from window.env)
  accessKey: getRequiredEnv('accessKey'),
  apiVersion: getRequiredEnv('apiVersion'),
  baseUrl:  getRequiredEnv('baseUrl'), // The base URL itself
  consoleApi: getRequiredEnv('consoleApi'),
  consoleApiV2: getRequiredEnv('consoleApiV2'),
  consoleApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  consoleEmbeddingApi: getRequiredEnv('consoleEmbeddingApi'),
  consoleInstructionApi: getRequiredEnv('consoleInstructionApi'),
  consoleLangfuseUrl: getRequiredEnv('consoleLangfuseUrl'), 
  consoleTruelensUrl:  getRequiredEnv('consoleTruelensUrl'),
  consolePipelineApi: getRequiredEnv('consolePipelineApi'),
  experienceApiUrl: getRequiredEnv('experienceApiUrl'),
  productApiUrl: getRequiredEnv('productApiUrl'),

  // Logging and App Specific
  enableLogStreaming: getRequiredEnv('enableLogStreaming'),
  logStreamingApiUrl: getRequiredEnv('logStreamingApiUrl'),
  appVersion: getRequiredEnv('appVersion'),
  workflowExecutionMode: getRequiredEnv('workflowExecutionMode'),
  useBasicLogin: getRequiredEnv('useBasicLogin'), // Assuming this is a string "true" or "false"
};

// Log the environment configuration for debugging purposes
console.log('Environment configuration loaded dynamically:', environment);