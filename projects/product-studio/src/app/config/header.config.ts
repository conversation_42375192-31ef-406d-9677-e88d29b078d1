import {
  HeaderConfig,
  SharedNavItem,
} from '@shared/components/app-header/app-header.component';
import { environment } from '../../environments/environment';

// Product Studio specific navigation items
const productStudioNavItems: SharedNavItem[] = [
  {
    label: 'Brainstormer',
    route: '/',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/brainstormer.svg`,
  },
  {
    label: 'Create Workflow',
    route: '/',
    selected: true,
    hasDropdown: false,
    icon: `assets/icons/create-workflow.svg`,
  },
  {
    label: 'User Story',
    route: '/',
    selected: false,
    hasDropdown: false,
    icon: `assets/icons/user-story-creation.svg`,
  },
  // {
  //   label: 'User Story Creation',
  //   route: '/product/brainstormer',
  //   selected: false,
  //   hasDropdown: false,
  //   icon: `assets/icons/analytics.svg`,
  // },
  // {
  //   label: 'Create Workflow',
  //   route: '/',
  //   selected: false,
  //   hasDropdown: false,
  //   icon: `assets/icons/research.svg`,
  // },
  // {
  //   label: 'More...',
  //   route: '/build',
  //   selected: true, // Default to Build being selected since it's now the default route
  //   hasDropdown: true,
  //   dropdownOpen: false,
  //   icon: `assets/icons/more.svg`,
  //   dropdownItems: [
  //     {
  //       label: 'Create Workflow',
  //       description: 'Create Workflow',
  //       route: '#',
  //       icon: `svgs/icons/awe_agents.svg`,
  //     },
  //     {
  //       label: 'User Story',
  //       description: 'Create, Manage and Edit Workflows',
  //       route: '#',
  //       icon: `svgs/icons/awe_workflows.svg`,
  //     }, 
  //     // {
  //     //   label: 'Brownfield',
  //     //   description: 'Create, Manage and Edit Workflows',
  //     //   route: '',
  //     //   icon: `svgs/icons/awe_workflows.svg`,
  //     // },
  //   ],

  // },
];

// Available studio apps for the app drawer
const availableStudioApps = [
  {
    name: 'Product Studio',
    route: environment.productStudioRedirectUrl,
    icon: 'assets/studio-logo/product-studio.svg',
    description: 'Product strategy and business model canvas',
  },
  {
    name: 'Experience Studio',
    route: environment.experienceStudioUrl,
    icon: 'assets/studio-logo/experience-studio.svg',
    description: 'AI-powered design analysis and code generation',
  },
  {
    name: 'Launchpad',
    route: environment.elderWandUrl,
    icon: 'assets/studio-logo/launch-pad.svg',
    description: 'Central application launcher and hub',
  },
  {
    name: 'Console',
    route: environment.consoleUrl,
    icon: 'assets/studio-logo/console.svg',
    description: 'Agent management and workflow automation',
  },
];

// Available languages for the language switcher
const availableLanguages = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Español' },
  { code: 'fr', name: 'Français' },
];

// Product Studio header configuration
export const productStudioHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/studio-logo/header-ascendion-logo.svg',
  navItems: productStudioNavItems,
  showOrgSelector: true,
  showThemeToggle: true,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  showLanguageSwitcher: true,
  disableThemeToggle: false,
  disableLanguageChange: true,
  projectName: 'Product Studio',
  redirectUrl: '/',
  currentApp: 'Product Studio',
  availableApps: availableStudioApps,
  availableLanguages: availableLanguages,
  enableLogoAnimation: true,
  logoAnimationInterval: 3500, // 3.5 seconds between transitions
  logoAnimationStyle: 'fade',
  studioLogos: ['ascendionAAVA-logo-light.svg', 'AAVA_logo.svg', 'PS_LOGO.svg'],
  studioNames: ['Experience Studio', 'Design Innovation', 'Code Generation'],
};
