<div class="custom-feature-list-container">
  <!-- Header with title and dropdown filter -->
  <div class="feature-list-header">
    <h4 class="feature-list-title">Feature List</h4>
    
    <!-- Custom Dropdown Filter -->
    <div class="custom-dropdown" *ngIf="showDropdown" (document:click)="onDocumentClick($event)">
      <button 
        class="dropdown-toggle" 
        (click)="toggleDropdown()"
        [class.active]="isDropdownOpen"
        type="button"
      >
        <div class="dropdown-content">
          <span 
            class="filter-indicator" 
            [style.background-color]="getSelectedFilterOption().color"
          ></span>
          <span class="filter-label">{{ getSelectedFilterOption().label }}</span>
        </div>
        <svg 
          class="dropdown-arrow" 
          [class.rotated]="isDropdownOpen"
          width="12" 
          height="8" 
          viewBox="0 0 12 8" 
          fill="none"
        >
          <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>
      
      <!-- Dropdown Menu -->
      <div class="dropdown-menu" *ngIf="isDropdownOpen">
        <button 
          *ngFor="let option of filterOptions"
          class="dropdown-item"
          [class.selected]="selectedFilter === option.value"
          (click)="selectFilter(option.value)"
          type="button"
        >
          <span 
            class="filter-indicator" 
            [style.background-color]="option.color"
          ></span>
          <span class="filter-label">{{ option.label }}</span>
        </button>
      </div>
    </div>
  </div>

  <!-- Feature Cards Grid -->
  <div class="feature-cards d-flex justify-content-between align-items-center flex-wrap gap-2">
    <div 
      *ngFor="let feature of filteredFeatures; trackBy: trackByFeature"
      class="feature-card"
      [style.border-top-color]="feature.color"
      [title]="feature.name"
    >
      <span class="feature-name">{{ feature.name }}</span>
    </div>
    
    <!-- Empty state -->
    <div *ngIf="filteredFeatures.length === 0" class="empty-state">
      <span class="empty-message">No features found for the selected filter</span>
    </div>
  </div>
</div>
