import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-left-panel-header',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './left-panel-header.component.html',
  styleUrl: './left-panel-header.component.scss'
})
export class LeftPanelHeaderComponent {
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() isCollapsed: boolean = false;
  @Output() togglePanel = new EventEmitter<void>();

  onTogglePanel(): void {
    this.togglePanel.emit();
  }
}
