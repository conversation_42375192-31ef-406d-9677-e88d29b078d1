import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="loading-overlay" [class.show]="isVisible">
      <div class="loading-container">
        <div class="loading-content">
          <!-- Spinner -->
          <div class="spinner-container">
            <div class="spinner"></div>
          </div>
          
          <!-- Loading Message -->
          <div class="loading-message">
            <h3>{{ title }}</h3>
            <p>{{ message }}</p>
          </div>
          
          <!-- Progress Steps (Optional) -->
          <div class="progress-steps" *ngIf="showSteps">
            <div class="step" 
                 *ngFor="let step of steps; let i = index" 
                 [class.active]="i === currentStep"
                 [class.completed]="i < currentStep">
              <div class="step-circle">
                <span *ngIf="i < currentStep">✓</span>
                <span *ngIf="i === currentStep" class="step-number">{{ i + 1 }}</span>
                <span *ngIf="i > currentStep" class="step-number">{{ i + 1 }}</span>
              </div>
              <span class="step-label">{{ step }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(8px);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .loading-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .loading-container {
      background: white;
      border-radius: 16px;
      padding: 40px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
      text-align: center;
      max-width: 400px;
      width: 90%;
    }

    .spinner-container {
      margin-bottom: 24px;
    }

    .spinner {
      width: 48px;
      height: 48px;
      border: 4px solid #f3f4f6;
      border-top: 4px solid #6366f1;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-message h3 {
      margin: 0 0 8px 0;
      color: #1f2937;
      font-size: 20px;
      font-weight: 600;
    }

    .loading-message p {
      margin: 0;
      color: #6b7280;
      font-size: 14px;
      line-height: 1.5;
    }

    .progress-steps {
      margin-top: 32px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .step {
      display: flex;
      align-items: center;
      gap: 12px;
      text-align: left;
    }

    .step-circle {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
    }

    .step.completed .step-circle {
      background: #10b981;
      color: white;
    }

    .step.active .step-circle {
      background: #6366f1;
      color: white;
      animation: pulse 2s infinite;
    }

    .step:not(.active):not(.completed) .step-circle {
      background: #f3f4f6;
      color: #9ca3af;
    }

    @keyframes pulse {
      0%, 100% { transform: scale(1); }
      50% { transform: scale(1.05); }
    }

    .step-label {
      color: #374151;
      font-size: 14px;
      font-weight: 500;
    }

    .step.completed .step-label {
      color: #10b981;
    }

    .step.active .step-label {
      color: #6366f1;
      font-weight: 600;
    }
  `]
})
export class LoadingComponent {
  @Input() isVisible: boolean = false;
  @Input() title: string = 'Processing...';
  @Input() message: string = 'Please wait while we process your request.';
  @Input() showSteps: boolean = false;
  @Input() steps: string[] = [];
  @Input() currentStep: number = 0;
}
