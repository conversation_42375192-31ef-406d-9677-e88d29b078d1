<div class="right-panel-header" [attr.data-theme]="theme">
  <div class="header-content">
    <!-- Left side: Toggle button and Project Title -->
    <div class="header-left">
      <button
        *ngIf="isCollapsed"
        class="toggle-button collapsed-toggle"
        (click)="onTogglePanel()"
        title="Show chat panel"
        type="button"
        aria-label="Show chat panel">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </button>

      <!-- Project Title (always visible) -->
      <div class="title-container" *ngIf="!isHeaderLoading">
        <h3 class="project-title" [title]="getFormattedTitle()">
          {{ getFormattedTitle() }}
        </h3>
      </div>
    </div>

    <!-- Center: Loading State for Pipeline Next API -->
    <div class="header-center">
      <div class="loading-container" *ngIf="isNextStepLoading">
        <div class="loading-spinner"></div>
        <span class="loading-text">{{ nextStepLoadingMessage }}</span>
      </div>
    </div>

    <!-- Right side: Step Pagination Controls -->
    <div class="header-right">
      <div class="step-pagination">
        <!-- Previous Step Button -->
        <button
          class="step-nav-button prev-button"
          (click)="onPreviousStep()"
          [disabled]="!(canGoPrevious$ | async)"
          title="Previous step"
          type="button"
          aria-label="Go to previous step">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <!-- Step Counter -->
        <div class="step-counter">
          <span class="current-step">{{ getCurrentStepNumber() }}</span>
          <span class="step-separator">/</span>
          <span class="total-steps">{{ totalSteps }}</span>
        </div>

        <!-- Next Step Button or Finish Button -->
        <button
          *ngIf="!isLastStep()"
          class="step-nav-button next-button"
          (click)="onNextStep()"
          [disabled]="!(canGoNext$ | async) || isNextButtonDisabled"
          [class.loading]="isNextStepLoading"
          title="Next step"
          type="button"
          aria-label="Go to next step">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <!-- Finish Button (Tick Icon) - Only on last step -->
        <button
          *ngIf="isLastStep()"
          class="step-nav-button finish-button"
          (click)="onFinish()"
          [disabled]="finishLoading"
          [class.loading]="finishLoading"
          title="Finish and view summary"
          type="button"
          aria-label="Finish and view summary">
          <svg *ngIf="!finishLoading" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <polyline points="20,6 9,17 4,12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          <!-- Loading spinner -->
          <div *ngIf="finishLoading" class="loading-spinner"></div>
        </button>
      </div>
    </div>
  </div>
</div>
