<div class="recent-projects-container">
  <!-- Header -->
  <div class="header">
    <h2 class="title">Recent Projects</h2>
    <button 
      class="refresh-btn" 
      (click)="retry()" 
      [disabled]="isLoading"
      title="Refresh projects">
      <span class="refresh-icon" [class.spinning]="isLoading">↻</span>
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading && projects.length === 0" class="loading-container">
    <div class="spinner"></div>
    <p class="loading-text">Loading recent projects...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="error-container">
    <div class="error-icon">⚠️</div>
    <p class="error-message">{{ error }}</p>
    <button class="retry-btn" (click)="retry()">Try Again</button>
  </div>

  <!-- Projects Grid -->
  <div *ngIf="!isLoading && !error && projects.length > 0" class="projects-grid">
    <div 
      *ngFor="let project of projects; trackBy: trackByProjectId" 
      class="project-card"
      [class.loading]="isProjectLoading(project.id)"
      (click)="onProjectClick(project)">
      
      <!-- Project Card Header -->
      <div class="card-header">
        <h3 class="project-name">{{ project.name }}</h3>
        <div class="status-badge" [ngClass]="getStatusBadgeClass(project.status)">
          {{ getStatusDisplayText(project.status) }}
        </div>
      </div>

      <!-- Project Description -->
      <p class="project-description">{{ project.description }}</p>

      <!-- Project Metadata -->
      <div class="project-metadata">
        <div class="metadata-item">
          <span class="label">Created:</span>
          <span class="value">{{ formatDate(project.created_at) }}</span>
        </div>
        <div class="metadata-item">
          <span class="label">Updated:</span>
          <span class="value">{{ formatDate(project.updated_at) }}</span>
        </div>
        <div class="metadata-item">
          <span class="label">Created by:</span>
          <span class="value">{{ project.created_by }}</span>
        </div>
      </div>

      <!-- Project Tags -->
      <div *ngIf="project.tags && project.tags.length > 0" class="project-tags">
        <span 
          *ngFor="let tag of project.tags" 
          class="tag">
          {{ tag }}
        </span>
      </div>

      <!-- Loading Overlay -->
      <div *ngIf="isProjectLoading(project.id)" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p class="loading-message">Loading project...</p>
      </div>

      <!-- Click Indicator -->
      <div class="click-indicator">
        <span class="arrow">→</span>
      </div>
    </div>
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && projects.length === 0" class="empty-state">
    <div class="empty-icon">📁</div>
    <h3 class="empty-title">No Recent Projects</h3>
    <p class="empty-message">You haven't created any projects yet. Start by creating your first project!</p>
    <button class="create-btn" (click)="onCreateNewProject()">
      Create New Project
    </button>
  </div>
</div>