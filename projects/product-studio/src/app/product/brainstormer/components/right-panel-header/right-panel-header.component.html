<div class="right-panel-header">
  <!-- Step Title with Loading State -->
  <div class="header-title-container">
    <awe-heading variant="h5" type="bold" class="header-title" *ngIf="!isHeaderLoading">
      {{ currentStep?.label || "Brainstorming" }}
    </awe-heading>
    
    <!-- Header Loader -->
    <div class="header-loading-container" *ngIf="isHeaderLoading">
      <div class="traffic-loader"></div>
      <span class="header-loading-text">{{ headerLoadingMessage }}</span>
    </div>
  </div>

  <!-- Navigation Actions -->
  <div class="header-actions">
    <button
      (click)="onPreviousStep()"
      [disabled]="!stepperService.canGoPrevious()"
      class="btn btn-light nav-button rounded-pill"
      type="button"
    >
      <awe-icons class="nav-icon" iconName="awe_chevron_left" color="#6b7280"></awe-icons>
    </button>

    <button
      (click)="onNextStep()"
      [disabled]="!stepperService.canGoNext()"
      class="btn btn-light nav-button rounded-pill"
      type="button"
    >
      <awe-icons
        class="nav-icon"
        iconName="awe_chevron_right"
        color="#6b7280"
      ></awe-icons>
    </button>
  </div>
</div>
