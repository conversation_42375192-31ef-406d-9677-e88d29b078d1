import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeadingComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { StepperService, StepperStep } from '../../../shared/services/stepper-service/stepper.service';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-right-panel-header',
  standalone: true,
  imports: [CommonModule, HeadingComponent, IconsComponent],
  templateUrl: './right-panel-header.component.html',
  styleUrl: './right-panel-header.component.scss'
})
export class RightPanelHeaderComponent implements OnInit, OnDestroy {
  // Input properties for step navigation state
  @Input() currentStep: StepperStep | null = null;
  @Input() isHeaderLoading: boolean = false;
  @Input() headerLoadingMessage: string = '';

  // Output events for navigation actions
  @Output() nextStepClicked = new EventEmitter<void>();
  @Output() previousStepClicked = new EventEmitter<void>();

  private subscriptions: Subscription[] = [];

  constructor(public stepperService: StepperService) {}

  ngOnInit(): void {
    // Subscribe to current step changes if not provided via input
    if (!this.currentStep) {
      this.subscriptions.push(
        this.stepperService.currentStep$.subscribe((step) => {
          this.currentStep = step;
        })
      );
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  onNextStep(): void {
    this.nextStepClicked.emit();
  }

  onPreviousStep(): void {
    this.previousStepClicked.emit();
  }
}
