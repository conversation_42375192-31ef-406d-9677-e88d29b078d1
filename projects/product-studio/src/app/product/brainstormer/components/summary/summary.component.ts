import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetector<PERSON>ef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';

import { HeadingComponent, CaptionComponent } from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { AweCardComponent } from '../awe-card/awe-card.component';
import { AweProgressBarComponent } from "../awe-progress-bar/awe-progress-bar.component";
import { CustomFeatureListComponent, CustomFeature } from '../custom-feature-list/custom-feature-list.component';
import { SummaryService } from './summary.service';
import { SummaryData } from './summary.interfaces';
import { StepperService, StepperStep } from '../../../shared/services/stepper-service/stepper.service';
import { AppStateService } from '../../../shared/services/app-state.service';
import { RoadmapDataService } from '../../services/roadmap-data.service';
import { Epic } from '../../interfaces/pipeline-api.interface';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { AweModalComponent } from '../awe-modal/awe-modal.component';

@Component({
  selector: 'app-summary',
  standalone: true,
  imports: [
    CommonModule,
    AweCardComponent,
    IconsComponent,
    HeadingComponent,
    AweProgressBarComponent,
    CaptionComponent,
    CustomFeatureListComponent,
    AweModalComponent
  ],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss'
})
export class SummaryComponent implements OnInit, OnDestroy, AfterViewInit {
  // Observables
  summaryData$: Observable<SummaryData>;
  projectName$: Observable<string>;

  // Component state
  summaryData: SummaryData | null = null;
  currentStep: StepperStep | null = null;
  isEditingTitle = false;
  private originalTitle = '';
  private subscriptions: Subscription[] = [];

  // Modal properties
  isModalOpen = false;
  modalTitle = '';
  modalMessage = '';
  modalType: 'error' | 'success' | 'info' | 'warning' | 'confirm' = 'info';
  modalCallback: (() => void) | null = null;

  constructor(
    private summaryService: SummaryService,
    private stepperService: StepperService,
    private appStateService: AppStateService,
    private productPipelineService: ProductPipelineService,
    private roadmapDataService: RoadmapDataService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {
    console.log('🏗️ [SUMMARY COMPONENT] Constructor called');

    // Initialize observables
    this.summaryData$ = this.summaryService.summaryData$;
    this.projectName$ = this.summaryService.getProjectName$();

    console.log('   📡 Observables initialized');

    // Initial subscription to ensure immediate data updates
    this.setupDataSubscription();

    // Load initial data
    this.loadInitialData();

    console.log('   ✅ Constructor setup completed');
  }

  // Lifecycle hooks
  ngOnInit(): void {
    console.log('🚀 [SUMMARY COMPONENT] ngOnInit called');

    // Subscribe to stepper service
    this.subscribeToStepperService();
    console.log('   ✅ ngOnInit completed');
  }

  ngAfterViewInit(): void {
    console.log('[SUMMARY COMPONENT] ngAfterViewInit called');
    console.log('Current summaryData:', this.summaryData?.description);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Private setup methods
  private setupDataSubscription(): void {
    console.log('📡 [SUMMARY COMPONENT] Setting up data subscription...');
    this.subscriptions.push(
      this.summaryData$.subscribe({
        next: (data) => {
          console.log('📨 [SUMMARY COMPONENT] Received summary data update:', data ? 'data received' : 'null data');
          if (data) {
            console.log('   📊 Data name:', data.name);
            console.log('   📊 Data progress:', data.progress + '%');
            console.log('   📊 Data hasData:', data.hasData);
            console.log('   📊 Features count:', data.features?.length || 0);
            console.log('   📊 Personas count:', data.personas?.length || 0);

            this.summaryData = data;
            this.cdr.detectChanges();

            console.log('   ✅ Summary data updated and change detection triggered');
          } else {
            console.log('   ⚠️ Received null/undefined data');
          }
        },
        error: (error) => console.error('❌ [SUMMARY COMPONENT] Error in summary data subscription:', error)
      })
    );

    console.log('   ✅ Data subscription setup completed');
  }

  private loadInitialData(): void {
    console.log('💾 [SUMMARY COMPONENT] Loading initial data...');

    // Try loading from session storage first
    const storedData = this.summaryService.getSummaryDataFromStorage();
    console.log('   📦 Stored data from session storage:', storedData ? 'found' : 'not found');

    if (storedData?.hasData) {
      console.log('   ✅ Using stored data:', storedData.name);
      this.summaryData = storedData;
    } else {
      console.log('   ⚠️ No valid stored data found');
    }

    // Subscribe to pipeline state changes
    const pipelineSubscription = this.appStateService.pipelineState$.subscribe(pipelineState => {
      console.log('🔄 [SUMMARY COMPONENT] Pipeline state changed:', pipelineState?.run_id);
      console.log('   📊 Pipeline project name:', pipelineState?.project_name);
      console.log('   📊 Pipeline data keys:', Object.keys(pipelineState?.data || {}));

      if (pipelineState) {
        console.log('   🔄 Triggering summary data refresh due to pipeline state change...');
        // this.summaryService.forceDataRefresh();
      }
    });

    this.subscriptions.push(pipelineSubscription);
    console.log('   ✅ Initial data loading setup completed');
  }

  private subscribeToStepperService(): void {
    const stepperSub = this.stepperService.currentStep$.subscribe({
      next: (step) => {

        this.currentStep = step;
        this.cdr.detectChanges();
      },
      error: (error) => console.error('❌ Error in stepper subscription:', error)
    });

    this.subscriptions.push(stepperSub);
  }
  // Export methods
  exportToExperienceStudio(): void {

  }

  exportToCsv(): void {

    if (!this.roadmapDataService.hasDataForExport()) {
      console.warn('⚠️ No roadmap data available for CSV export');
      this.showModal('Export Not Available', 'No roadmap data available to export. Please complete the roadmap step first.', 'warning');
      return;
    }


    this.roadmapDataService.exportRoadmapToCsv().subscribe({
      next: (result) => {
        const message = `Successfully exported ${result.rowCount} roadmap epics to "${result.filename}"`;
        this.showModal('Export Successful', message, 'success');
      },
      error: (error) => {
        console.error('❌ CSV export failed:', error);
        this.showModal('Export Failed', 'Failed to export CSV. Please try again.', 'error');
        console.error('❌ CSV export error details:', error);
      }
    });
  }

  exportToJira(): void {

    // Always allow export as per requirements, but check if we have data to export
    const epics = this.roadmapDataService.getEpicsFromRoadmap();

    if (epics.length === 0) {
      console.warn('⚠️ No roadmap epics available for Jira export');
      this.showModal('Export Not Available', 'No roadmap epics available to export. Please complete the roadmap step first.', 'warning');
      return;
    }

    // Show confirmation dialog
    const confirmMessage = `This will create ${epics.length} Jira tickets from your roadmap epics. Continue?`;
    this.showConfirmModal('Confirm Jira Export', confirmMessage, () => {
      this.performJiraExport();
    });
  }

  private performJiraExport(): void {
    // Export to Jira using pipeline state data
    this.roadmapDataService.exportRoadmapToJira().subscribe({
      next: (result) => {
        if (result.success) {
          const message = `Successfully created ${result.ticketsCreated || 'multiple'} Jira tickets!`;
          this.showModal('Export Successful', message, 'success');
        } else {
          const message = `Jira export completed with message: ${result.message}`;
          this.showModal('Export Complete', message, 'info');
        }
      },
      error: (error) => {
        console.error('❌ Jira export failed:', error);
        const errorMessage = error.error || error.message || 'Failed to export to Jira. Please try again.';
        this.showModal('Export Failed', `Export failed: ${errorMessage}`, 'error');
        console.error('❌ Jira export error details:', error);
      }
    });
  }

  // Getters and checks
  // getFormattedTitle(): string {
  //   return this.appStateService.projectDetailsState.project_name || '';
  // }

  hasStepData(step: string): boolean {
    return this.summaryService.hasStepData(step);
  }

  getProjectName(): string {
    const projectName = this.appStateService.projectDetailsState.project_name ||
                       this.appStateService.pipelineState.project_name ||
                       '';
    console.log("###Project Name", projectName);
    return projectName;
  }

  getDescription(): string {
    // First try to get from summaryData
    if (this.summaryData?.description) {
      return this.summaryData.description;
    }
    
    // Fallback to project details justification
    return this.appStateService.projectDetailsState?.justification || '';
  }

  isCsvExportAvailable(): boolean {
    return true;
  }

  isJiraExportAvailable(): boolean {
    return true;
  }

  isExperienceStudioExportAvailable(): boolean {
    return false;
  }

  getJiraEpicsPreview(): Epic[] {
    return this.roadmapDataService.getEpicsFromRoadmap();
  }

  refreshFromSessionStorage(): void {
    const storedData = this.summaryService.getSummaryDataFromStorage();
    if (storedData) {
      this.summaryData = storedData;
    }
  }

  // Project title editing
  startEditingTitle(): void {
    this.originalTitle = this.getProjectName();
    this.isEditingTitle = true;

    setTimeout(() => {
      const input = document.querySelector('.project-title-input') as HTMLInputElement;
      if (input) {
        input.focus();
        input.select();
      }
    }, 0);
  }

  saveTitle(newTitle: string): void {
    const trimmedTitle = newTitle?.trim();

    if (trimmedTitle && trimmedTitle !== this.originalTitle) {
      this.setProjectName(trimmedTitle);
    }

    this.isEditingTitle = false;
    this.originalTitle = '';
  }

  setProjectName(projectName: string): void {
    if (projectName && projectName.trim()) {
      this.productPipelineService.setProjectName(projectName.trim());
    }
  }

  cancelEditingTitle(): void {
    this.isEditingTitle = false;
    this.originalTitle = '';
  }

  // Navigation methods
  redirectToUnderstanding(): void {
    this.navigateToBrainstormingStep('understanding');
  }

  redirectToPersona(): void {
    this.navigateToBrainstormingStep('persona');
  }

  redirectToFeature(): void {
    this.navigateToBrainstormingStep('features');
  }

  redirectToSwot(): void {
    this.navigateToBrainstormingStep('swot');
  }

  redirectToRoadmap(): void {
    this.navigateToBrainstormingStep('roadmap');
  }

  private navigateToBrainstormingStep(stepId: string): void {
    // First navigate to the brainstorming route if not already there
    const currentUrl = this.router.url;

    if (!currentUrl.includes('/brainstorming')) {
      this.router.navigate(['/product/brainstormer/brainstorming']).then(() => {
        this.stepperService.goToStepById(stepId);
      });
    } else {
      this.stepperService.goToStepById(stepId);
    }
  }

  // Data transformation
  getCustomFeatures(): CustomFeature[] {
    if (!this.summaryData?.features) {
      return [];
    }
    return this.summaryData.features.map(feature => ({
      name: feature.name,
      color: feature.color,
      category: feature.category
    }));
  }

  // UI helpers
  getPlaceholderText(step: string): string {
    switch (step) {
      case 'understanding':
        return 'Complete the Understanding step to see market insights here.';
      case 'personas':
        return 'Complete the User Persona step to see personas here.';
      case 'features':
        return 'Complete the Feature List step to see features here.';
      case 'swot':
        return 'Complete the SWOT Analysis step to see analysis here.';
      case 'roadmap':
        return 'Complete the Roadmap step to see timeline here.';
      default:
        return 'No data available yet.';
    }
  }

  // Modal methods
  showModal(title: string, message: string, type: 'error' | 'success' | 'info' | 'warning' = 'info', callback?: () => void): void {
    this.modalTitle = title;
    this.modalMessage = message;
    this.modalType = type;
    this.modalCallback = callback || null;
    this.isModalOpen = true;
  }

  showConfirmModal(title: string, message: string, callback: () => void): void {
    this.modalTitle = title;
    this.modalMessage = message;
    this.modalType = 'confirm';
    this.modalCallback = callback;
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.modalCallback = null;
  }

  onModalConfirm(): void {
    if (this.modalCallback) {
      this.modalCallback();
    }
    this.closeModal();
  }

  getModalClass(): string {
    switch (this.modalType) {
      case 'error':
        return 'modal-error';
      case 'success':
        return 'modal-success';
      case 'warning':
        return 'modal-warning';
      case 'confirm':
        return 'modal-confirm';
      case 'info':
      default:
        return 'modal-info';
    }
  }
}
