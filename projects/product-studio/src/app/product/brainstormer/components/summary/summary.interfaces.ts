// Summary Component Interfaces for Product Studio

/**
 * Summary-specific persona interface (simplified from full UserPersona)
 */
export interface SummaryPersona {
  name: string;
  role: string;
  avatarUrl: string;
}

/**
 * Summary-specific feature interface with color coding
 */
export interface SummaryFeature {
  name: string;
  color: string;
  category: 'must_have' | 'should_have' | 'could_have' | 'wont_have';
}

/**
 * Summary-specific SWOT item interface
 */
export interface SummarySwotItem {
  category: 'strengths' | 'weaknesses' | 'opportunities' | 'threats';
  points: string[];
  color: string;
}

/**
 * Timeline item for roadmap display
 */
export interface SummaryTimelineItem {
  icon: string;
  label: string;
}

/**
 * Timeline quarter/phase grouping
 */
export interface SummaryTimelineQuarter {
  quarter: string;
  items: SummaryTimelineItem[];
}

/**
 * Complete summary data structure for the component
 */
export interface SummaryData {
  // Project basic info
  name: string;
  description: string;
  progress: number;
  contributionText: string;
  
  // Step-specific data
  understandingText: string;
  features: SummaryFeature[];
  personas: SummaryPersona[];
  swot: SummarySwotItem[];
  timeline: SummaryTimelineQuarter[];
  
  // State flags
  isLoading: boolean;
  hasData: boolean;
}

/**
 * Step completion status for UI display
 */
export interface StepSummary {
  stepName: string;
  displayName: string;
  isCompleted: boolean;
  hasData: boolean;
  dataCount: number;
  lastUpdated?: Date;
}

/**
 * Text truncation configuration
 */
export interface TruncationConfig {
  titleMaxLength: number;
  descriptionMaxLength: number;
  listItemMaxLength: number;
  ellipsis: string;
}

/**
 * Color scheme configuration for different data types
 */
export interface SummaryColorScheme {
  features: {
    must_have: string;
    should_have: string;
    could_have: string;
    wont_have: string;
  };
  swot: {
    strengths: string;
    opportunities: string;
    weaknesses: string;
    threats: string;
  };
  timeline: {
    default: string;
    completed: string;
    inProgress: string;
  };
}

/**
 * Summary service configuration
 */
export interface SummaryServiceConfig {
  truncation: TruncationConfig;
  colors: SummaryColorScheme;
  refreshInterval?: number;
  enableRealTimeUpdates: boolean;
}

/**
 * Default configuration values
 */
export const DEFAULT_TRUNCATION_CONFIG: TruncationConfig = {
  titleMaxLength: 50,
  descriptionMaxLength: 100,
  listItemMaxLength: 80,
  ellipsis: '...'
};

export const DEFAULT_COLOR_SCHEME: SummaryColorScheme = {
  features: {
    must_have: '#0F9D57',    // Green
    should_have: '#FDC100',  // Yellow
    could_have: '#FD7542',   // Orange
    wont_have: '#25364D'     // Dark Blue
  },
  swot: {
    strengths: '#0F9D57',     // Green
    opportunities: '#FDC100', // Yellow
    weaknesses: '#FD7542',    // Orange
    threats: '#FF4444'        // Red
  },
  timeline: {
    default: '#6C757D',       // Gray
    completed: '#0F9D57',     // Green
    inProgress: '#FDC100'     // Yellow
  }
};

export const DEFAULT_SUMMARY_SERVICE_CONFIG: SummaryServiceConfig = {
  truncation: DEFAULT_TRUNCATION_CONFIG,
  colors: DEFAULT_COLOR_SCHEME,
  enableRealTimeUpdates: true
};
