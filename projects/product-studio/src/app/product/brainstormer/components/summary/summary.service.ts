import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { BehaviorSubject, Observable, combineLatest, Subscription } from 'rxjs';
import { map, distinctUntilChanged, shareReplay, tap } from 'rxjs/operators';
import { AppStateService } from '../../../shared/services/app-state.service';
import { ProductPipelineService } from '../../services/product-pipeline.service';
import { PersonaDataService } from '../../services/persona-data.service';
import { RoadmapDataService } from '../../services/roadmap-data.service';
import {
  PersonaData,
  SWOTData,
  FeaturesData,
  RoadmapData,
  ProjectDetailsState
} from '../../interfaces/pipeline-api.interface';
import {
  SummaryDataState,
  SummaryPersona,
  SummaryFeature,
  SummarySwotItem,
  SummaryTimelineQuarter,
  SummaryTimelineItem
} from '../../../shared/interfaces/app-state.interface';

// Legacy type alias for backward compatibility
export type SummaryData = SummaryDataState;

@Injectable({
  providedIn: 'root'
})
export class SummaryService implements OnDestroy {
  private static readonly SESSION_STORAGE_KEY = 'summary_data';

  public projectDetails$: Observable<ProjectDetailsState>;

  private subscriptions: Subscription[] = [];

  // Track which steps have been processed to avoid duplicate updates
  private processedSteps: Set<string> = new Set();

  // Color mappings for different categories
  private readonly FEATURE_COLORS = {
    must_have: '#0F9D57',    // Green
    should_have: '#FDC100',  // Yellow
    could_have: '#FD7542',   // Orange
    wont_have: '#25364D'     // Dark Blue
  };

  private readonly SWOT_COLORS = {
    strengths: '#0F9D57',     // Green
    opportunities: '#FDC100', // Yellow
    weaknesses: '#FD7542',    // Orange
    threats: '#FF4444'        // Red
  };

  // Use app state for summary data management
  public readonly summaryData$: Observable<SummaryDataState>;

  constructor(
    private appStateService: AppStateService,
    private pipelineService: ProductPipelineService,
    private personaDataService: PersonaDataService,
    private roadmapDataService: RoadmapDataService
  ) {
    // Initialize the observable after appStateService is available
    this.summaryData$ = this.appStateService.summaryDataState$;

    this.projectDetails$ = this.appStateService.ProjectDetailsState$;

    // Initialize reactive data stream first
    this.initializeSummaryData();

    // Setup real-time step monitoring for progressive summary updates
    // this.setupStepCompletionMonitoring();

    // Setup roadmap data subscription for real-time updates
    // this.setupRoadmapDataSubscription();
    // this.forceDataRefresh();

    // Load from session storage after a small delay to ensure observables are set up
    setTimeout(() => {
      this.loadFromSessionStorage();
    }, 100);
  }

  private initializeSummaryData(): void {
    // Subscribe to pipeline state changes and transform data
    combineLatest([
      this.appStateService.pipelineState$,
      this.appStateService.loadingState$,
      this.appStateService.ProjectDetailsState$ // Add project details state to the stream
    ]).pipe(
      map(([pipelineState, loadingState, projectDetails]) => {
        console.log('🔄 [SUMMARY SERVICE] Transforming data with project details:', {
          hasProjectDetails: !!projectDetails,
          hasJustification: !!projectDetails?.justification,
          projectName: pipelineState.project_name
        });

        const summaryData: SummaryDataState = {
          name: pipelineState.project_name || '',
          description: projectDetails?.justification || '',
          hasData: !!pipelineState.data && Object.keys(pipelineState.data).length > 0,
          progress: this.calculateProgress(pipelineState),
          contributionText: this.generateContributionText(pipelineState),
          understandingText: this.extractUnderstandingText(pipelineState.data?.market_research),
          features: this.extractFeatures(pipelineState.data?.features),
          personas: this.extractPersonas(pipelineState.data?.persona),
          swot: this.extractSwotAnalysis(pipelineState.data?.swot),
          timeline: this.extractTimeline(pipelineState.data?.roadmap),
          isLoading: loadingState.isLoadingPipeline,
          lastUpdated: new Date()
        };

        // Save to session storage immediately
        this.saveToSessionStorage(summaryData);
        return summaryData;
      }),
      distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)),
      shareReplay(1),
      tap(summaryData => {
        this.saveToSessionStorage(summaryData);
      })
    ).subscribe(summaryData => {
      // Update app state
      this.appStateService.updateSummaryDataState(summaryData);
    });
  }
  /**
   * Setup real-time monitoring for step completion to build summary data progressively
   */
  // private setupStepCompletionMonitoring(): void {
  //   console.log('🔄 Setting up step completion monitoring for progressive summary updates');
  //   let previousRunId: string | null = null;
  //   // Monitor pipeline state changes for step completions
  //   const stepMonitoringSubscription = this.appStateService.pipelineState$.pipe(
  //     distinctUntilChanged((prev, curr) => {
  //       // Check if run_id changed (new project started)
  //       if (prev.run_id !== curr.run_id) {
  //         return false; // Always process when run_id changes
  //       }
  //       // Check if any step data has changed
  //       const prevDataHash = JSON.stringify(prev.data || {});
  //       const currDataHash = JSON.stringify(curr.data || {});
  //       return prevDataHash === currDataHash;
  //     })
  //   ).subscribe(pipelineState => {
  //     // Reset tracking if this is a new project
  //     if (previousRunId !== pipelineState.run_id) {
  //       console.log('🆕 New project detected, resetting step tracking');
  //       this.resetProcessedSteps();
  //       previousRunId = pipelineState.run_id;

  //       // Initialize summary data for new project
  //       this.initializeSummaryForNewProject(pipelineState);
  //     }

  //     this.handleStepCompletions(pipelineState);
  //   });

  //   this.subscriptions.push(stepMonitoringSubscription);
  // }

  /**
   * Handle step completions and update summary data progressively
   */
  private handleStepCompletions(pipelineState: any): void {
    if (!pipelineState || !pipelineState.data) {
      return;
    }

    const currentSummaryData = this.appStateService.summaryDataState;
    let hasUpdates = false;
    const updates: Partial<SummaryDataState> = {};
    const updatedSteps: string[] = [];

    // Check for LBC step completion
    if (pipelineState.data.lbc && this.shouldUpdateStep('lbc', pipelineState.data.lbc)) {
      console.log('📝 LBC step completed - Updating summary with understanding data');
      console.log('   LBC data preview:', JSON.stringify(pipelineState.data.lbc).substring(0, 200) + '...');
      updates.understandingText = this.extractUnderstandingText(pipelineState.data);
      updatedSteps.push('LBC');
      hasUpdates = true;
    }

    // Check for Persona step completion
    if (pipelineState.data.persona && this.shouldUpdateStep('persona', pipelineState.data.persona)) {
      console.log('👥 Persona step completed - Updating summary with persona data');
      console.log('   Persona count:', pipelineState.data.persona.personas?.length || 0);
      updates.personas = this.extractPersonas(pipelineState.data.persona);
      updatedSteps.push('Persona');
      hasUpdates = true;
    }

    // Check for SWOT step completion
    if (pipelineState.data.swot && this.shouldUpdateStep('swot', pipelineState.data.swot)) {
      console.log('📊 SWOT step completed - Updating summary with SWOT analysis');
      const swotData = this.extractSwotAnalysis(pipelineState.data.swot);
      console.log('   SWOT items count:', swotData.length);
      updates.swot = swotData;
      updatedSteps.push('SWOT');
      hasUpdates = true;
    }

    // Check for Features step completion
    if (pipelineState.data.features && this.shouldUpdateStep('features', pipelineState.data.features)) {
      console.log('🎯 Features step completed - Updating summary with feature list');
      const featuresData = this.extractFeatures(pipelineState.data.features);
      console.log('   Features count:', featuresData.length);
      updates.features = featuresData;
      updatedSteps.push('Features');
      hasUpdates = true;
    }

    // Check for Roadmap step completion
    if (pipelineState.data.roadmap && this.shouldUpdateStep('roadmap', pipelineState.data.roadmap)) {
      console.log('🗺️ Roadmap step completed - Updating summary with timeline data');
      const timelineData = this.extractTimeline(pipelineState.data.roadmap);
      console.log('   Timeline quarters:', timelineData.length);
      updates.timeline = timelineData;
      updatedSteps.push('Roadmap');
      hasUpdates = true;
    }

    // Update progress and other metadata
    if (hasUpdates) {
      const newProgress = this.calculateProgress(pipelineState);
      updates.progress = newProgress;
      updates.contributionText = this.generateContributionText(pipelineState);
      updates.hasData = this.hasAnyPipelineData(pipelineState.data);
      updates.name = pipelineState.project_name || currentSummaryData.name;

      console.log(`✅ Progressive summary update completed for steps: ${updatedSteps.join(', ')}`);
      console.log(`   Progress: ${currentSummaryData.progress}% → ${newProgress}%`);
      console.log(`   Updated fields: ${Object.keys(updates).join(', ')}`);

      this.appStateService.updateSummaryDataState(updates);
    }
  }

  /**
   * Determine if a step should be updated based on data avilability and changes
   */
  private shouldUpdateStep(stepName: string, stepData: any): boolean {
    if (!stepData || (typeof stepData === 'object' && Object.keys(stepData).length === 0)) {
      return false;
    }

    // Create a unique key for this step's data to track if it's been processed
    const stepKey = `${stepName}_${JSON.stringify(stepData).substring(0, 100)}`;

    if (this.processedSteps.has(stepKey)) {
      // This step data has already been processed
      return false;
    }

    // Mark this step as processed
    this.processedSteps.add(stepKey);
    return true;
  }

  /**
   * Reset processed steps tracking (useful when starting a new project)
   */
  public resetProcessedSteps(): void {
    console.log('🔄 Resetting processed steps tracking for new project');
    this.processedSteps.clear();
  }

  private transformPipelineDataToSummary(pipelineState: any, isLoading: boolean): SummaryData {
    console.log('🔄 [SUMMARY SERVICE] transformPipelineDataToSummary called');
    console.log('   isLoading:', isLoading);
    console.log('   pipelineState run_id:', pipelineState?.run_id);
    console.log('   ### pipelineState project_name:', pipelineState?.project_name);
    console.log('   pipelineState data keys:', Object.keys(pipelineState?.data || {}));

    if (isLoading || !pipelineState) {
      console.log('   ⚠️ Returning default data (loading or no pipeline state)');
      return this.getCurrentSummaryData();
    }

    // Extract project details with fallback chain
    const name = this.appStateService.projectDetailsState.project_name ||
      pipelineState.project_name ||
      '';
    const description = this.extractProjectDescription(pipelineState) || '';

    console.log('   📝 Extracted project name:', name);
    console.log('   📝 Extracted description:', description ? description.substring(0, 100) + '...' : 'No description found');
    console.log('   📝 ProjectDetailsState justification:', this.appStateService.projectDetailsState.justification ? 'Found' : 'Not found');

    const progress = this.calculateProgress(pipelineState);

    // Debug pipeline state data structure
    console.log('   🔍 Pipeline state data keys:', Object.keys(pipelineState.data || {}));
    console.log('   🔍 Features data available:', {
      'pipelineState.data.features': !!pipelineState.data?.features,
      'pipelineState.features': !!pipelineState.features,
      'features_type': typeof (pipelineState.data?.features || pipelineState.features)
    });

    const features = this.extractFeatures(pipelineState.data?.features || pipelineState.features);
    const personas = this.extractPersonas(pipelineState.data?.persona || pipelineState.persona);
    const swot = this.extractSwotAnalysis(pipelineState.data?.swot || pipelineState.swot);
    const timeline = this.extractTimeline(pipelineState.data?.roadmap || pipelineState.roadmap);

    console.log('   📊 Calculated progress:', progress + '%');
    console.log('   🎯 Features count:', features.length);
    console.log('   👥 Personas count:', personas.length);
    console.log('   📈 SWOT items count:', swot.length);
    console.log('   🗺️ Timeline quarters:', timeline.length);

    const summaryData = {
      name,
      description,
      progress,
      contributionText: this.generateContributionText(pipelineState),
      understandingText: this.extractUnderstandingText(pipelineState.data),
      features,
      personas,
      swot,
      timeline,
      isLoading: false,
      hasData: this.hasAnyPipelineData(pipelineState.data),
      lastUpdated: new Date()
    };

    console.log('   📋 ###Summary data :', summaryData);

    return summaryData;
  }

  /**
   * Check if there's any valid pipeline data
   */
  private hasAnyPipelineData(data: any): boolean {

    if (!data) {
      return false;
    }

    const dataPoints = [
      'market_research',
      'lbc',
      'persona',
      'swot',
      'features',
      'roadmap'
    ];

    const hasData = dataPoints.some(point => {
      const hasPoint = !!(data[point] && Object.keys(data[point]).length > 0);
      return hasPoint;
    });

    return hasData;
  }

  /**
   * Extract and format project description
   */
  private extractProjectDescription(pipelineState: any): string {
    // Primary source: justification from project details state
    const justification = this.appStateService.projectDetailsState.justification;

    if (justification) {
      return justification;
    }

    // Fallback sources for project description
    const fallbackDescription = pipelineState.data?.project_details?.justification ||
                               pipelineState.data?.project_details?.description ||
                               pipelineState.data?.project_details?.summary ||
                               pipelineState.justification ||
                               pipelineState.description;

    return fallbackDescription || '';
  }

  private calculateProgress(pipelineState: any): number {

    // Define the steps we're tracking
    const steps = ['market_research', 'lbc', 'persona', 'swot', 'features', 'roadmap'];
    const totalSteps = steps.length;

    // Count completed steps based on data presence
    let completedSteps = 0;
    const data = pipelineState.data || {};

    steps.forEach(step => {
      if (data[step] && Object.keys(data[step]).length > 0) {
        completedSteps++;
      }
    });

    // Calculate percentage
    const progress = Math.round((completedSteps / totalSteps) * 100);

    return progress;
  }

  private generateContributionText(pipelineState: any): string {
    const progress = this.calculateProgress(pipelineState);

    if (progress === 0) {
      return 'Ready to start your brainstorming journey!';
    } else if (progress < 50) {
      return `Great start! You've completed ${progress}% of your brainstorming session.`;
    } else if (progress < 100) {
      return `Excellent progress! You're ${progress}% through your brainstorming session.`;
    } else {
      return `Congratulations! You've completed your brainstorming session with ${progress}% progress.`;
    }
  }

  private extractUnderstandingText(data: any): string {
    const marketData = data?.market_research;
    const lbcData = data?.lbc;

    if (marketData) {
      let understanding = '';

      if (marketData.market_summary) {
        understanding += this.truncateText(marketData.market_summary, 100);
      }

      if (marketData.identified_gaps) {
        understanding += understanding ? ' ' : '';
        understanding += this.truncateText(marketData.identified_gaps, 100);
      }

      return understanding || 'Market research insights will appear here.';
    }

    if (lbcData?.problem && lbcData.problem.length > 0) {
      return this.truncateText(lbcData.problem.join('. '), 200);
    }

    return 'Understanding insights will be available after completing the market research step.';
  }

  private extractFeatures(featuresData?: FeaturesData): SummaryFeature[] {
    console.log('   🎯 Extracting features from data:', featuresData ? 'Found' : 'Not found');

    if (!featuresData) {
      console.log('   ⚠️ No features data provided');
      return [];
    }

    console.log('   📊 Features data structure:', {
      must_have: featuresData.must_have?.length || 0,
      should_have: featuresData.should_have?.length || 0,
      could_have: featuresData.could_have?.length || 0,
      wont_have: featuresData.wont_have?.length || 0
    });

    const features: SummaryFeature[] = [];

    // Extract titles from each MoSCoW category
    if (featuresData.must_have) {
      features.push(...featuresData.must_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.must_have,
        category: 'must_have' as const
      })));
    }

    if (featuresData.should_have) {
      features.push(...featuresData.should_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.should_have,
        category: 'should_have' as const
      })));
    }

    if (featuresData.could_have) {
      features.push(...featuresData.could_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.could_have,
        category: 'could_have' as const
      })));
    }

    if (featuresData.wont_have) {
      features.push(...featuresData.wont_have.map(item => ({
        name: this.truncateText(item.title, 15),
        color: this.FEATURE_COLORS.wont_have,
        category: 'wont_have' as const
      })));
    }

    console.log('   ✅ Extracted features total:', features.length);
    return features;
  }

  private extractPersonas(personaData?: PersonaData): SummaryPersona[] {
    if (!personaData?.personas) {
      return [];
    }

    return personaData.personas.slice(0, 4).map((persona, index) => {
      // Use gender-based avatar mapping from PersonaDataService
      const avatarUrl = persona.avatar ||
        this.personaDataService.getGenderBasedAvatar(
          (persona as any).gender, // Cast to access gender field
          persona.role,
          index
        );

      return {
        name: this.truncateText(persona.name, 15),
        role: this.truncateText(persona.role, 18),
        avatarUrl: avatarUrl
      };
    });
  }

  private extractSwotAnalysis(swotData?: SWOTData): SummarySwotItem[] {
    if (!swotData) {
      return [];
    }

    const swotItems: SummarySwotItem[] = [];

    if (swotData.strengths && swotData.strengths.length > 0) {
      swotItems.push({
        category: 'strengths',
        color: this.SWOT_COLORS.strengths,
        points: swotData.strengths.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }

    if (swotData.weaknesses && swotData.weaknesses.length > 0) {
      swotItems.push({
        category: 'weaknesses',
        color: this.SWOT_COLORS.weaknesses,
        points: swotData.weaknesses.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }

    if (swotData.opportunities && swotData.opportunities.length > 0) {
      swotItems.push({
        category: 'opportunities',
        color: this.SWOT_COLORS.opportunities,
        points: swotData.opportunities.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }

    if (swotData.threats && swotData.threats.length > 0) {
      swotItems.push({
        category: 'threats',
        color: this.SWOT_COLORS.threats,
        points: swotData.threats.slice(0, 2).map(item => this.truncateText(item.title, 40))
      });
    }

    return swotItems;
  }

  private extractTimeline(roadmapData?: RoadmapData): SummaryTimelineQuarter[] {
    if (!roadmapData?.project_tasks || roadmapData.project_tasks.length === 0) {
      return this.getDefaultTimeline();
    }

    // Transform roadmap data into timeline quarters
    const quarterMap = new Map<number, SummaryTimelineItem[]>();

    // Process each task from the roadmap data
    roadmapData.project_tasks.forEach((apiTask: any, index: number) => {
      const quarter = apiTask.quarter || 1; // Default to quarter 1 if not specified
      const taskName = apiTask.task || `Task ${index + 1}`;

      // Get appropriate icon based on task name/type
      const icon = this.getTaskIcon(taskName, apiTask.priority);

      const timelineItem: SummaryTimelineItem = {
        icon: icon,
        label: taskName
      };

      // Add to the appropriate quarter
      if (!quarterMap.has(quarter)) {
        quarterMap.set(quarter, []);
      }
      quarterMap.get(quarter)!.push(timelineItem);
    });

    // Convert map to SummaryTimelineQuarter array
    const timelineQuarters: SummaryTimelineQuarter[] = [];

    // Ensure we have at least 4 quarters, even if some are empty
    for (let q = 1; q <= Math.max(4, Math.max(...quarterMap.keys())); q++) {
      const quarterTasks = quarterMap.get(q) || [];

      timelineQuarters.push({
        quarter: `Quarter ${q}`,
        items: quarterTasks
      });
    }

    return timelineQuarters.length > 0 ? timelineQuarters : this.getDefaultTimeline();
  }

  private getDefaultTimeline(): SummaryTimelineQuarter[] {
    return [
      {
        quarter: 'Quarter 1',
        items: [
          { icon: 'awe_research', label: 'Market Research' },
          { icon: 'awe_planning', label: 'Project Planning' },
          { icon: 'awe_design', label: 'Initial Design' }
        ]
      },
      {
        quarter: 'Quarter 2',
        items: [
          { icon: 'awe_prototype', label: 'Prototype Development' },
          { icon: 'awe_test', label: 'Testing & Validation' }
        ]
      },
      {
        quarter: 'Quarter 3',
        items: [
          { icon: 'awe_code', label: 'Development' },
          { icon: 'awe_review', label: 'Quality Review' }
        ]
      },
      {
        quarter: 'Quarter 4',
        items: [
          { icon: 'awe_deploy', label: 'Launch Preparation' },
          { icon: 'awe_business', label: 'Market Launch' }
        ]
      }
    ];
  }

  private truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + '...';
  }

  /**
   * Get appropriate icon for a task based on its name and priority
   */
  private getTaskIcon(taskName: string, priority?: string): string {
    const lowerTaskName = taskName.toLowerCase();

    // Map task types to icons based on common keywords
    if (lowerTaskName.includes('research') || lowerTaskName.includes('analysis')) {
      return 'awe_research';
    }
    if (lowerTaskName.includes('design') || lowerTaskName.includes('ui') || lowerTaskName.includes('ux')) {
      return 'awe_design';
    }
    if (lowerTaskName.includes('develop') || lowerTaskName.includes('code') || lowerTaskName.includes('implement')) {
      return 'awe_code';
    }
    if (lowerTaskName.includes('test') || lowerTaskName.includes('qa') || lowerTaskName.includes('quality')) {
      return 'awe_test';
    }
    if (lowerTaskName.includes('deploy') || lowerTaskName.includes('launch') || lowerTaskName.includes('release')) {
      return 'awe_deploy';
    }
    if (lowerTaskName.includes('plan') || lowerTaskName.includes('strategy') || lowerTaskName.includes('roadmap')) {
      return 'awe_planning';
    }
    if (lowerTaskName.includes('prototype') || lowerTaskName.includes('mvp') || lowerTaskName.includes('demo')) {
      return 'awe_prototype';
    }
    if (lowerTaskName.includes('market') || lowerTaskName.includes('business') || lowerTaskName.includes('sales')) {
      return 'awe_business';
    }
    if (lowerTaskName.includes('document') || lowerTaskName.includes('spec') || lowerTaskName.includes('requirement')) {
      return 'awe_document';
    }
    if (lowerTaskName.includes('review') || lowerTaskName.includes('feedback') || lowerTaskName.includes('approval')) {
      return 'awe_review';
    }

    // Default icons based on priority if no keyword match
    if (priority) {
      const lowerPriority = priority.toLowerCase();
      if (lowerPriority === 'high') return 'awe_priority_high';
      if (lowerPriority === 'medium') return 'awe_priority_medium';
      if (lowerPriority === 'low') return 'awe_priority_low';
    }

    // Default fallback icon
    return 'awe_task';
  }

  /**
   * Get current summary data synchronously
   */
  getCurrentSummaryData(): SummaryData {
    return this.appStateService.summaryDataState;
  }

  /**
   * Check if specific step data is available
   */
  hasStepData(step: string): boolean {
    const currentData = this.appStateService.summaryDataState;
    switch (step) {
      case 'understanding':
        return currentData.understandingText !== 'Understanding insights will appear here.';
      case 'personas':
        return currentData.personas.length > 0;
      case 'features':
        return currentData.features.length > 0;
      case 'swot':
        return currentData.swot.length > 0;
      case 'roadmap':
        return currentData.timeline.length > 0;
      default:
        return false;
    }
  }

  /**
   * Get current project name from pipeline state
   */
  getProjectName(): string {
    return this.pipelineService.getProjectName() || '';
  }

  /**
   * Get project name as observable for reactive updates
   */
  getProjectName$(): Observable<string> {
    return this.pipelineService.getProjectName$().pipe(
      map(projectName => projectName || '')
    );
  }

  /**
   * Set project name through pipeline service
   */
  setProjectName(projectName: string): void {
    this.pipelineService.setProjectName(projectName);
  }

  /**
   * Force refresh of summary data
   * Useful when navigating to summary after completing steps
   */
  refreshSummaryData(): void {
    const currentPipelineState = this.appStateService.pipelineState;

    // Trigger pipeline state update to refresh all observables
    this.appStateService.updatePipelineState({
      ...currentPipelineState,
      lastUpdated: new Date().toISOString()
    });

  }

  /**
   * Save summary data to session storage
   */
  private saveToSessionStorage(summaryData: SummaryDataState): void {
    try {
      const dataToStore = {
        ...summaryData,
        lastUpdated: new Date()
      };
      
      console.log('💾 [SUMMARY SERVICE] Saving to session storage:', {
        hasName: !!dataToStore.name,
        hasDescription: !!dataToStore.description,
        timestamp: dataToStore.lastUpdated
      });
      
      sessionStorage.setItem(SummaryService.SESSION_STORAGE_KEY, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('❌ [SUMMARY SERVICE] Failed to save to session storage:', error);
    }
  }

  /**
   * Load summary data from session storage
   */
  private loadFromSessionStorage(): void {
    try {
      const storedData = sessionStorage.getItem(SummaryService.SESSION_STORAGE_KEY);
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        console.log('📦 [SUMMARY SERVICE] Loaded from session storage:', {
          hasName: !!parsedData.name,
          hasDescription: !!parsedData.description
        });
        
        // Update app state with stored data
        this.appStateService.updateSummaryDataState({
          ...parsedData,
          lastUpdated: new Date()
        });
      }
    } catch (error) {
      console.error('❌ [SUMMARY SERVICE] Failed to load from session storage:', error);
    }
  }

  /**
   * Clear session storage
   */
  private clearSessionStorage(): void {
    try {
      sessionStorage.removeItem(SummaryService.SESSION_STORAGE_KEY);
      console.log('🗑️ [SUMMARY SERVICE] Cleared session storage');
    } catch (error) {
      console.error('❌ [SUMMARY SERVICE] Failed to clear session storage:', error);
    }
  }

  /**
   * Get summary data from session storage (public method for components)
   */
  public getSummaryDataFromStorage(): SummaryData | null {
    try {
      const storedData = sessionStorage.getItem(SummaryService.SESSION_STORAGE_KEY);
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        const { timestamp, ...summaryData } = parsedData;
        return summaryData;
      }
    } catch (error) {
      console.error('❌ Failed to get summary data from session storage:', error);
    }
    return null;
  }

  /**
   * Force update session storage with current data
   */
  public updateSessionStorage(): void {
    const currentData = this.appStateService.summaryDataState;
    this.saveToSessionStorage(currentData);
  }

  /**
   * Force immediate data refresh and emit to subscribers
   */
  // public forceDataRefresh(): void {
  //   console.log('🔄 [SUMMARY SERVICE] Force refreshing summary data...');

  //   // Get current pipeline state
  //   const currentPipelineState = this.appStateService.pipelineState;
  //   const currentLoadingState = this.appStateService.loadingState;

  //   console.log('   📊 Pipeline state run_id:', currentPipelineState.run_id);
  //   console.log('   📊 Pipeline state project_name:', currentPipelineState.project_name);
  //   console.log('   📊 Available steps:', Object.keys(currentPipelineState.data || {}));
  //   console.log('   📊 Loading state:', currentLoadingState.isLoadingPipeline);

  //   // Transform and emit immediately
  //   const refreshedData = this.transformPipelineDataToSummary(
  //     currentPipelineState,
  //     currentLoadingState.isLoadingPipeline
  //   );

  //   console.log('   ✅ Refreshed summary data progress:', refreshedData.progress + '%');
  //   console.log('   ✅ Summary data has content:', refreshedData.hasData);
  //   console.log('   ✅ Summary data name:', refreshedData.name);

  //   // Save to session storage
  //   this.saveToSessionStorage(refreshedData);

  //   // Emit to subscribers immediately
  //   this.appStateService.updateSummaryDataState(refreshedData);

  //   console.log('✅ [SUMMARY SERVICE] Summary data refresh completed and emitted to subscribers');
  // }

  /**
   * Trigger progressive summary update for current pipeline state
   */
  public triggerProgressiveUpdate(): void {
    console.log('🔄 Triggering progressive summary update...');
    const currentPipelineState = this.appStateService.pipelineState;
    this.handleStepCompletions(currentPipelineState);
  }

  /**
   * OnDestroy implementation
   */
  ngOnDestroy(): void {
    // this.subscriptions.forEach(sub => sub.unsubscribe());
    // this.subscriptions = [];
  }
}
