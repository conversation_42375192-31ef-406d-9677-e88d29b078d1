// Pipeline API Interfaces for Product Brainstormer

// Request Interfaces
export interface PipelineStartRequest {
  user_idea: string;
  project_name: string;
  user_groups: string[];
  industry: string;
}
export interface ProjectDetailsRequest {
  prompt: string;
}

export interface ProjectDetailsResponse {
  user_idea: string;
  project_name: string;
  user_groups: string[];
  industry: string;
  justification: string;
}

export interface PipelineStepRequest {
  run_id: string;
  current_step: string;
}

// Chat API Request Interface
export interface ChatRequest {
  run_id: string;
  current_step: string;
  message: string;
}

// Enhance Prompt API Request Interface
export interface EnhancePromptRequest {
  prompt: string;
}

// Response Interfaces
export interface PipelineStartResponse {
  run_id: string;
  current_step: string; // Note: start response uses current_step
  project_name?: string;
  industry?: string;
  user_groups?: string;
}

export interface PipelineStepResponse {
  run_id: string;
  step: string;
  data: any; // This will be typed based on the specific step
}

// Chat API Response Interface
export interface ChatResponse {
  response_type: 'clarification' | 'answer' | 'error';
  message_to_user: string;
  payload: any; // Additional data if needed
}

// Enhance Prompt API Response Interface
export interface EnhancePromptResponse {
  enhancedPrompt: {
    ideaTitle: string;
    conceptOverview: string;
    problem: string;
    targetAudience: string;
    coreSolution: string;
    uniqueValueProposition: string;
    keySuccessMetric: string;
    monetizationModel: string;
  };
  clarificationNeeded: string[];
}

// Market Research Data (first step)
export interface MarketResearchData {
  market_summary: string;
  competitors: Competitor[];
  identified_gaps: string;
}

export interface Competitor {
  name: string;
  url: string;
  strengths: string;
}

// LBC (Lean Business Canvas) Data (second step)
export interface LBCData {
  problem: string[];
  solution: string[];
  key_partners: string[];
  value_proposition: string[];
  customer_segments: string[];
  revenue_streams: string[];
  key_metrics: string[];
  alternatives: string[];
  solution_tenants: string[];
  cost_structure: string[];

}

// Persona Data (third step)
export interface PersonaData {
  personas: UserPersona[];
}

export interface UserPersona {
  name: string;
  role: string;
  age: number;
  education: string;
  status: string;
  location: string;
  tech_literacy: string;
  avatar: string | null;
  quote: string;
  personality: string[];
  pain_points: string[];
  goals: string[];
  motivation: string[];
  expectations: string[];
  skills: PersonaSkill[];
  devices: string[];
}

export interface PersonaSkill {
  name: string;
  level: number;
}

// SWOT Analysis Data (fourth step)
export interface SWOTData {
  strengths: SWOTItem[];
  weaknesses: SWOTItem[];
  opportunities: SWOTItem[];
  threats: SWOTItem[];
}

export interface SWOTItem {
  title: string;
  description: string;
  justification: string;
}

// Features Data (fifth step)
export interface FeaturesData {
  must_have: FeatureItem[];
  should_have: FeatureItem[];
  could_have: FeatureItem[];
  wont_have: FeatureItem[];
}

export interface FeatureItem {
  title: string;
  description: string;
  justification: string;
}

// Roadmap Data (sixth step)
export interface RoadmapData {
  project_tasks: RoadmapProjectTask[];
}

// Individual project task from roadmap API response
export interface RoadmapProjectTask {
  task: string;
  description: string;
  long_description?: string; // Epic description for CSV export
  priority: string;
  duration: number;
  quarter: number;
}

// Epic interface for Jira export
export interface Epic {
  id: string;
  title: string;
  description: string;
  longDescription:string;
  additionalDetails: string; // alias for additional_details
}

// Jira export request interface
export interface JiraRequest {
  appType: string;
  domain: string;
  epics: Epic[];
}

// Jira export response interface
export interface JiraResponse {
  success: boolean;
  message?: string;
  ticketsCreated?: number;
  error?: string;
}

// Pipeline Step Types
export type PipelineStep = 'market_research' | 'lbc' | 'persona' | 'swot' | 'features' | 'roadmap';

export interface pipelineInitialDataState {
 run_id: string | null;
 project_name: string | null;
 industry: string | null;
 user_groups: string | null;
}

// Pipeline State Management
export interface ProjectDetailsState {
  project_name: string | null;
  industry: string | null;
  user_groups: string[] | null;
  justification: string | null;
}

export interface PipelineState {
  run_id: string | null;
  project_name: string | null;
  industry: string | null;
  user_groups: string | null;
  current_step: PipelineStep | null;
  completed_steps: PipelineStep[];
  data: {
    market_research?: MarketResearchData;
    lbc?: LBCData;
    persona?: PersonaData;
    swot?: SWOTData;
    features?: FeaturesData;
    roadmap?: RoadmapData;
  };
  lastUpdated?: string; // Optional timestamp for forcing observable updates
}

// Error Response Interface
export interface PipelineErrorResponse {
  error: string;
  message: string;
  status_code: number;
}
