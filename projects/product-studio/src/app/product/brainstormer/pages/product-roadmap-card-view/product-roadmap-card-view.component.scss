// Minimal custom SCSS - most styling handled by Bootstrap classes
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@900&display=swap");
// Section Headers

// Custom component styles with theme support
.quarter-title {
  font-size: 18px;
  font-weight: 700;
  margin: 0;
  color: var(--roadmap-card-header-title-color, #333);
}

.plus-icon-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--roadmap-card-header-title-color, #333);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--hover-bg-color, rgba(0, 0, 0, 0.1));
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.three-dot-icon-btn {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted-color, #6c757d);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--hover-bg-color, rgba(0, 0, 0, 0.1));
  }

  svg {
    width: 16px;
    height: 16px;
  }
}

.roadmap-card-header {
  background: var(--roadmap-card-header-bg, #fff);
  border-radius: 0.5rem 0.5rem 0 0;
}

.roadmap-card-body {
  background: var(--roadmap-card-bg, #fff);
  border-radius: 0 0 0.5rem 0.5rem;
}

.roadmap-card-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: var(--roadmap-card-color, #333);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.roadmap-card-description {
  font-size: 12px;
  line-height: 1.4;
  color: var(--roadmap-card-color, #666);
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  color: var(--text-color, #333);
}

.section-header {
  display: flex;
  // align-items: center;
  justify-content: space-between;
  height: auto;
  background: var(--roadmap-card-header-bg) !important;
  color: var(--roadmap-card-header-title-color);
  border-radius: 12px 12px 0px 0px; // Reduced from 16px
  // border-bottom: 3px solid #042f5a;

  .bullet-pt {
    width: 8px; // Reduced from 10px
    height: 8px; // Reduced from 10px
    // background-color: #5030e5;
    align-items: center;
    border-radius: 50%;
    margin-right: 8px; // Reduced from 10px
  }

  .section-title {
    display: flex;
    align-items: center;
    align-self: center;
    font-size: 18px; // Further reduced for more compact appearance
    font-weight: 700;
    letter-spacing: 0.3px; // Further reduced
    gap: 8px; // Increased for better spacing between title and badge

    .task-count-badge {
      background-color: #E5E5E5;
      color: #666666;
      border-radius: 8px; // Further reduced
      padding: 2px 6px; // Added explicit padding for better alignment
      font-size: 10px; // Further reduced
      font-weight: 500;
      min-width: 18px; // Further reduced
      height: 18px; // Added explicit height for consistent alignment
      text-align: center;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
  .section-action {
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--roadmap-card-header-action-bg);
    border-radius: 10px; // Further reduced
    color: var(--roadmap-card-header-action-color);
    padding: 10px; // Further reduced
    font-size: 16px; // Further reduced
    font-weight: 700;
    line-height: 1;
  }
}
.roadmap-card-item-card {
  --awe-card-border: #e9ecef;
  --awe-card-box-shadow: 0px 1px 3px -1px rgba(0, 0, 0, 0.1); // Reduced shadow
  --awe-card-border-radius: 0.5rem; // Restored for better appearance
  --awe-card-padding-base: 1rem; // Reduced from 1rem
  display: flex;
  flex-direction: column;
  font-family: "Mulish", sans-serif;
  font-weight: 600;
  color: var(--roadmap-card-color);
  font-size: 12px; // 12px as specified in requirements
  border-radius: var(--roadmap-card-border-redius);
  border: 1px solid var(--awe-card-border);
  background: var(--roadmap-card-bg);
  box-shadow: 0px 1px 3px -1px rgba(0, 0, 0, 0.1); // Reduced shadow
  cursor: grab !important;
  cursor: pointer; // Indicate clickable card

  // Header content layout
  [awe-card-header-content] {
    overflow: hidden; // Prevent content from overflowing card boundaries
    width: 100%; // Ensure full width usage
    box-sizing: border-box; // Include padding in width calculation

    .roadmap-card-tag {
      background-color: var(--roadmap-card-tag-bg);
      color: var(--roadmap-card-tag-color);
      border-radius: 3px; // Reduced from 4px
      font-size: 12px; // 12px as specified for priority indicators
      font-weight: 500;
      white-space: nowrap;
      max-width: fit-content;
    }
  }

  // Card body layout for fixed height
  [awe-card-body-content] {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; // Allow flex children to shrink
  }

  .roadmap-card-description {
    color: #555;
    font-weight: 500;
    font-size: 14px; // Further reduced for more compact appearance
    flex-grow: 1; // Take available space
    overflow: hidden; // Hide overflow
    display: -webkit-box;
    -webkit-line-clamp: 4; // Increased to 4 lines for better content visibility
    line-clamp: 4; // Standard property for compatibility
    -webkit-box-orient: vertical;
    word-break: break-word; // Handle long words
  }

  &:hover {
    --awe-card-box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1); // Reduced hover shadow
    transform: translateY(-1px); // Reduced hover lift
  }
}

:host ::ng-deep .roadmap-card-title {
  color: var(--roadmap-card-title);
  font-size: 18px; // Reduced from 16px
  line-height: 1.2; // Further reduced
  margin: 0;
  overflow: hidden;
  white-space: nowrap; // Force single line
  text-overflow: ellipsis; // Show ellipsis for overflow
  word-break: break-word;
  max-width: 100%; // Ensure title doesn't exceed container width
}

// Card header layout fixes
.min-width-0 {
  min-width: 0; // Allow flex item to shrink below content size
}

// Three-dot icon positioning
.three-dot-icon {
  cursor: pointer !important;
  padding: 12px 4px 4px 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px; // Fixed width to prevent layout shifts
  height: 24px; // Fixed height

  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

:host ::ng-deep .roadmap-card-title {
  color: var(--roadmap-card-title);
  gap: 8px;
}

// Custom dropdown styling
// Improved dropdown styling
.dropdown-arrow {
  .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 120px;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    z-index: 1000;
    border: 1px solid #dee2e6;

    &.show {
      display: block;
    }

    .dropdown-item {
      display: flex;
      width: 100%;
      padding: 0.5rem 1rem;
      clear: both;
      font-weight: 400;
      color: #212529;
      text-align: inherit;
      text-decoration: none;
      white-space: nowrap;
      background-color: transparent;
      border: 0;
      transition: background-color 0.15s ease-in-out;
      cursor: pointer;

      &:hover,
      &:focus {
        background-color: #f8f9fa;
        color: #1e2125;
      }

      &.text-danger:hover {
        background-color: #f5c2c7;
        color: #842029;
      }

      // &.border-buttom {
      //   border-bottom: 1px solid #e9ecef;
      // }
    }
  }
}

button {
  min-width: 0px;
  border: none;
  background: none;
}

.border-buttom {
  border-bottom: 1px solid #303233;
}
.three-dot-icon {
  // margin: 1.25rem;
  cursor: pointer !important;
}

.add-more-section {
  .add-more-btn {
    background-color: var(--roadmap-card-add-card-btn-bg);
    border: 1px solid var(--roadmap-card-add-card-btn-border);
    border-radius: 12px;
    color: var(--roadmap-card-add-card-btn-color);
    font-size: 16px;
    font-weight: 500;
    line-height: normal;
    padding: 0.75rem 1rem;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;

    .plus-icon {
      font-size: 20px;
      margin-left: 0.5rem;
    }
  }
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 0.5rem;
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    0 4px 10px rgba(0, 0, 0, 0.1);
  transform: rotate(2deg);
}

.cdk-drag-placeholder {
  opacity: 0.3;
  background: rgba(108, 117, 125, 0.1);
  border: 2px dashed #6c757d;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.cdk-drag-animating {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

.roadmap-card-list-dropzone.cdk-drop-list-dragging
  awe-card.roadmap-card-item-card:not(.cdk-drag-placeholder) {
  transition: transform 350ms cubic-bezier(0.25, 0.8, 0.25, 1);
}

:host ::ng-deep awe-card.cdk-drag-dragging {
  cursor: grabbing !important;
}

.edit-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.roadmap-card-tag {
  background-color: var(--roadmap-card-tag-bg);
  color: var(--roadmap-card-tag-color);
  border-radius: 4px;
}

.roadmap-card-list-dropzone {
  cursor: grab !important;
  &:active {
    cursor: grabbing !important;
  }
}

.roadmap-card-main {
  height: 100%;
  background-color: var(--roadmap-card-bg);
  border-radius: 12px; // Reduced from 16px
  border: 0.6px solid #d4d4d4;
  background: #fff;

  /* where are you? */
  box-shadow:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
}

// STYLES FOR THE MODAL CONTENT
.edit-modal-header {
  .modal-title {
    font-weight: 600;
  }
}

.edit-modal-body {
  .roadmap-card-title {
    font-weight: 500;
    color: #555;
  }

  .editable-tag-item {
    input.form-control-sm {
      border-radius: 0.5rem;
    }
    .btn-outline-danger {
      border-radius: 0.5rem;
      padding: 0.25rem 0.5rem;
      line-height: 1;
    }
  }

  .add-new-tag-btn {
    border-radius: 0.5rem;
    border-style: dashed;
    color: #6c757d;
    border-color: #ced4da;
    &:hover {
      background-color: #e9ecef;
    }
  }

  .regenerate-section {
    display:none !important;
    .form-label {
      font-size: 0.9rem;
    }
    .input-group {
      input.form-control-sm,
      button.btn-sm {
        border-radius: 0.5rem;
      }
      .btn-sm {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
      input.form-control-sm {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
    }
  }
}

.inp-container {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  // align-items: stretch;
  gap: 10px;
  margin-bottom: 0;
  .label {
    width: auto;
    display: flex;
    align-self: center;
    align-items: center;
    font-size: 1rem;
    flex-grow: 0;
  }
  .input-wrapper {
    width: auto;
    flex-grow: 1;
  }
}
:host ::ng-deep .input-container label {
  display: none;
}


button {
  border-radius: 50px;
  background: #fff;
  padding: 12px 24px;
  border: none;
  border-radius: 50px;
}
.btn-cancel {
  border: 1px solid var(--Primary-500, #7c3aed);
  background: var(--Neutral-Neutral-colors-Solid, #fff);
  margin-right: 1rem;
}
.btn-delete {
  background: var(--Primary-500, #7c3aed);
  border: 1px solid var(--Primary-500, #7c3aed);
  color: #fff;
}

// Horizontal scrolling styles for roadmap
.roadmap-horizontal-wrapper {
  width: 100%;
}

.scroll-container-wrapper {
  position: relative;
  width: 100%;
}

/* Minimal Scroll Arrow Buttons */
.scroll-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 100;
  padding: 0.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(124, 58, 237, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;

  .arrow-icon {
    width: 1.1rem;
    height: 1.1rem;
    color: #7c3aed;
    transition: color 0.2s ease;
  }

  &:hover:not(.disabled) {
    background: rgba(124, 58, 237, 0.1);
    border-color: rgba(124, 58, 237, 0.6);
    box-shadow: 0 6px 16px rgba(124, 58, 237, 0.2);
    transform: translateY(-50%) scale(1.05);

    .arrow-icon {
      color: #6d28d9;
    }
  }

  &.disabled {
    background: rgba(243, 244, 246, 0.7);
    cursor: not-allowed;
    opacity: 0.5;

    .arrow-icon {
      color: #d1d5db;
    }

    &:hover {
      background: rgba(243, 244, 246, 0.7);
      border-color: rgba(229, 231, 235, 0.6);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

.scroll-arrow-left {
  left: 10px;
  z-index: 101;
}

.scroll-arrow-right {
  right: 10px;
  z-index: 101;
}

.roadmap-horizontal-container {
  display: flex;
  gap: 1.5rem; // Increased gap for better spacing between wider cards
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  // Enable smooth touch scrolling on mobile
  -webkit-overflow-scrolling: touch;
}

.roadmap-quarter-column {
  flex: 0 0 380px; // Further increased to accommodate wider cards
  min-width: 380px; // Further increased to accommodate wider cards
  max-width: 380px; // Further increased to accommodate wider cards
}

// Responsive adjustments for horizontal scrolling
@media (max-width: 1200px) {
  .roadmap-quarter-column {
    flex: 0 0 350px; // Further increased to accommodate wider cards
    min-width: 350px; // Further increased to accommodate wider cards
    max-width: 350px; // Further increased to accommodate wider cards
  }
}

@media (max-width: 992px) {
  .roadmap-quarter-column {
    flex: 0 0 330px; // Further increased to accommodate wider cards
    min-width: 330px; // Further increased to accommodate wider cards
    max-width: 330px; // Further increased to accommodate wider cards
  }
}

@media (max-width: 768px) {
  .roadmap-horizontal-container {
    gap: 1rem; // Restored for better spacing
  }

  .roadmap-quarter-column {
    flex: 0 0 310px; // Further increased to accommodate wider cards
    min-width: 310px; // Further increased to accommodate wider cards
    max-width: 310px; // Further increased to accommodate wider cards
  }
}

@media (max-width: 576px) {
  .roadmap-horizontal-container {
    gap: 0.75rem; // Restored for better spacing
  }

  .roadmap-quarter-column {
    flex: 0 0 290px; // Further increased to accommodate wider cards
    min-width: 290px; // Further increased to accommodate wider cards
    max-width: 290px; // Further increased to accommodate wider cards
  }

  // Adjust card height for mobile
  .roadmap-card-item-card {
    height: 140px; // Adjusted for mobile with 4-line description
    min-height: 140px;
  }
}

// Card Detail Modal Styles
.card-detail-content {
  .form-label {
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 0.25rem;
  }

  p {
    font-size: 0.875rem;
    line-height: 1.4;
    color: #6c757d;
  }

  .roadmap-card-tag {
    font-size: 0.75rem;
    font-weight: 500;
  }
}


