import {
  Component,
  OnInit,
  OnDestroy,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  PersonaDataService,
  PersonaData,
  PersonaCard,
} from '../../services/persona-data.service';
import { AweCardComponent } from '../../components/awe-card/awe-card.component';
import { AweModalComponent } from '../../components/awe-modal/awe-modal.component';
import {
  HeadingComponent,
  InputComponent,
} from '@awe/play-comp-library';
import { IconsComponent } from '../../../../shared/components/icons/icons.component';
import { ProductPipelineService } from '../../services/product-pipeline.service';

@Component({
  selector: 'app-user-persona',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AweCardComponent,
    AweModalComponent,
    HeadingComponent,
    IconsComponent,
    InputComponent,
  ],
  templateUrl: './user-persona.component.html',
  styleUrls: ['./user-persona.component.scss'],
})
export class UserPersonaComponent implements OnInit, OnDestroy, AfterViewInit {
  @Output() personaSelected = new EventEmitter<string>();
  @ViewChild('scrollContainer', { static: false }) scrollContainer!: ElementRef;

  colonIcon: string = 'svgs/colon.svg';
  awe_delete: string = 'icons/awe-delete.svg';

  // Horizontal scroll state
  canScrollLeft = false;
  canScrollRight = true;

  isModalOpen = false;
  modalMode: 'add' | 'edit' = 'add';
  selectedCardForEdit: PersonaCard | null = null;
  editData: any = {};
  regeneratePrompt: string = '';

  personas: PersonaData[] = [];
  private subscription = new Subscription();

  isDeleteModalOpen = false;
  personaToDelete: PersonaData | null = null;

  // Accordion State
  activeAccordion: string | null = null;

  constructor(
    private personaDataService: PersonaDataService,
    private pipelineService: ProductPipelineService
  ) {}

  ngOnInit(): void {
    this.subscription.add(
      this.personaDataService.personas$.subscribe((personas) => {
        this.personas = personas;
      }),
    );

    // Subscribe to pipeline state changes to load persona data from API
    this.subscription.add(
      this.pipelineService.pipelineState$.subscribe((state) => {

        // Load persona data whenever it's available (regardless of current_step)
        // This ensures data restoration after page reload
        if (state.data.persona) {
          const personaData = state.data.persona as any;

          // Check for structured persona data with personas array
          if (personaData.personas && Array.isArray(personaData.personas)) {
            this.personaDataService.updatePersonasFromAPI(personaData.personas);
          }
          // Check for legacy persona data structure (direct array)
          else if (Array.isArray(state.data.persona)) {
            this.personaDataService.updatePersonasFromAPI(state.data.persona);
          }
        }
      })
    );
  }

  ngAfterViewInit(): void {
    // Set up scroll event listener after view initialization
    setTimeout(() => {
      this.checkScrollButtons();
      if (this.scrollContainer?.nativeElement) {
        this.scrollContainer.nativeElement.addEventListener('scroll', () => {
          this.checkScrollButtons();
        });
      }
    }, 100);
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    // Clean up scroll event listener
    if (this.scrollContainer?.nativeElement) {
      this.scrollContainer.nativeElement.removeEventListener('scroll', () => {
        this.checkScrollButtons();
      });
    }
  }

  // Horizontal scroll methods
  scrollLeft(): void {
    if (this.scrollContainer?.nativeElement) {
      this.scrollContainer.nativeElement.scrollBy({
        left: -300,
        behavior: 'smooth'
      });
    }
  }

  scrollRight(): void {
    if (this.scrollContainer?.nativeElement) {
      this.scrollContainer.nativeElement.scrollBy({
        left: 300,
        behavior: 'smooth'
      });
    }
  }

  checkScrollButtons(): void {
    if (this.scrollContainer?.nativeElement) {
      const { scrollLeft, scrollWidth, clientWidth } = this.scrollContainer.nativeElement;
      this.canScrollLeft = scrollLeft > 0;
      this.canScrollRight = scrollLeft < scrollWidth - clientWidth - 1;
    }
  }

  openAddModal(): void {
    this.modalMode = 'add';
    this.selectedCardForEdit = {
      id: 'new-persona-profile',
      title: 'Profile',
      type: 'profile',
      data: {},
    };
    this.setupEditDataForAdd();
    this.isModalOpen = true;
  }

  closeModal(): void {
    this.isModalOpen = false;
    this.selectedCardForEdit = null;
    this.editData = {};
    this.regeneratePrompt = '';
  }

  saveCardData(): void {
    if (!this.selectedCardForEdit) return;

    if (this.modalMode === 'add') {
      // Validate required fields
      if (!this.isValidPersonaData()) {
        console.warn('Cannot save persona: Required fields are missing');
        return;
      }

      const personalityArray = Array.isArray(this.editData.personality)
        ? this.editData.personality
        : [];

      const newPersonaData =
        this.personaDataService.convertUserPersonaToPersonaData({
          ...this.editData,
          personality: personalityArray,
          avatar: this.personaDataService.getDefaultAvatar(
            this.editData.role || '',
          ),
        });
      this.personaDataService.addPersona(newPersonaData);
    }
    this.closeModal();
  }

  // Validation method to check if persona data is valid
  isValidPersonaData(): boolean {
    if (!this.editData) return false;

    // Check required fields
    const requiredFields = ['name', 'role'];
    const hasRequiredFields = requiredFields.every(field =>
      this.editData[field] && this.editData[field].trim().length > 0
    );

    // Check if at least one optional field has content
    const optionalFields = ['age', 'education', 'status', 'location', 'quote'];
    const hasOptionalContent = optionalFields.some(field =>
      this.editData[field] && this.editData[field].trim().length > 0
    );

    // Check if personality array has content
    const hasPersonality = Array.isArray(this.editData.personality) &&
                           this.editData.personality.length > 0 &&
                           this.editData.personality.some((trait: any) => trait && trait.trim().length > 0);

    return hasRequiredFields && (hasOptionalContent || hasPersonality);
  }

  openDeleteModal(persona: PersonaData): void {
    this.isDeleteModalOpen = true;
    this.personaToDelete = persona;
  }

  closeDeleteModal(): void {
    this.isDeleteModalOpen = false;
    this.personaToDelete = null;
  }

  confirmDeletePersona(): void {
    if (this.personaToDelete) {
      this.personaDataService.deletePersona(this.personaToDelete.id);
    }
    this.closeDeleteModal();
  }

  onPersonaClick(persona: PersonaData): void {
    this.personaDataService.setSelectedPersona(persona.id);
    this.personaSelected.emit(persona.id);
  }

  trackByPersona(_index: number, persona: PersonaData): string {
    return persona.id;
  }

  // Modal Helper Methods
  private setupEditDataForAdd(): void {
    this.editData = {
      name: '',
      role: '',
      age: '',
      education: '',
      status: '',
      location: '',
      techLiteracy: 'Medium',
      quote: '',
      avatar: '',
      personality: [],
    };
  }

  isArrayData(data: any): boolean {
    return Array.isArray(data);
  }

  trackByIndex(index: number): number {
    return index;
  }

  // Accordion methods for inline expansion
  toggleAccordion(accordionId: string): void {
    this.activeAccordion = this.activeAccordion === accordionId ? null : accordionId;
  }

  isAccordionActive(accordionId: string): boolean {
    return this.activeAccordion === accordionId;
  }

  clearAccordionSelection(): void {
    this.activeAccordion = null;
  }

  // Get first item from array for accordion display
  getFirstItem(items: string[]): string {
    return items && items.length > 0 ? items[0] : 'No items available';
  }

  addArrayItem(): void {
    if (!this.selectedCardForEdit) return;

    if (this.selectedCardForEdit.type === 'skills') {
      if (!Array.isArray(this.editData)) this.editData = [];
      this.editData.push({ name: '', level: 50 });
    } else if (Array.isArray(this.editData)) {
      this.editData.push('');
    }
  }

  removeArrayItem(index: number): void {
    if (
      Array.isArray(this.editData) &&
      index >= 0 &&
      index < this.editData.length
    ) {
      this.editData.splice(index, 1);
    }
  }
}
