import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AppStateService } from '../../../shared/services/app-state.service';
import { RoadmapProjectTask, RoadmapData } from '../../interfaces/pipeline-api.interface';

export interface CsvExportOptions {
  filename?: string;
  includeHeaders?: boolean;
  delimiter?: string;
}

export interface CsvExportResult {
  success: boolean;
  filename: string;
  rowCount: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class CsvExportService {

  constructor(private appStateService: AppStateService) { }

  /**
   * Export roadmap data to CSV format using pipeline state data
   * @param options - Export configuration options
   * @returns Observable with export result
   */
  exportRoadmapToCsv(
    options: CsvExportOptions = {}
  ): Observable<CsvExportResult> {
    try {

      // Get current pipeline state
      const pipelineState = this.appStateService.pipelineState;

      // Get roadmap data from pipeline state
      const roadmapData = pipelineState.data.roadmap;

      if (!roadmapData || !roadmapData.project_tasks || roadmapData.project_tasks.length === 0) {
        console.error('❌ No roadmap data found in pipeline state');
        return throwError(() => new Error('No roadmap data available to export. Please complete the roadmap step first.'));
      }


      const defaultOptions: CsvExportOptions = {
        filename: 'roadmap-epics.csv',
        includeHeaders: true,
        delimiter: ',',
        ...options
      };

      // Extract and format data for CSV
      const csvData = this.formatRoadmapDataForCsv(roadmapData.project_tasks, defaultOptions);

      // Generate and download CSV file
      this.downloadCsvFile(csvData, defaultOptions.filename!);

      const result: CsvExportResult = {
        success: true,
        filename: defaultOptions.filename!,
        rowCount: roadmapData.project_tasks.length
      };

      return of(result);

    } catch (error) {
      console.error('❌ CSV Export Error:', error);
      return throwError(() => error);
    }
  }

  /**
   * Format roadmap project tasks data into CSV format
   * @param projectTasks - Array of project tasks from roadmap API
   * @param options - Export options
   * @returns Formatted CSV string
   */
  private formatRoadmapDataForCsv(
    projectTasks: RoadmapProjectTask[], 
    options: CsvExportOptions
  ): string {
    const rows: string[] = [];

    // Add headers if requested
    if (options.includeHeaders) {
      const headers = ['Title', 'Description'];
      rows.push(this.formatCsvRow(headers, options.delimiter!));
    }

    // Process each project task
    projectTasks.forEach((task, index) => {
      try {
        const title = this.sanitizeForCsv(task.task || `Task ${index + 1}`);
        const description = this.sanitizeForCsv(
          task.long_description || task.description || 'No description available'
        );

        const row = [title, description];
        rows.push(this.formatCsvRow(row, options.delimiter!));
      } catch (error) {
        console.warn(`⚠️ Error processing task ${index + 1}:`, error);
        // Continue with next task instead of failing entire export
      }
    });

    return rows.join('\n');
  }

  /**
   * Format a single CSV row with proper escaping
   * @param values - Array of values for the row
   * @param delimiter - CSV delimiter character
   * @returns Formatted CSV row string
   */
  private formatCsvRow(values: string[], delimiter: string): string {
    return values
      .map(value => this.escapeCsvValue(value, delimiter))
      .join(delimiter);
  }

  /**
   * Escape and format a single CSV value
   * @param value - The value to escape
   * @param delimiter - CSV delimiter character
   * @returns Properly escaped CSV value
   */
  private escapeCsvValue(value: string, delimiter: string): string {
    if (!value) return '""';

    // Convert to string and handle null/undefined
    const stringValue = String(value);

    // Check if value needs to be quoted
    const needsQuoting = stringValue.includes(delimiter) || 
                        stringValue.includes('"') || 
                        stringValue.includes('\n') || 
                        stringValue.includes('\r');

    if (needsQuoting) {
      // Escape existing quotes by doubling them
      const escapedValue = stringValue.replace(/"/g, '""');
      return `"${escapedValue}"`;
    }

    return stringValue;
  }

  /**
   * Sanitize text content for CSV export
   * @param text - Text to sanitize
   * @returns Sanitized text
   */
  private sanitizeForCsv(text: string): string {
    if (!text) return '';

    return String(text)
      // Normalize line breaks
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Remove excessive whitespace while preserving intentional formatting
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      .trim();
  }

  /**
   * Download CSV content as a file
   * @param csvContent - The CSV content string
   * @param filename - Name of the file to download
   */
  private downloadCsvFile(csvContent: string, filename: string): void {
    try {
      // Create blob with proper CSV MIME type and UTF-8 encoding
      const blob = new Blob(['\ufeff' + csvContent], { 
        type: 'text/csv;charset=utf-8;' 
      });

      // Create download link
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up object URL
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('❌ Error downloading CSV file:', error);
      throw new Error('Failed to download CSV file');
    }
  }

  /**
   * Validate CSV export data
   * @param data - Data to validate
   * @returns Observable indicating validation result
   */
  private validateExportData(data: any): Observable<boolean> {
    if (!data) {
      return throwError(() => new Error('No data provided for export'));
    }

    if (!data.project_tasks || !Array.isArray(data.project_tasks)) {
      return throwError(() => new Error('Invalid roadmap data structure'));
    }

    if (data.project_tasks.length === 0) {
      return throwError(() => new Error('No project tasks found to export'));
    }

    return of(true);
  }
}
