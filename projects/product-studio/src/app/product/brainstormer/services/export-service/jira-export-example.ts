// /**
//  * Complete example of Jira export functionality
//  * This demonstrates how to transform project_tasks to Epic format and export to Jira
//  */

// import { JiraExportService } from './jira-export.service';
// import { RoadmapDataService } from './roadmap-data.service';
// import { Epic, JiraRequest, RoadmapData, RoadmapProjectTask } from '../interfaces/pipeline-api.interface';
// import { createEpicsFromProjectTasks, createJiraPayload } from '../utils/roadmap-transform.utils';

// // Your sample project_tasks data
// const sampleProjectTasks: RoadmapProjectTask[] = [
//   {
//     task: "Q1: UI/UX Design Phase (MVP)",
//     description: "Design all user flows, wireframes, and high-fidelity mockups for the initial MVP launch, including the User Mobile App, Coffee Shop Owner Portal, and Internal Admin Dashboard.",
//     long_description: "### Epic: MVP Design System & User Experience\n\n**Goal:** To create a comprehensive, intuitive, and visually appealing design for all core components of the platform required for a successful Q2 launch.\n\n**Scope:**\n- **User Mobile App (iOS & Android):**\n  - Onboarding and user profile creation.\n  - Home screen with mood/taste input for the Matching Engine.\n  - Search results view with map and list layouts.\n  - Advanced Search & Contextual Filters interface.\n  - Coffee Shop Profile page.\n  - Structured Review Submission flow.\n- **Coffee Shop Owner Portal (Web):**\n  - Business claiming and verification flow.\n  - Dashboard for managing profile information (hours, address, photos, amenities).\n- **Internal Admin Dashboard (Web):**\n  - Interface for Data Moderation & Curation.\n\n**Acceptance Criteria:**\n- All user flows are mapped and approved.\n- Wireframes for all screens are completed.\n- A high-fidelity, clickable prototype is available for stakeholder review.\n- A component library/design system is established for development.",
//     priority: "high",
//     duration: 40,
//     quarter: 1
//   },
//   {
//     task: "Q2: Backend API Development",
//     description: "Develop core API endpoints and database schema",
//     long_description: "### Epic: Core Backend Infrastructure\n\n**Goal:** Implement the foundational backend services required for MVP launch.\n\n**Scope:**\n- RESTful API endpoints for user management\n- Coffee shop data management APIs\n- Review and rating system APIs\n- Search and filtering APIs\n- Authentication and authorization\n- Database schema design and implementation\n\n**Acceptance Criteria:**\n- All API endpoints are documented and tested\n- Database schema is optimized for performance\n- Authentication system is secure and scalable\n- API rate limiting and monitoring in place",
//     priority: "high",
//     duration: 60,
//     quarter: 2
//   },
//   {
//     task: "Q3: Mobile App Development",
//     description: "Build native mobile applications for iOS and Android",
//     long_description: "### Epic: Mobile Application Development\n\n**Goal:** Create native mobile apps that provide excellent user experience.\n\n**Scope:**\n- iOS app development using Swift/SwiftUI\n- Android app development using Kotlin\n- Integration with backend APIs\n- Push notification system\n- Offline functionality for core features\n- App store optimization and deployment\n\n**Acceptance Criteria:**\n- Apps pass all platform-specific guidelines\n- Performance metrics meet target benchmarks\n- User testing shows high satisfaction scores\n- Apps are successfully published to stores",
//     priority: "medium",
//     duration: 90,
//     quarter: 3
//   }
// ];

// /**
//  * Example 1: Direct transformation using utility function
//  */
// export function directTransformationExample() {
  
//   // Transform project tasks to epics
//   const epics = createEpicsFromProjectTasks(sampleProjectTasks);
  
//   epics.forEach((epic, index) => {
//       id: epic.id,
//       title: epic.title,
//       description: epic.description.substring(0, 100) + '...',
//       additionalDetails: epic.additionalDetails
//     });
//   });

//   // Create Jira payload
//   const jiraPayload = createJiraPayload(sampleProjectTasks, 'coffee-finder-app', 'product-development');
  
//     appType: jiraPayload.appType,
//     domain: jiraPayload.domain,
//     epicsCount: jiraPayload.epics.length
//   });

//   return { epics, jiraPayload };
// }

// /**
//  * Example 2: Using JiraExportService directly
//  */
// export function jiraServiceExample() {
  
//   const jiraExportService = new JiraExportService(null as any); // Mock HttpClient for example
  
//   const roadmapData: RoadmapData = {
//     project_tasks: sampleProjectTasks
//   };

//   // Transform to epics
//   const epics = jiraExportService.transformProjectTasksToEpics(roadmapData.project_tasks);
  
//   // Validate epics
//   const isValid = jiraExportService.validateEpics(epics);

//   // Get preview
//   const preview = jiraExportService.getJiraPreview(roadmapData);
//     id: epic.id,
//     title: epic.title,
//     priority: epic.additionalDetails
//   })));

//   return { epics, isValid, preview };
// }

// /**
//  * Example 3: Component integration pattern
//  */
// export class ExampleJiraExportComponent {
//   constructor(private roadmapDataService: RoadmapDataService) {}

//   /**
//    * Export to Jira with user confirmation
//    */
//   exportToJira(): void {

//     // Check if data is available
//     if (!this.roadmapDataService.hasDataForExport()) {
//       console.warn('⚠️ No roadmap data available');
//       alert('No roadmap data available to export. Please complete the roadmap step first.');
//       return;
//     }

//     // Get preview of what will be created
//     const epics = this.roadmapDataService.getEpicsFromRoadmap();

//     // Show confirmation dialog with details
//     const epicsList = epics.map(epic => `• ${epic.title} (${epic.additionalDetails} priority)`).join('\n');
//     const confirmMessage = `This will create ${epics.length} Jira tickets:\n\n${epicsList}\n\nContinue?`;
    
//     if (!confirm(confirmMessage)) {
//       return;
//     }

//     // Perform export
//     this.roadmapDataService.exportRoadmapToJira('coffee-finder-app', 'product-roadmap').subscribe({
//       next: (result) => {
        
//         if (result.success) {
//           const message = `Successfully created ${result.ticketsCreated || epics.length} Jira tickets!`;
//           alert(message);
          
//           // Optional: Navigate to Jira or show success page
//           // this.router.navigate(['/jira-success']);
//         } else {
//           alert(`Jira export completed with message: ${result.message}`);
//         }
//       },
//       error: (error) => {
//         console.error('❌ Jira export failed:', error);
        
//         const errorMessage = error.error || error.message || 'Failed to export to Jira. Please try again.';
//         alert(`Export failed: ${errorMessage}`);
        
//         // Optional: Show detailed error dialog
//         // this.showErrorDialog(error);
//       }
//     });
//   }

//   /**
//    * Get preview without exporting
//    */
//   previewJiraTickets(): Epic[] {
//     return this.roadmapDataService.getJiraPreview();
//   }

//   /**
//    * Check if export is available
//    */
//   canExportToJira(): boolean {
//     return this.roadmapDataService.hasDataForExport();
//   }
// }

// /**
//  * Example 4: Testing the transformation
//  */
// export function testTransformation() {

//   // Test with your sample data
//   const epics = createEpicsFromProjectTasks(sampleProjectTasks);

//   // Verify the transformation

//   // Check each epic
//   epics.forEach((epic, index) => {
//     const originalTask = sampleProjectTasks[index];
    
    
//     // Verify mapping
//   });

//   return epics;
// }

// /**
//  * Example 5: Mock API call demonstration
//  */
// export function mockApiCallExample() {

//   const jiraPayload: JiraRequest = createJiraPayload(sampleProjectTasks);

//   // Simulate what the API call would look like

//   // Simulate response
//   const mockResponse = {
//     success: true,
//     message: `Successfully created ${jiraPayload.epics.length} Jira tickets`,
//     ticketsCreated: jiraPayload.epics.length,
//     ticketIds: jiraPayload.epics.map(epic => `PROJ-${epic.id}`)
//   };


//   return { request: jiraPayload, response: mockResponse };
// }

// // Run all examples
// export function runAllExamples() {

//   directTransformationExample();

//   jiraServiceExample();

//   testTransformation();

//   mockApiCallExample();
// }
