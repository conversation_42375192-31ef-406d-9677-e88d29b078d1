import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { AppStateService } from '../../../shared/services/app-state.service';
import {
  Epic,
  JiraRequest,
  JiraResponse,
  RoadmapData,
  RoadmapProjectTask
} from '../../interfaces/pipeline-api.interface';
import { ProductPipelineService } from '../product-pipeline.service';

export interface JiraExportOptions {
  appType?: string;
  domain?: string;
}

@Injectable({
  providedIn: 'root'
})
export class JiraExportService {

  private readonly httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };

  constructor(
    private http: HttpClient,
    private appStateService: AppStateService,
    private productPipelineService: ProductPipelineService
  ) {

    // get data from the pipeline state
    this.appStateService.pipelineState$.subscribe((state) => {
      });
   }

  /**
   * Transform project tasks into Epic format for Jira export
   * @param projectTasks - Array of project tasks from roadmap API
   * @returns Array of Epic objects
   */
  transformProjectTasksToEpics(projectTasks: RoadmapProjectTask[]): Epic[] {
    if (!projectTasks || projectTasks.length === 0) {
      return [];
    }

    return projectTasks.map((task, index) => ({
      id: (index + 1).toString(), // Use 1-based index as string
      title: task.task || `Task ${index + 1}`,
      longDescription: task.long_description || task.description || 'No description available',
      description: task.description || 'No description available',
      additionalDetails: task.priority || 'medium'
    }));
  }

  /**
   * Export roadmap data to Jira using pipeline state data
   * @returns Observable with Jira export result
   */
  exportRoadmapToJira(): Observable<JiraResponse> {
    try {

      // Get current pipeline state
      const pipelineState = this.appStateService.pipelineState;
      const projectdetails= this.productPipelineService.getProjectDetails();

      // Get roadmap data from pipeline statez
      const roadmapData = pipelineState.data.roadmap;

      if (!roadmapData || !roadmapData.project_tasks || roadmapData.project_tasks.length === 0) {
        console.error('❌ No roadmap data found in pipeline state');
        return throwError(() => new Error('No roadmap data available to export to Jira. Please complete the roadmap step first.'));
      }


      // Transform project tasks to epics
      const epics = this.transformProjectTasksToEpics(roadmapData.project_tasks);

      // Get industry from pipeline state for domain
      // const industry = 
    

      // Create Jira request payload
      const jiraPayload: JiraRequest = {
        appType: 'WEB',
        domain: 'Retail',
        epics: epics
      };


      // Make API call to Jira export endpoint
      return this.callJiraExportAPI(jiraPayload);

    } catch (error) {
      console.error('❌ Jira Export Error:', error);
      return throwError(() => error);
    }
  }

  /**
   * Call the Jira export API
   * @param jiraPayload - The Jira request payload
   * @returns Observable with Jira response
   */
  private callJiraExportAPI(jiraPayload: JiraRequest): Observable<JiraResponse> {
    // Production Jira API endpoint
    const url = 'https://avaplus-prod.avateam.io/alm/issue';


    return this.http.post<JiraResponse>(url, jiraPayload, this.httpOptions)
      .pipe(
        map((response) => {
          return response;
        }),
        catchError((error) => {
          console.error('❌ Jira export API call failed:', error);
          return this.handleJiraError(error);
        })
      );
  }



  /**
   * Handle Jira API errors
   * @param error - HTTP error response
   * @returns Observable error
   */
  private handleJiraError(error: any): Observable<never> {
    let errorMessage = 'Failed to export to Jira';

    if (error.error?.message) {
      errorMessage = error.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    } else if (error.status) {
      switch (error.status) {
        case 401:
          errorMessage = 'Unauthorized: Please check your Jira credentials';
          break;
        case 403:
          errorMessage = 'Forbidden: Insufficient permissions to create Jira tickets';
          break;
        case 404:
          errorMessage = 'Jira API endpoint not found';
          break;
        case 500:
          errorMessage = 'Internal server error: Please try again later';
          break;
        default:
          errorMessage = `HTTP ${error.status}: ${error.statusText || 'Unknown error'}`;
      }
    }

    const jiraError: JiraResponse = {
      success: false,
      error: errorMessage
    };

    return throwError(() => jiraError);
  }

  /**
   * Validate Epic data before export
   * @param epics - Array of Epic objects to validate
   * @returns boolean indicating if data is valid
   */
  validateEpics(epics: Epic[]): boolean {
    if (!epics || epics.length === 0) {
      return false;
    }

    return epics.every(epic => 
      epic.id && 
      epic.title && 
      epic.title.trim().length > 0 &&
      epic.description &&
      epic.description.trim().length > 0
    );
  }

  /**
   * Get preview of epics that would be created in Jira
   * @param roadmapData - The roadmap data
   * @returns Array of Epic objects for preview
   */
  getJiraPreview(roadmapData: RoadmapData): Epic[] {
    if (!roadmapData || !roadmapData.project_tasks) {
      return [];
    }

    return this.transformProjectTasksToEpics(roadmapData.project_tasks);
  }
}
