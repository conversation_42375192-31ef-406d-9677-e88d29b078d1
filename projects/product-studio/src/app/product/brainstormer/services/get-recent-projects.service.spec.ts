import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { GetRecentProjectsService, ProjectListItem, ProjectDetailsResponse } from './get-recent-projects.service';
import { AppStateService } from '../../shared/services/app-state.service';
import { environment } from '../../../../environments/environment';

describe('GetRecentProjectsService', () => {
  let service: GetRecentProjectsService;
  let httpMock: HttpTestingController;
  let mockAppStateService: jasmine.SpyObj<AppStateService>;

  const mockProjectList: ProjectListItem[] = [
    {
      id: '1',
      run_id: 'run-123',
      name: 'Test Project 1',
      description: 'A test project',
      created_by: '<EMAIL>',
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-01-15T12:00:00Z',
      status: 'completed',
      tags: ['test', 'demo']
    },
    {
      id: '2',
      run_id: 'run-456',
      name: 'Test Project 2',
      description: 'Another test project',
      created_by: '<EMAIL>',
      created_at: '2024-01-14T10:00:00Z',
      updated_at: '2024-01-14T12:00:00Z',
      status: 'in_progress',
      tags: ['test']
    }
  ];

  const mockProjectDetails: ProjectDetailsResponse = {
    id: '1',
    run_id: 'run-123',
    name: 'Test Project 1',
    description: 'A test project',
    created_by: '<EMAIL>',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T12:00:00Z',
    status: 'completed',
    tags: ['test', 'demo'],
    metadata: {
      industry: 'Technology',
      user_groups: ['Developers', 'Designers']
    },
    market_research: { data: 'market research data' },
    lean_business_canvas: { data: 'lbc data' },
    user_personas: {
      personas: [{ name: 'Test Persona', description: 'A test persona' }]
    },
    swot_analysis: { data: 'swot data' },
    features: { data: 'features data' },
    roadmap_tasks: [
      { task: 'Task 1', description: 'First task' },
      { task: 'Task 2', description: 'Second task' }
    ],
    conversations: []
  };

  beforeEach(() => {
    const appStateServiceSpy = jasmine.createSpyObj('AppStateService', ['updatePipelineState']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        GetRecentProjectsService,
        { provide: AppStateService, useValue: appStateServiceSpy }
      ]
    });

    service = TestBed.inject(GetRecentProjectsService);
    httpMock = TestBed.inject(HttpTestingController);
    mockAppStateService = TestBed.inject(AppStateService) as jasmine.SpyObj<AppStateService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getRecentProjects', () => {
    it('should retrieve recent projects', () => {
      const mockResponse = {
        projects: mockProjectList,
        total: 2,
        page: 1,
        limit: 10
      };

      service.getRecentProjects().subscribe(projects => {
        expect(projects).toEqual(mockProjectList);
        expect(projects.length).toBe(2);
        expect(projects[0].name).toBe('Test Project 1');
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/project/list`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);
    });

    it('should handle error when fetching projects', () => {
      service.getRecentProjects().subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.message).toContain('Server Error');
        }
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/project/list`);
      req.flush('Error', { status: 500, statusText: 'Internal Server Error' });
    });
  });

  describe('getProjectDetails', () => {
    it('should retrieve project details', () => {
      const runId = 'run-123';

      service.getProjectDetails(runId).subscribe(details => {
        expect(details).toEqual(mockProjectDetails);
        expect(details.run_id).toBe(runId);
        expect(details.name).toBe('Test Project 1');
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/project/details`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({ run_id: runId });
      req.flush(mockProjectDetails);
    });

    it('should handle error when fetching project details', () => {
      const runId = 'run-123';

      service.getProjectDetails(runId).subscribe({
        next: () => fail('should have failed'),
        error: (error) => {
          expect(error.message).toContain('Server Error');
        }
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/project/details`);
      req.flush('Not Found', { status: 404, statusText: 'Not Found' });
    });
  });

  describe('loadProject', () => {
    it('should load project and update app state', () => {
      const runId = 'run-123';

      service.loadProject(runId).subscribe(details => {
        expect(details).toEqual(mockProjectDetails);
        expect(mockAppStateService.updatePipelineState).toHaveBeenCalled();
        
        const updateCall = mockAppStateService.updatePipelineState.calls.mostRecent();
        const pipelineState = updateCall.args[0];
        
        expect(pipelineState.run_id).toBe(runId);
        expect(pipelineState.project_name).toBe('Test Project 1');
        expect(pipelineState.industry).toBe('Technology');
        expect(pipelineState.data).toBeDefined();
        if (pipelineState.data) {
          expect(pipelineState.data.market_research).toBeTruthy();
          expect(pipelineState.data.lbc).toBeTruthy();
          expect(pipelineState.data.persona).toBeTruthy();
          expect(pipelineState.data.roadmap?.project_tasks).toEqual(mockProjectDetails.roadmap_tasks);
        }
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/project/details`);
      req.flush(mockProjectDetails);
    });
  });

  describe('transformProjectDetailsToPipelineState', () => {
    it('should correctly transform project details to pipeline state', () => {
      // Access private method through service instance
      const transformedState = (service as any).transformProjectDetailsToPipelineState(mockProjectDetails);

      expect(transformedState.run_id).toBe('run-123');
      expect(transformedState.project_name).toBe('Test Project 1');
      expect(transformedState.industry).toBe('Technology');
      expect(transformedState.data).toBeDefined();
      if (transformedState.data) {
        expect(transformedState.data.market_research).toBeTruthy();
        expect(transformedState.data.lbc).toBeTruthy();
        expect(transformedState.data.persona).toBeTruthy();
        expect(transformedState.data.roadmap?.project_tasks).toEqual(mockProjectDetails.roadmap_tasks);
      }
      expect(transformedState.completed_steps).toContain('market_research');
      expect(transformedState.completed_steps).toContain('roadmap');
    });
  });

  describe('determineCompletedSteps', () => {
    it('should determine completed steps correctly', () => {
      const completedSteps = (service as any).determineCompletedSteps(mockProjectDetails);

      expect(completedSteps).toContain('market_research');
      expect(completedSteps).toContain('lbc');
      expect(completedSteps).toContain('persona');
      expect(completedSteps).toContain('swot');
      expect(completedSteps).toContain('features');
      expect(completedSteps).toContain('roadmap');
      expect(completedSteps.length).toBe(6);
    });

    it('should handle empty project details', () => {
      const emptyDetails: ProjectDetailsResponse = {
        ...mockProjectDetails,
        market_research: null,
        lean_business_canvas: null,
        user_personas: { personas: [] },
        swot_analysis: null,
        features: null,
        roadmap_tasks: []
      };

      const completedSteps = (service as any).determineCompletedSteps(emptyDetails);
      expect(completedSteps.length).toBe(0);
    });
  });
});
