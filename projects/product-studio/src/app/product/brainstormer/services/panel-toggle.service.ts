import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class PanelToggleService {
  private isLeftPanelCollapsed$ = new BehaviorSubject<boolean>(false);
  private defaultLeftPanelWidth = '25%';
  private defaultRightPanelWidth = '75%';

  constructor() {}

  /**
   * Observable for left panel collapsed state
   */
  get isLeftPanelCollapsed(): Observable<boolean> {
    return this.isLeftPanelCollapsed$.asObservable();
  }

  /**
   * Get current collapsed state
   */
  get isCollapsed(): boolean {
    return this.isLeftPanelCollapsed$.value;
  }

  /**
   * Toggle the left panel visibility
   */
  toggleLeftPanel(): void {
    const leftPanel = document.querySelector('.awe-leftpanel') as HTMLElement;
    const rightPanel = document.querySelector('.awe-rightpanel') as HTMLElement;
    const splitScreen = document.querySelector('.awe-splitscreen') as HTMLElement;

    if (leftPanel && rightPanel && splitScreen) {
      if (this.isLeftPanelCollapsed$.value) {
        // Show left panel
        leftPanel.style.width = this.defaultLeftPanelWidth;
        rightPanel.style.width = this.defaultRightPanelWidth;
        this.isLeftPanelCollapsed$.next(false);
        splitScreen.classList.remove('left-panel-collapsed');
      } else {
        // Hide left panel
        leftPanel.style.width = '0%';
        rightPanel.style.width = '100%';
        this.isLeftPanelCollapsed$.next(true);
        splitScreen.classList.add('left-panel-collapsed');
      }
    }
  }

  /**
   * Set left panel collapsed state
   */
  setLeftPanelCollapsed(collapsed: boolean): void {
    if (collapsed !== this.isLeftPanelCollapsed$.value) {
      this.toggleLeftPanel();
    }
  }

  /**
   * Show left panel
   */
  showLeftPanel(): void {
    if (this.isLeftPanelCollapsed$.value) {
      this.toggleLeftPanel();
    }
  }

  /**
   * Hide left panel
   */
  hideLeftPanel(): void {
    if (!this.isLeftPanelCollapsed$.value) {
      this.toggleLeftPanel();
    }
  }

  /**
   * Set default panel widths
   */
  setDefaultWidths(leftWidth: string, rightWidth: string): void {
    this.defaultLeftPanelWidth = leftWidth;
    this.defaultRightPanelWidth = rightWidth;
  }
}
