import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { ProductPipelineService } from './product-pipeline.service';
import { AppStateService } from '../../shared/services/app-state.service';
import { EnhancePromptRequest, EnhancePromptResponse } from '../interfaces/pipeline-api.interface';
import { environment } from '../../../environments/environment';

describe('ProductPipelineService - Enhance Prompt', () => {
  let service: ProductPipelineService;
  let httpMock: HttpTestingController;
  let appStateService: jasmine.SpyObj<AppStateService>;

  beforeEach(() => {
    const appStateServiceSpy = jasmine.createSpyObj('AppStateService', [
      'setLoading',
      'clearError',
      'setError'
    ]);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        ProductPipelineService,
        { provide: AppStateService, useValue: appStateServiceSpy }
      ]
    });

    service = TestBed.inject(ProductPipelineService);
    httpMock = TestBed.inject(HttpTestingController);
    appStateService = TestBed.inject(AppStateService) as jasmine.SpyObj<AppStateService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('enhancePrompt', () => {
    it('should enhance a prompt successfully', () => {
      const testPrompt = 'Create a mobile app for fitness tracking';
      const mockResponse: EnhancePromptResponse = {
        enhancedPrompt: {
          ideaTitle: "FitTrack Pro",
          conceptOverview: "A comprehensive mobile application for fitness tracking",
          problem: "People struggle to maintain consistent fitness routines",
          targetAudience: "Health-conscious individuals aged 25-45",
          coreSolution: "Personalized workout tracking with social features",
          uniqueValueProposition: "AI-powered recommendations based on user behavior",
          keySuccessMetric: "User retention rate after 3 months",
          monetizationModel: "Freemium with premium subscription features"
        },
        clarificationNeeded: []
      };

      service.enhancePrompt(testPrompt).subscribe(response => {
        expect(response).toEqual(mockResponse);
        expect(response.enhancedPrompt.ideaTitle).toBe('FitTrack Pro');
        expect(response.enhancedPrompt.conceptOverview).toContain('comprehensive mobile application');
        expect(response.clarificationNeeded).toEqual([]);
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/enhance`);
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({
        prompt: testPrompt
      } as EnhancePromptRequest);

      req.flush(mockResponse);

      // Verify loading state management
      expect(appStateService.setLoading).toHaveBeenCalledWith(
        'isLoadingPipeline',
        true,
        'Enhancing your prompt...'
      );
      expect(appStateService.clearError).toHaveBeenCalledWith('pipelineError');
    });

    it('should handle empty prompt by trimming whitespace', () => {
      const testPrompt = '  Create a simple app  ';
      const trimmedPrompt = 'Create a simple app';
      
      service.enhancePrompt(testPrompt).subscribe();

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/enhance`);
      expect(req.request.body.prompt).toBe(trimmedPrompt);
      
      req.flush({
        enhancedPrompt: {
          ideaTitle: 'Enhanced App',
          conceptOverview: 'Enhanced version',
          problem: 'Test problem',
          targetAudience: 'Test audience',
          coreSolution: 'Test solution',
          uniqueValueProposition: 'Test value prop',
          keySuccessMetric: 'Test metric',
          monetizationModel: 'Test model'
        },
        clarificationNeeded: []
      });
    });

    it('should handle API errors gracefully', () => {
      const testPrompt = 'Test prompt';
      const errorMessage = 'Enhancement service unavailable';

      service.enhancePrompt(testPrompt).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error).toBeDefined();
        }
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/enhance`);
      req.flush({ message: errorMessage }, { status: 500, statusText: 'Internal Server Error' });

      // Verify error handling
      expect(appStateService.setError).toHaveBeenCalledWith(
        'pipelineError',
        'Failed to enhance prompt. Please try again.'
      );
      expect(appStateService.setLoading).toHaveBeenCalledWith('isLoadingPipeline', false);
    });

    it('should handle network errors', () => {
      const testPrompt = 'Test prompt';

      service.enhancePrompt(testPrompt).subscribe({
        next: () => fail('Should have failed'),
        error: (error) => {
          expect(error).toBeDefined();
        }
      });

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/enhance`);
      req.error(new ErrorEvent('Network error'));

      expect(appStateService.setError).toHaveBeenCalled();
      expect(appStateService.setLoading).toHaveBeenCalledWith('isLoadingPipeline', false);
    });
  });

  describe('API Integration Test', () => {
    it('should construct correct API URL', () => {
      const testPrompt = 'Test prompt';
      
      service.enhancePrompt(testPrompt).subscribe();
      
      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/enhance`);
      expect(req.request.url).toBe(`${environment.pipelineApiBaseUrl}/enhance`);
      
      req.flush({
        enhancedPrompt: {
          ideaTitle: 'Test',
          conceptOverview: 'Enhanced',
          problem: 'Test',
          targetAudience: 'Test',
          coreSolution: 'Test',
          uniqueValueProposition: 'Test',
          keySuccessMetric: 'Test',
          monetizationModel: 'Test'
        },
        clarificationNeeded: []
      });
    });

    it('should include correct headers', () => {
      const testPrompt = 'Test prompt';

      service.enhancePrompt(testPrompt).subscribe();

      const req = httpMock.expectOne(`${environment.pipelineApiBaseUrl}/enhance`);
      expect(req.request.headers.get('Content-Type')).toBe('application/json');

      req.flush({
        enhancedPrompt: {
          ideaTitle: 'Test',
          conceptOverview: 'Enhanced',
          problem: 'Test',
          targetAudience: 'Test',
          coreSolution: 'Test',
          uniqueValueProposition: 'Test',
          keySuccessMetric: 'Test',
          monetizationModel: 'Test'
        },
        clarificationNeeded: []
      });
    });
  });
});
