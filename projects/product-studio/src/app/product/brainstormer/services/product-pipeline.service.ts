import { inject, Injectable } from '@angular/core';
import {
  HttpClient,
  HttpHeaders,
  HttpErrorResponse,
} from '@angular/common/http';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import {
  PipelineStartRequest,
  PipelineStepRequest,
  PipelineStartResponse,
  PipelineStepResponse,
  PipelineState,
  PipelineStep,
  PipelineErrorResponse,
  ChatRequest,
  ChatResponse,
  ProjectDetailsRequest,
  ProjectDetailsResponse,
  EnhancePromptRequest,
  EnhancePromptResponse,
} from '../interfaces/pipeline-api.interface';
import { Router } from '@angular/router';
import { AppStateService } from '../../shared/services/app-state.service';
import { ChatMessage } from '../../shared/interfaces/app-state.interface';

@Injectable({
  providedIn: 'root',
})
export class ProductPipelineService {
  // Hybrid state management: centralized AppStateService + legacy session storage support
  private readonly STORAGE_KEY = 'aava_pipeline_state';
  private readonly PROJECT_DETAILS_KEY = 'aava_project_details';
  private pipelineStateSubject = new BehaviorSubject<PipelineState>(
    this.loadStateFromStorage(),
  );
  public pipelineState$ = this.pipelineStateSubject.asObservable();

  // Project details state for form pre-population
  private projectDetailsSubject = new BehaviorSubject<ProjectDetailsResponse | null>(null);
  public projectDetails$ = this.projectDetailsSubject.asObservable();

  // Original prompt state for form pre-population
  private originalPromptSubject = new BehaviorSubject<string | null>(null);
  public originalPrompt$ = this.originalPromptSubject.asObservable();

  private httpOptions = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    }),
  };

  constructor(
    private http: HttpClient,
    private router: Router,
    private appStateService: AppStateService,
  ) {
    // Setup hybrid state management with bidirectional sync
    this.setupStateSync();
    // Load project details from storage if available
    this.loadProjectDetailsFromStorage();
  

  }

  /**
   * Setup bidirectional sync between legacy state and centralized state
   */
  private setupStateSync(): void {
    // Sync centralized state to legacy state (for backward compatibility)
    this.appStateService.pipelineState$.subscribe((state) => {
      this.pipelineStateSubject.next(state);
      this.saveStateToStorage(state);
    });

    // Initialize centralized state from legacy storage if available
    const legacyState = this.loadStateFromStorage();
    if (legacyState.run_id || Object.keys(legacyState.data).length > 0) {
      this.appStateService.updatePipelineState(legacyState);
    }
  }

  /**
   * Get current pipeline state (hybrid: prefer BehaviorSubject, fallback to centralized)
   */
  get currentState(): PipelineState {
    return this.pipelineStateSubject.value;
  }

  /**
   * Set project details for form pre-population
   */
  setProjectDetails(details: ProjectDetailsResponse): void {
    this.projectDetailsSubject.next(details);
    // Also store in session storage for persistence
    try {
      sessionStorage.setItem(this.PROJECT_DETAILS_KEY, JSON.stringify(details));

    } catch (error) {
      console.warn('⚠️ Failed to store project details:', error);
    }
  }

  /**
   * Get current project details
   */
  getProjectDetails(): ProjectDetailsResponse | null {
    return this.projectDetailsSubject.value;
  }

  /**
   * Clear project details
   */
  clearProjectDetails(): void {
    this.projectDetailsSubject.next(null);
    this.clearOriginalPrompt(); // Also clear the original prompt
    try {
      sessionStorage.removeItem(this.PROJECT_DETAILS_KEY);

    } catch (error) {
      console.warn('⚠️ Failed to clear project details:', error);
    }
  }

  /**
   * Set original prompt for form pre-population
   */
  setOriginalPrompt(prompt: string): void {
    this.originalPromptSubject.next(prompt);

  }

  /**
   * Get current original prompt
   */
  getOriginalPrompt(): string | null {
    return this.originalPromptSubject.value;
  }

  /**
   * Clear original prompt
   */
  clearOriginalPrompt(): void {
    this.originalPromptSubject.next(null);

  }

  /**
   * Load project details from session storage
   */
  private loadProjectDetailsFromStorage(): void {
    try {
      const stored = sessionStorage.getItem(this.PROJECT_DETAILS_KEY);
      if (stored) {
        const projectDetails = JSON.parse(stored) as ProjectDetailsResponse;
        this.projectDetailsSubject.next(projectDetails);

        // Also sync with app state service project details state
        this.appStateService.updateProjectDetailsState({
          project_name: projectDetails.project_name,
          industry: projectDetails.industry,
          user_groups: projectDetails.user_groups,
          justification: projectDetails.user_idea // Use user_idea as project description/justification
        });

        console.log('✅ Project details loaded and synced from storage:', projectDetails.project_name);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load project details from storage:', error);
    }
  }

  // ===========================================
  // API CALLS

  /**
   * Call identify details API to extract project information from user prompt
   */
  getIdentifyDetails(prompt: string): Observable<ProjectDetailsResponse> {
    const url = `${environment.pipelineApiBaseUrl}/identify-details`;

    const request: ProjectDetailsRequest = {
      prompt: prompt,
    };

    // Store the original prompt for form pre-population
    this.setOriginalPrompt(prompt);

    this.appStateService.setLoading(
      'isLoadingPipeline',
      true,
      'Analyzing your idea...',
    );
    this.appStateService.clearAllErrors();
   

    return this.http
      .post<ProjectDetailsResponse>(url, request, this.httpOptions)
      .pipe(
        tap((response) => {
          // Store project details for form pre-population
          this.setProjectDetails(response);

          // Update product-studio project details state
          this.appStateService.updateProjectDetailsState({
            project_name: response.project_name,
            industry: response.industry,
            user_groups: response.user_groups,
            justification: response.justification 
          });

          // Store in experience-studio state via sessionStorage for cross-project access
          this.storeProjectDetailsInExperienceStudio(response);

          console.log("#Project-Details:", response);
          this.appStateService.setLoading('isLoadingPipeline', false);
        }),
        catchError((error) => {
          console.error('❌ Failed to identify project details:', error);
          this.appStateService.setError('pipelineError', 'Failed to analyze your idea. Please try again.');
          this.appStateService.setLoading('isLoadingPipeline', false);
          return this.handleError(error);
        }),
      );
  }

  /**
   * Start the pipeline with user idea and automatically call market research
   * @deprecated Use startPipelineWithDetails instead
   */
  startPipeline(userIdea: string): Observable<PipelineStartResponse> {
    // For backward compatibility, use default values
    return this.startPipelineWithDetails({
      user_idea: userIdea,
      project_name: 'Untitled Project',
      user_groups: ['Business Users'],
      industry: 'Other'
    });
  }

  /**
   * Start the pipeline with complete project details
   */
  startPipelineWithDetails(projectDetails: PipelineStartRequest): Observable<PipelineStartResponse> {
    console.log('🚀 [PIPELINE SERVICE] Starting pipeline with details');
    console.log('   📊 Project name:', projectDetails.project_name);
    console.log('   📊 User idea:', projectDetails.user_idea.substring(0, 100) + '...');
    console.log('   📊 Industry:', projectDetails.industry);
    console.log('   📊 User groups:', projectDetails.user_groups);

    const request: PipelineStartRequest = {
      user_idea: projectDetails.user_idea,
      project_name: projectDetails.project_name,
      user_groups: projectDetails.user_groups,
      industry: projectDetails.industry,
    };

    const url = `${environment.pipelineApiBaseUrl}/pipeline/start`;

    // Set loading state
    this.appStateService.setLoading(
      'isLoadingPipeline',
      true,
      'Starting pipeline...',
    );
    this.appStateService.clearAllErrors();

    return this.http
      .post<PipelineStartResponse>(url, request, this.httpOptions)
      .pipe(
        switchMap((response) => {
          console.log('✅ [PIPELINE SERVICE] Pipeline start response received');
          console.log('   📊 Response run_id:', response.run_id);
          console.log('   📊 Response project_name:', response.project_name);
          console.log('   📊 Response current_step:', response.current_step);

          // Update centralized state
          this.appStateService.setPipelineStart(response);

          // Legacy support - update local state
          this.updatePipelineState({
            run_id: response.run_id,
            project_name: response.project_name || null,
            industry: response.industry || null,
            user_groups: response.user_groups || null,
            current_step: response.current_step as PipelineStep,
            completed_steps: [],
            data: {},
          });

          console.log('   🔄 Starting market research step...');
          // Automatically call market research step after start
          return this.progressToStep('market_research').pipe(
            map(() => {
              console.log('   ✅ Market research completed, returning start response');
              return response;
            }) // Return the original start response
          );
        }),
        catchError((error) => {
          this.appStateService.setLoading('isLoadingPipeline', false);
          this.appStateService.setError(
            'pipelineError',
            'Failed to start pipeline',
          );
          return this.handleError(error);
        }),
      );
  }

  /**
   * Progress to next step in the pipeline
   * @param currentStep - The current step to send as current_step in the request
   */
  progressToStep(currentStep: PipelineStep): Observable<PipelineStepResponse> {
    console.log('⏭️ [PIPELINE SERVICE] progressToStep called for:', currentStep);
    const currentState = this.currentState;
    console.log('   📊 Current run_id:', currentState.run_id);

    if (!currentState.run_id) {
      const error = 'No active pipeline session. Please start a new pipeline.';
      console.error('   ❌ No run_id found, cannot progress step');
      this.appStateService.setError('pipelineError', error);
      return throwError(() => new Error(error));
    }

    const request: PipelineStepRequest = {
      run_id: currentState.run_id,
      current_step: currentStep,
    };

    const url = `${environment.pipelineApiBaseUrl}/pipeline/next`;
    console.log('   🌐 Making API call to:', url);

    // Set loading state with the actual API operation name
    this.appStateService.setLoading(
      'isLoadingStep',
      true,
      `Processing ${currentStep}...`,
      currentStep // Pass the actual API step name for stepper loading states
    );
    this.appStateService.clearError('stepError');

    return this.http
      .post<PipelineStepResponse>(url, request, this.httpOptions)
      .pipe(
        tap((response) => {
          console.log('✅ [PIPELINE SERVICE] Step response received for:', response.step);
          console.log('   📊 Response data keys:', Object.keys(response.data || {}));

          // Update centralized state
          this.appStateService.setPipelineStepComplete(response);
          this.appStateService.setLoading('isLoadingStep', false, undefined, '');

          // Legacy support - update local state
          this.updateStepData(response.step as PipelineStep, response.data);

          console.log('   ✅ Step data updated in both centralized and local state');
        }),
        catchError((error) => {
          this.appStateService.setLoading('isLoadingStep', false, undefined, '');
          this.appStateService.setError(
            'stepError',
            `Failed to process ${currentStep}`,
          );
          return this.handleError(error);
        }),
      );
  }

  /**
   * Get specific step data
   */
  getStepData<T>(step: PipelineStep): T | null {
    return (this.currentState.data[step] as T) || null;
  }

  /**
   * Check if step is completed
   */
  isStepCompleted(step: PipelineStep): boolean {
    return this.currentState.completed_steps.includes(step);
  }

  /**
   * Reset pipeline state (hybrid: reset both local and centralized state)
   */
  resetPipeline(): void {
    const defaultState: PipelineState = {
      run_id: null,
      project_name: null,
      industry: null,
      user_groups: null,
      current_step: null,
      completed_steps: [],
      data: {},
    };

    this.pipelineStateSubject.next(defaultState);
    this.appStateService.resetState();
  }

  /**
   * Update pipeline state (hybrid: update both local and centralized state)
   */
  private updatePipelineState(newState: PipelineState): void {
    this.pipelineStateSubject.next(newState);
    this.appStateService.updatePipelineState(newState);
  }

  private updateProjectDetailsState(newState: ProjectDetailsResponse): void {
    this.projectDetailsSubject.next(newState);
    this.appStateService.updateProjectDetailsState(newState);
  }

  /**
   * Store project details in experience-studio state via sessionStorage
   * This enables cross-project access to project details
   */
  private storeProjectDetailsInExperienceStudio(response: ProjectDetailsResponse): void {
    try {
      const experienceStudioProjectDetails = {
        project_name: response.project_name,
        user_idea: response.user_idea,
        user_groups: response.user_groups,
        industry: response.industry,
        lastUpdated: new Date()
      };

      // Store in sessionStorage with experience-studio key
      sessionStorage.setItem('experience_studio_project_details', JSON.stringify(experienceStudioProjectDetails));

      console.log('✅ Project details stored for experience-studio access:', experienceStudioProjectDetails);
    } catch (error) {
      console.warn('⚠️ Failed to store project details for experience-studio:', error);
    }
  }

  /**
   * Update step data and mark step as completed
   */
  private updateStepData(step: PipelineStep, data: any): void {
    const currentState = this.currentState;
    const updatedState: PipelineState = {
      ...currentState,
      current_step: step,
      completed_steps: [
        ...currentState.completed_steps.filter((s) => s !== step),
        step,
      ],
      data: {
        ...currentState.data,
        [step]: data,
      },
    };
    this.updatePipelineState(updatedState);
  }

  /**
   * Handle HTTP errors
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Network error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage =
            'Invalid request. Please check your input and try again.';
          break;
        case 401:
          errorMessage =
            'Authentication required. Please log in and try again.';
          break;
        case 403:
          errorMessage =
            'Access denied. You do not have permission to perform this action.';
          break;
        case 404:
          errorMessage = 'Service not found. Please try again later.';
          break;
        case 429:
          errorMessage =
            'Too many requests. Please wait a moment and try again.';
          break;
        case 500:
          errorMessage = 'Server error. Please try again later.';
          break;
        case 503:
          errorMessage =
            'Service temporarily unavailable. Please try again later.';
          break;
        default:
          errorMessage = `Server error (${error.status}). Please try again later.`;
      }

      // Try to extract more specific error message from response
      if (error.error && typeof error.error === 'object') {
        const pipelineError = error.error as PipelineErrorResponse;
        if (pipelineError.message) {
          errorMessage = pipelineError.message;
        }
      }
    }

    console.error('Pipeline API Error:', {
      status: error.status,
      message: errorMessage,
      error: error.error,
    });

    return throwError(() => new Error(errorMessage));
  }

  /**
   * Load pipeline state from session storage
   */
  private loadStateFromStorage(): PipelineState {
    try {
      const stored = sessionStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);

        return parsedState;
      }
    } catch (error) {
      console.warn('⚠️ Failed to load pipeline state from storage:', error);
    }

    // Return default state if no stored state or error
    return {
      run_id: null,
      project_name: null,
      industry: null,
      user_groups: null,
      current_step: null,
      completed_steps: [],
      data: {},
    };
  }

  /**
   * Save pipeline state to session storage
   */
  private saveStateToStorage(state: PipelineState): void {
    try {
      // Only save if there's meaningful data to avoid saving empty states
      if (state.run_id || Object.keys(state.data).length > 0) {
        sessionStorage.setItem(this.STORAGE_KEY, JSON.stringify(state));

      }
    } catch (error) {
      console.warn('⚠️ Failed to save pipeline state to storage:', error);
    }
  }

  /**
   * Clear stored pipeline state (hybrid: both session storage and centralized state)
   */
  clearStoredState(): void {
    try {
      sessionStorage.removeItem(this.STORAGE_KEY);

    } catch (error) {
      console.warn('⚠️ Failed to clear stored pipeline state:', error);
    }

    // Also clear centralized state
    this.appStateService.resetState();
  }

  /**
   * Check if there is stored pipeline data (hybrid: check both sources)
   */
  hasStoredData(): boolean {
    // Check centralized state first
    const hasAppStateData = this.appStateService.hasStoredData();

    // Also check session storage
    try {
      const stored = sessionStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsedState = JSON.parse(stored);
        const hasStorageData =
          parsedState.run_id && Object.keys(parsedState.data).length > 0;
        return hasAppStateData || hasStorageData;
      }
    } catch (error) {
      console.warn('⚠️ Failed to check stored pipeline data:', error);
    }

    return hasAppStateData;
  }

  /**
   * Enhance user prompt using AI
   * Takes a user's original prompt and returns an enhanced/improved version
   */
  enhancePrompt(prompt: string): Observable<EnhancePromptResponse> {
    const url = `${environment.pipelineApiBaseUrl}/enhance`;

    const request: EnhancePromptRequest = {
      prompt: prompt.trim(),
    };

    // Set loading state using existing pipeline loading key
    this.appStateService.setLoading(
      'isLoadingPipeline',
      true,
      'Enhancing your prompt...',
    );
    this.appStateService.clearError('pipelineError');



    return this.http
      .post<EnhancePromptResponse>(url, request, this.httpOptions)
      .pipe(
        tap((response) => {

          this.appStateService.setLoading('isLoadingPipeline', false);
        }),
        catchError((error) => {
          console.error('❌ Failed to enhance prompt:', error);
          this.appStateService.setError('pipelineError', 'Failed to enhance prompt. Please try again.');
          this.appStateService.setLoading('isLoadingPipeline', false);
          return this.handleError(error);
        }),
      );
  }

  /**
   * Send chat message to the AI assistant
   * Handles user queries and requests for clarification or changes
   */
  sendChatMessage(message: string): Observable<ChatResponse> {
    const currentState = this.pipelineStateSubject.value;

    if (!currentState.run_id) {
      const error =
        'No active pipeline session. Please start a new brainstorming session.';
      this.appStateService.setError('chatError', error);
      return throwError(() => new Error(error));
    }

    // Get current step from URL context for accurate step tracking
    const currentStepFromContext = this.getCurrentStepFromContext();
    const finalCurrentStep = currentStepFromContext || currentState.current_step || 'lbc';



    const chatRequest: ChatRequest = {
      run_id: currentState.run_id,
      current_step: finalCurrentStep,
      message: message.trim(),
    };

    // Add user message to centralized state (mark as new, not restored)
    const userMessage: ChatMessage = {
      id: this.appStateService.generateMessageId(),
      content: message.trim(),
      sender: 'user',
      timestamp: new Date(),
      isRestored: false, // Mark as new message
    };
    this.appStateService.addNewChatMessage(userMessage);

    // Set loading state
    this.appStateService.setLoading(
      'isLoadingChat',
      true,
      'Sending message...',
    );
    this.appStateService.updateChatState({ isAiTyping: true });
    this.appStateService.clearError('chatError');



    return this.http
      .post<ChatResponse>(
        `${environment.pipelineApiBaseUrl}/pipeline/chat`,
        chatRequest,
        this.httpOptions,
      )
      .pipe(
        tap((response: ChatResponse) => {
          // Add AI response to centralized state (mark as new, not restored)
          const aiMessage: ChatMessage = {
            id: this.appStateService.generateMessageId(),
            content: response.message_to_user,
            sender: 'ai',
            timestamp: new Date(),
            responseType: response.response_type,
            isRestored: false, // Mark as new message
          };
          this.appStateService.addNewChatMessage(aiMessage);

          // Update chat state
          this.appStateService.setChatResponse(response);
          this.appStateService.setLoading('isLoadingChat', false);


        }),
        catchError((error: HttpErrorResponse) => {
          this.appStateService.setLoading('isLoadingChat', false);
          this.appStateService.updateChatState({ isAiTyping: false });
          this.appStateService.setError('chatError', 'Failed to send message');

          console.error('❌ Chat API error:', error);
          return this.handleError(error);
        }),
      );
  }

  /**
   * Get current step from URL context for accurate step tracking
   * Since all steps are under /brainstorming, we need to get the step from the stepper service
   */
  private getCurrentStepFromContext(): PipelineStep | null {
    const url = this.router.url;
    const currentState = this.pipelineStateSubject.value;



    // Check if we're in the brainstorming flow
    if (url.includes('/brainstorming')) {
      // Get current step from pipeline state since URL doesn't change between steps
      const currentStep = currentState.current_step;

      if (currentStep) {

        return currentStep;
      }

      // Fallback to 'lbc' if no current step is set

      return 'lbc';
    }


    return null;
  }

  /**
   * Update current step in pipeline state (for navigation tracking)
   */
  updateCurrentStep(step: PipelineStep): void {
    const currentState = this.pipelineStateSubject.value;
    const updatedState = {
      ...currentState,
      current_step: step
    };

    this.pipelineStateSubject.next(updatedState);
    this.appStateService.updatePipelineState(updatedState);


  }

  /**
   * Set project name in pipeline state
   */
  setProjectName(projectName: string): void {
    const trimmedName = projectName.trim() || null;

    // Update pipeline state
    const currentState = this.pipelineStateSubject.value;
    const updatedState = {
      ...currentState,
      project_name: trimmedName
    };

    this.pipelineStateSubject.next(updatedState);
    this.appStateService.updatePipelineState(updatedState);

    // Also update project details state to keep both in sync
    this.appStateService.updateProjectDetailsState({
      project_name: trimmedName
    });

    console.log('✅ Project name updated in both pipeline and project details states:', trimmedName);
  }

  /**
   * Get current project name from pipeline state
   */
  getProjectName(): string | null {
    const currentState = this.pipelineStateSubject.value;
    return currentState.project_name;
  }

  /**
   * Get project name as observable for reactive updates
   */
  getProjectName$(): Observable<string | null> {
    return this.pipelineStateSubject.pipe(
      map(state => state.project_name)
    );
  }

  /**
   * Check if current session has loaded project data
   * @returns boolean indicating if project data is loaded
   */
  hasLoadedProjectData(): boolean {
    const currentState = this.pipelineStateSubject.value;
    return !!(currentState.run_id && Object.keys(currentState.data).length > 0);
  }

  /**
   * Get current project run_id
   * @returns string | null - The current project run_id
   */
  getCurrentRunId(): string | null {
    return this.pipelineStateSubject.value.run_id;
  }

  /**
   * Check if a specific step has data loaded
   * @param step - The pipeline step to check
   * @returns boolean indicating if step has data
   */
  hasStepData(step: PipelineStep): boolean {
    const currentState = this.pipelineStateSubject.value;
    return !!(currentState.data[step] && Object.keys(currentState.data[step] || {}).length > 0);
  }

  /**
   * Get summary of loaded project data
   * @returns Object with data availability summary
   */
  getProjectDataSummary(): {
    runId: string | null;
    projectName: string | null;
    completedSteps: PipelineStep[];
    totalSteps: number;
    completionPercentage: number;
  } {
    const currentState = this.pipelineStateSubject.value;
    const allSteps: PipelineStep[] = ['market_research', 'lbc', 'persona', 'swot', 'features', 'roadmap'];
    const completedSteps = currentState.completed_steps;

    return {
      runId: currentState.run_id,
      projectName: currentState.project_name,
      completedSteps,
      totalSteps: allSteps.length,
      completionPercentage: Math.round((completedSteps.length / allSteps.length) * 100)
    };
  }
}
