import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class SplitScreenService {
  // Simple property to track split screen state
  private _isOpen: boolean = true; // Default to open

  // Track edit modal states
  private _isAnyEditModalOpen: boolean = false;

  // Callback system for state change notifications
  private stateChangeCallbacks: Array<(isOpen: boolean) => void> = [];

  // Callback system for edit modal state notifications
  private editModalStateCallbacks: Array<(isModalOpen: boolean) => void> = [];

  constructor() {
    // SplitScreenService initialized - Split screen is open by default
  }

  // ===========================================
  // CALLBACK REGISTRATION
  // ===========================================

  /**
   * Register a callback to be notified when split screen state changes
   */
  onStateChange(callback: (isOpen: boolean) => void): void {
    this.stateChangeCallbacks.push(callback);
  }

  /**
   * Unregister a callback
   */
  removeStateChangeCallback(callback: (isOpen: boolean) => void): void {
    const index = this.stateChangeCallbacks.indexOf(callback);
    if (index > -1) {
      this.stateChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * Notify all registered callbacks of state change
   */
  private notifyStateChange(): void {
    this.stateChangeCallbacks.forEach(callback => {
      try {
        callback(this._isOpen);
      } catch (error) {
        console.error('❌ Error in state change callback:', error);
      }
    });
  }

  // ===========================================
  // PUBLIC API METHODS
  // ===========================================

  /**
   * Check if split screen is currently open
   */
  isOpen(): boolean {
    return this._isOpen;
  }

  /**
   * Open the split screen
   */
  openSplitScreen(): void {
    if (!this._isOpen) {
      this._isOpen = true;
      this.notifyStateChange();
    } else {
    }
  }

  /**
   * Close the split screen
   */
  closeSplitScreen(): void {
    if (this._isOpen) {
      this._isOpen = false;
      this.notifyStateChange();
    } else {
    }
  }

  /**
   * Toggle split screen state (for backward compatibility)
   */
  toggleSplitScreen(): void {
    const previousState = this._isOpen;
    // Toggling split screen state
    this._isOpen = !this._isOpen;

    // Only notify if state actually changed
    if (previousState !== this._isOpen) {
      this.notifyStateChange();
    }
  }

  // ===========================================
  // EDIT MODAL STATE MANAGEMENT
  // ===========================================

  /**
   * Check if any edit modal is currently open
   */
  isAnyEditModalOpen(): boolean {
    return this._isAnyEditModalOpen;
  }

  /**
   * Set edit modal open state
   */
  setEditModalOpen(isOpen: boolean): void {
    const previousState = this._isAnyEditModalOpen;
    this._isAnyEditModalOpen = isOpen;


    // Notify callbacks if state changed
    if (previousState !== this._isAnyEditModalOpen) {
      this.notifyEditModalStateChange();
    }
  }

  /**
   * Register a callback for edit modal state changes
   */
  onEditModalStateChange(callback: (isModalOpen: boolean) => void): void {
    this.editModalStateCallbacks.push(callback);
  }

  /**
   * Unregister an edit modal state callback
   */
  removeEditModalStateCallback(callback: (isModalOpen: boolean) => void): void {
    const index = this.editModalStateCallbacks.indexOf(callback);
    if (index > -1) {
      this.editModalStateCallbacks.splice(index, 1);
    }
  }

  /**
   * Notify all registered callbacks of edit modal state change
   */
  private notifyEditModalStateChange(): void {
    this.editModalStateCallbacks.forEach(callback => {
      try {
        callback(this._isAnyEditModalOpen);
      } catch (error) {
        console.error('❌ Error in edit modal state change callback:', error);
      }
    });
  }

  // ===========================================
  // DEBUG METHODS
  // ===========================================

  /**
   * Get debug information about the service state
   */
  getDebugInfo(): { isOpen: boolean; callbackCount: number } {
    return {
      isOpen: this._isOpen,
      callbackCount: this.stateChangeCallbacks.length
    };
  }

  /**
   * Force notify all callbacks (for testing)
   */
  forceNotify(): void {
    this.notifyStateChange();
  }
}
