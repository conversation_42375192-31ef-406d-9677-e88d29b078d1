import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  tags: string[];
}

export interface FeatureSection {
  id: string;
  title: string;
  subtitle: string;
  features: FeatureCard[];
}

@Injectable({
  providedIn: 'root'
})
export class SwotDataService {
  // Initialize with empty array - data will come from API
  private sectionsSubject = new BehaviorSubject<FeatureSection[]>([]);
  public sections$ = this.sectionsSubject.asObservable();

  constructor() {}

 
  // Get all sections
  getSections(): FeatureSection[] {
    return this.sectionsSubject.value;
  }

  // Get section by ID
  getSectionById(id: string): FeatureSection | undefined {
    return this.sectionsSubject.value.find(section => section.id === id);
  }

  // Get section IDs
  getSectionIds(): string[] {
    return this.sectionsSubject.value.map(section => section.id);
  }

  // Add new feature to a section
  addFeature(sectionId: string, feature: Omit<FeatureCard, 'id'>): void {
    const currentSections = this.sectionsSubject.value;
    const sectionIndex = currentSections.findIndex(section => section.id === sectionId);

    if (sectionIndex !== -1) {
      const updatedSections = [...currentSections];
      const newFeature: FeatureCard = {
        ...feature,
        id: `${sectionId}-${Date.now()}`
      };

      updatedSections[sectionIndex] = {
        ...updatedSections[sectionIndex],
        features: [...updatedSections[sectionIndex].features, newFeature]
      };

      this.sectionsSubject.next(updatedSections);
    }
  }

  // Update existing feature
  updateFeature(featureId: string, updatedFeature: Partial<FeatureCard>): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: [
            ...updatedSections[sectionIndex].features.slice(0, featureIndex),
            { ...updatedSections[sectionIndex].features[featureIndex], ...updatedFeature },
            ...updatedSections[sectionIndex].features.slice(featureIndex + 1)
          ]
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Delete feature
  deleteFeature(featureId: string): void {
    const currentSections = this.sectionsSubject.value;
    const updatedSections = [...currentSections];

    for (let sectionIndex = 0; sectionIndex < updatedSections.length; sectionIndex++) {
      const featureIndex = updatedSections[sectionIndex].features.findIndex(f => f.id === featureId);

      if (featureIndex !== -1) {
        updatedSections[sectionIndex] = {
          ...updatedSections[sectionIndex],
          features: updatedSections[sectionIndex].features.filter(f => f.id !== featureId)
        };
        break;
      }
    }

    this.sectionsSubject.next(updatedSections);
  }

  // Get feature by ID across all sections
  getFeatureById(featureId: string): FeatureCard | undefined {
    const sections = this.sectionsSubject.value;
    for (const section of sections) {
      const feature = section.features.find(f => f.id === featureId);
      if (feature) return feature;
    }
    return undefined;
  }

  /**
   * Update SWOT data from API response
   * Maps API response structure to internal FeatureSection format
   */
  updateFromApiResponse(apiData: any): void {

    const sections: FeatureSection[] = [
      {
        id: 'strengths',
        title: 'S',
        subtitle: 'STRENGTHS',
        features: this.mapApiItemsToFeatures(apiData.strengths || [], 'strength')
      },
      {
        id: 'weaknesses',
        title: 'W',
        subtitle: 'WEAKNESSES',
        features: this.mapApiItemsToFeatures(apiData.weaknesses || [], 'weakness')
      },
      {
        id: 'opportunities',
        title: 'O',
        subtitle: 'OPPORTUNITIES',
        features: this.mapApiItemsToFeatures(apiData.opportunities || [], 'opportunity')
      },
      {
        id: 'threats',
        title: 'T',
        subtitle: 'THREATS',
        features: this.mapApiItemsToFeatures(apiData.threats || [], 'threat')
      }
    ];

    this.sectionsSubject.next(sections);
  }

  /**
   * Map API SWOT items to internal FeatureCard format
   */
  private mapApiItemsToFeatures(apiItems: any[], prefix: string): FeatureCard[] {
    return apiItems.map((item, index) => ({
      id: `${prefix}-${index + 1}`,
      title: item.title || `${prefix} ${index + 1}`,
      description: item.description || item.justification || 'No description available',
      tags: item.tags || []
    }));
  }
}