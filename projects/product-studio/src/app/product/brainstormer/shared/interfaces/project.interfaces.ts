// Project interfaces

export interface ProjectDetails {
  id: number;
  run_id: string;
  name: string;
  description: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  status: string;
  tags: string[];
  metadata: Record<string, any>;
}

export interface ProjectDetailsResponse {
  project: ProjectDetails;
}

export interface PipelineState {
  project_name?: string;
  project_description?: string;
  description?: string;
  name?: string;
  run_id?: string;
  current_step?: string;
  data?: Record<string, any>;
  project?: ProjectDetails;
}
