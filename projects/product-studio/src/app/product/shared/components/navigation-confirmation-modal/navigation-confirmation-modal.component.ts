import { Component, EventEmitter, Input, Output, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, IconsComponent } from '@awe/play-comp-library';


@Component({
  selector: 'app-navigation-confirmation-modal',
  standalone: true,
  imports: [CommonModule, ButtonComponent, IconsComponent],
  templateUrl: './navigation-confirmation-modal.component.html',
  styleUrls: ['./navigation-confirmation-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class NavigationConfirmationModalComponent {
  @Input() isVisible: boolean = false;
  @Input() title: string = 'Exit Session';
  @Input() message: string = 'Are you really sure you want to exit the session?';
  @Input() cancelButtonText: string = 'Cancel';
  @Input() confirmButtonText: string = 'OK';
  
  @Output() onCancel = new EventEmitter<void>();
  @Output() onConfirm = new EventEmitter<void>();
  @Output() onBackdropClick = new EventEmitter<void>();

  handleCancel(): void {
    this.onCancel.emit();
  }

  handleConfirm(): void {
    this.onConfirm.emit();
  }

  handleBackdropClick(event: MouseEvent): void {
    if (event.target === event.currentTarget) {
      this.onBackdropClick.emit();
    }
  }

  handleKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.handleCancel();
    }
  }
}
