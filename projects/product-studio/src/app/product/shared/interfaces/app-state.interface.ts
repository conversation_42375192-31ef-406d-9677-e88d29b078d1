// Centralized State Management Interfaces for AAVA Product Studio

import { 
  PipelineState, 
  PipelineStep, 
  ChatResponse,
  MarketResearchData,
  LBCData,
  PersonaData,
  SWOTData,
  FeaturesData,
  RoadmapData,
  ProjectDetailsState
} from '../../brainstormer/interfaces/pipeline-api.interface';

// Chat Message Interface (Enhanced)
export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  responseType?: 'clarification' | 'answer' | 'error';
  isTyping?: boolean;
  isRestored?: boolean; // Flag to indicate if message was restored from storage
}

// Chat State Interface
export interface ChatState {
  messages: ChatMessage[];
  isAiTyping: boolean;
  isLoading: boolean;
  errorMessage: string | null;
  lastResponse: ChatResponse | null;
  currentInputText: string; // Store current input text
  restoredFromStorage: boolean; // Flag to track if state was restored
}

// Navigation State Interface
export interface NavigationState {
  currentStepIndex: number;
  currentStepId: string;
  canGoNext: boolean;
  canGoPrevious: boolean;
  completedSteps: string[];
  stepsWithData: string[];
}

// Loading State Interface
export interface LoadingState {
  isLoadingPipeline: boolean;
  isLoadingStep: boolean;
  isLoadingChat: boolean;
  currentOperation: string | null;
  loadingMessage: string | null;
}

// Error State Interface
export interface ErrorState {
  pipelineError: string | null;
  stepError: string | null;
  chatError: string | null;
  networkError: string | null;
  lastErrorTimestamp: Date | null;
}

// User Session State Interface
export interface UserSessionState {
  sessionId: string | null;
  isAuthenticated: boolean;
  lastActivity: Date | null;
  preferences: {
    theme: 'light' | 'dark';
    autoSave: boolean;
    notifications: boolean;
  };
}

// UI State Interface
export interface UIState {
  showSplitScreen: boolean;
  activeModal: string | null;
  sidebarCollapsed: boolean;
  currentView: 'brainstorming' | 'summary' | 'dashboard';
  roboBallState: 'idle' | 'hover' | 'active';
}

// Summary Data State Interface
export interface SummaryDataState {
  name: string;
  description: string;
  progress: number;
  contributionText: string;
  understandingText: string;
  features: SummaryFeature[];
  personas: SummaryPersona[];
  swot: SummarySwotItem[];
  timeline: SummaryTimelineQuarter[];
  isLoading: boolean;
  hasData: boolean;
  lastUpdated: Date | null;
}

// Summary-specific interfaces
export interface SummaryPersona {
  name: string;
  role: string;
  avatarUrl: string;
}

export interface SummaryFeature {
  name: string;
  color: string;
  category: 'must_have' | 'should_have' | 'could_have' | 'wont_have';
}

export interface SummarySwotItem {
  category: 'strengths' | 'weaknesses' | 'opportunities' | 'threats';
  points: string[];
  color: string;
}

export interface SummaryTimelineItem {
  icon: string;
  label: string;
}

export interface SummaryTimelineQuarter {
  quarter: string;
  items: SummaryTimelineItem[];
}

// Comprehensive Application State Interface
export interface AppState {
  pipeline: PipelineState;
  projectDetails: ProjectDetailsState;
  chat: ChatState;
  navigation: NavigationState;
  loading: LoadingState;
  error: ErrorState;
  session: UserSessionState;
  ui: UIState;
  summaryData: SummaryDataState;
  lastUpdated: Date;
}

// State Update Action Types
export type StateUpdateAction = 
  | 'PIPELINE_START'
  | 'PIPELINE_STEP_COMPLETE'
  | 'PIPELINE_ERROR'
  | 'CHAT_MESSAGE_SENT'
  | 'CHAT_RESPONSE_RECEIVED'
  | 'CHAT_ERROR'
  | 'NAVIGATION_STEP_CHANGE'
  | 'LOADING_START'
  | 'LOADING_END'
  | 'ERROR_SET'
  | 'ERROR_CLEAR'
  | 'SESSION_UPDATE'
  | 'UI_UPDATE'
  | 'STATE_RESTORE';

// State Update Payload Interface
export interface StateUpdatePayload {
  action: StateUpdateAction;
  data?: any;
  timestamp?: Date;
  source?: string;
}

// State Selector Types
export type PipelineDataSelector = keyof PipelineState['data'];
export type ChatSelector = keyof ChatState;
export type NavigationSelector = keyof NavigationState;
export type LoadingSelector = keyof LoadingState;
export type ErrorSelector = keyof ErrorState;

// Computed State Selectors Interface
export interface ComputedSelectors {
  getCurrentStepData: () => any;
  getStepProgress: () => number;
  hasActiveSession: () => boolean;
  isStepCompleted: (stepId: string) => boolean;
  getRecentChatMessages: (count: number) => ChatMessage[];
  getErrorSummary: () => string | null;
}

// State Persistence Configuration
export interface StatePersistenceConfig {
  storageKey: string;
  persistedKeys: (keyof AppState)[];
  autoSave: boolean;
  saveInterval: number;
  maxStorageSize: number;
}

// Default State Values
export const DEFAULT_PROJECT_DETAILS_STATE: ProjectDetailsState = {
  project_name: null,
  industry: null,
  user_groups: null,
  justification:null
};


export const DEFAULT_PIPELINE_STATE: PipelineState = {
  run_id: null,
  project_name: null,
  industry: null,
  user_groups: null,
  current_step: null,
  completed_steps: [],
  data: {}
};

export const DEFAULT_CHAT_STATE: ChatState = {
  messages: [],
  isAiTyping: false,
  isLoading: false,
  errorMessage: null,
  lastResponse: null,
  currentInputText: '',
  restoredFromStorage: false
};

export const DEFAULT_NAVIGATION_STATE: NavigationState = {
  currentStepIndex: 0,
  currentStepId: 'understanding',
  canGoNext: false,
  canGoPrevious: false,
  completedSteps: [],
  stepsWithData: []
};

export const DEFAULT_LOADING_STATE: LoadingState = {
  isLoadingPipeline: false,
  isLoadingStep: false,
  isLoadingChat: false,
  currentOperation: null,
  loadingMessage: null
};

export const DEFAULT_ERROR_STATE: ErrorState = {
  pipelineError: null,
  stepError: null,
  chatError: null,
  networkError: null,
  lastErrorTimestamp: null
};

export const DEFAULT_SESSION_STATE: UserSessionState = {
  sessionId: null,
  isAuthenticated: false,
  lastActivity: null,
  preferences: {
    theme: 'light',
    autoSave: true,
    notifications: true
  }
};

export const DEFAULT_UI_STATE: UIState = {
  showSplitScreen: false,
  activeModal: null,
  sidebarCollapsed: false,
  currentView: 'brainstorming',
  roboBallState: 'idle'
};

export const DEFAULT_SUMMARY_DATA_STATE: SummaryDataState = {
  name: 'Loading...',
  description: 'Loading project data...',
  progress: 0,
  contributionText: 'Preparing your brainstorming session...',
  understandingText: 'Understanding insights will appear here.',
  features: [],
  personas: [],
  swot: [],
  timeline: [],
  isLoading: true,
  hasData: false,
  lastUpdated: null
};

// Complete Default App State
export const DEFAULT_APP_STATE: AppState = {
  pipeline: DEFAULT_PIPELINE_STATE,
  projectDetails: DEFAULT_PROJECT_DETAILS_STATE,
  chat: DEFAULT_CHAT_STATE,
  navigation: DEFAULT_NAVIGATION_STATE,
  loading: DEFAULT_LOADING_STATE,
  error: DEFAULT_ERROR_STATE,
  session: DEFAULT_SESSION_STATE,
  ui: DEFAULT_UI_STATE,
  summaryData: DEFAULT_SUMMARY_DATA_STATE,
  lastUpdated: new Date()
};
export type { PipelineState };

