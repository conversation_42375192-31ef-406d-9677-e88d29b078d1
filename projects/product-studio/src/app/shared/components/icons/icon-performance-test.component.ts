import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconsComponent } from './icons.component';
import { SvgCacheService } from './icons.component';

@Component({
  selector: 'app-icon-performance-test',
  standalone: true,
  imports: [CommonModule, IconsComponent],
  template: `
    <div class="performance-test-container">
      <h3>Icon Performance Test</h3>
      <p>Cache size: {{ cacheSize }}</p>
      
      <div class="test-icons">
        <h4>Multiple instances of the same icon (should use cache):</h4>
        <div class="icon-row">
          <awe-icons iconName="awe_edit" color="#007bff" *ngFor="let i of [1,2,3,4,5,6,7,8,9,10]"></awe-icons>
        </div>
        
        <h4>Different icons (should load from network once each):</h4>
        <div class="icon-row">
          <awe-icons iconName="awe_edit" color="#007bff"></awe-icons>
          <awe-icons iconName="awe_arrow_rightward_filled" color="#28a745"></awe-icons>
          <awe-icons iconName="awe_edit" color="#dc3545"></awe-icons>
          <awe-icons iconName="awe_arrow_rightward_filled" color="#ffc107"></awe-icons>
        </div>
      </div>
      
      <button (click)="refreshCacheInfo()" class="btn btn-primary">Refresh Cache Info</button>
      <button (click)="clearCache()" class="btn btn-warning">Clear Cache</button>
    </div>
  `,
  styles: [`
    .performance-test-container {
      padding: 20px;
      border: 1px solid #ddd;
      margin: 20px;
      border-radius: 8px;
    }
    
    .icon-row {
      display: flex;
      gap: 10px;
      margin: 10px 0;
      flex-wrap: wrap;
    }
    
    .test-icons {
      margin: 20px 0;
    }
    
    .btn {
      margin: 5px;
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    
    .btn-primary {
      background-color: #007bff;
      color: white;
    }
    
    .btn-warning {
      background-color: #ffc107;
      color: black;
    }
  `]
})
export class IconPerformanceTestComponent implements OnInit {
  cacheSize = 0;
  private svgCache = inject(SvgCacheService);

  ngOnInit() {
    this.refreshCacheInfo();
  }

  refreshCacheInfo() {
    this.cacheSize = this.svgCache.getCacheSize();
  }

  clearCache() {
    this.svgCache.clearCache();
    this.refreshCacheInfo();
  }
}
