// Main module
export { AuthModule } from './auth.module';

// Services
export { AuthService } from './services/auth.service';
export { AuthStateService } from './services/auth-state.service';
export { AuthTokenService } from './services/auth-token.service';
export { TokenStorageService } from './services/token-storage.service';

// Guards
export { AuthGuard } from './guards/auth.guard';

// Interceptors
export { AuthInterceptor } from './interceptors/auth.interceptor';

// Interfaces
export type { AuthConfig } from './interfaces/auth-config.interface';

// Re-export types from AuthService for convenience
export type {
  Team,
  Project,
  Domain,
  Organization
} from './services/auth.service';
