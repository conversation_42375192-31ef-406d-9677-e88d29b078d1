import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  On<PERSON><PERSON>roy,
  HostListener,
  ElementRef,
  ChangeDetectorRef,
  ViewChild,
  AfterViewInit,
  inject,
  signal,
  computed,
  ChangeDetectionStrategy,
  DestroyRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from '@awe/play-comp-library';
import { Router, NavigationEnd, NavigationStart } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { takeUntilDestroyed as takeUntilDestroyedOperator } from '@angular/core/rxjs-interop';
import {
  trigger,
  state,
  style,
  transition,
  animate,
  keyframes
} from '@angular/animations';
// import { ThemeService } from '../../services/theme/theme.service'; // Optional - can be injected if available
import { TokenStorageService } from '../../auth/services/token-storage.service';
import { AuthService } from '../../auth/services/auth.service';
import { CentralizedRedirectService } from '../../services/centralized-redirect.service';
import {
  DropdownComponent,
  DropdownOption,
  IconComponent,
} from '@ava/play-comp-library';
import { ButtonComponent } from '@ava/play-comp-library';
import { SharedNavItemComponent } from '../nav-item/nav-item.component';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';

// Organization hierarchy interfaces
interface Team {
  teamId: number;
  teamName: string;
}

interface Project {
  projectId: number;
  projectName: string;
  teams: Team[];
}

interface Domain {
  domainId: number;
  domainName: string;
  projects: Project[];
}

interface Organization {
  orgId: number;
  organizationName: string;
  domains: Domain[];
}

// Shared interfaces for navigation configuration
export interface SharedDropdownItem {
  label: string;
  description: string;
  route: string;
  icon: string;
}

export interface SharedNavItem {
  label: string;
  route: string;
  selected: boolean;
  hasDropdown: boolean;
  dropdownOpen?: boolean;
  icon: string;
  dropdownItems?: SharedDropdownItem[];
  disabled?: boolean;
}

export interface StudioApp {
  name: string;
  route: string;
  icon: string;
  description?: string;
}



// Dynamic center content configuration
export interface CenterContentConfig {
  type: 'navigation' | 'dynamic-text' | 'static-text';
  // For navigation type
  navItems?: SharedNavItem[];
  // For dynamic-text type
  defaultText?: string;
  routeTextMap?: Map<string, string> | { [route: string]: string };
  // For static-text type
  staticText?: string;
  // Styling
  textClass?: string;
  containerClass?: string;
}

// Route-based content mapping
export interface RouteContentMapping {
  route: string;
  displayText: string;
  icon?: string;
  description?: string;
}

/**
 * Header configuration interface for shared app header component
 *
 * @example
 * // Experience Studio configuration - single Ascendion logo
 * const config: HeaderConfig = {
 *   logoSrc: 'assets/svgs/ascendion-logo-dark.svg',
 *   showOnlyAscendionLogo: true,
 *   enableLogoAnimation: false, // Can be toggled programmatically
 *   projectName: 'Experience Studio',
 *   navItems: [...],
 * };
 *
 * @example
 * // Multi-studio configuration with animation
 * const config: HeaderConfig = {
 *   logoSrc: 'assets/svgs/default-logo.svg',
 *   enableLogoAnimation: true,
 *   logoAnimationStyle: 'fade',
 *   studioLogos: ['logo1.svg', 'logo2.svg', 'logo3.svg'],
 *   studioNames: ['Studio 1', 'Studio 2', 'Studio 3'],
 *   navItems: [...],
 * };
 */
export interface HeaderConfig {
  logoSrc: string;
  navItems: SharedNavItem[];
  showOrgSelector?: boolean;
  showThemeToggle?: boolean;
  showAppDrawer?: boolean;
  showProfileDropdown?: boolean;
  projectName?: string;
  redirectUrl?: string;
  currentApp?: string; // Current app name to filter from drawer
  availableApps?: StudioApp[]; // Available studio apps
  // Profile dropdown configuration
  showThemeToggleInProfile?: boolean;
  showLanguageSwitcher?: boolean;
  availableLanguages?: { code: string; name: string; flag?: string }[];
  // Disable/enable configuration
  disableThemeToggle?: boolean;
  disableLanguageChange?: boolean;
  // Logo animation configuration
  enableLogoAnimation?: boolean; // Enable/disable logo animation (default: true for backward compatibility)
  logoAnimationInterval?: number; // Animation interval in milliseconds (default: 3000)
  logoAnimationStyle?: 'fade' | 'smooth' | 'crossfade'; // Professional fade animation styles (default: 'fade')
  studioLogos?: string[]; // Custom studio logos array (light theme)
  studioNames?: string[]; // Custom studio names array
  // Theme-aware logo configuration
  studioLogosDark?: string[]; // Dark theme studio logos array
  enableThemeAwareLogos?: boolean; // Enable automatic theme-aware logo switching (default: false)
  // Single logo display configuration
  showOnlyAscendionLogo?: boolean; // Show only Ascendion dark logo (overrides other logo settings)
  // NEW: Center content configuration
  centerContent?: CenterContentConfig;
  // NEW: Route-based content mappings
  routeContentMappings?: RouteContentMapping[];
}

@Component({
  selector: 'shared-app-header',
  standalone: true,
  imports: [
    HeaderComponent,
    CommonModule,
    SharedNavItemComponent,
    DropdownComponent,
    ButtonComponent,
    ReactiveFormsModule,
    IconComponent,
  ],
  templateUrl: './app-header.component.html',
  styleUrls: ['./app-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('fadeInFromTop', [
      state('in', style({ opacity: 1, transform: 'translateY(0)' })),
      transition('void => *', [
        animate('500ms ease-in', keyframes([
          style({ opacity: 0, transform: 'translateY(-20px)', offset: 0 }),
          style({ opacity: 0.5, transform: 'translateY(-10px)', offset: 0.5 }),
          style({ opacity: 1, transform: 'translateY(0)', offset: 1.0 })
        ]))
      ]),
      transition('* => void', [
        animate('300ms ease-out', style({ opacity: 0, transform: 'translateY(-10px)' }))
      ])
    ]),
    trigger('contentChange', [
      transition('* => *', [
        animate('300ms ease-in-out', keyframes([
          style({ opacity: 1, transform: 'scale(1)', offset: 0 }),
          style({ opacity: 0, transform: 'scale(0.95)', offset: 0.5 }),
          style({ opacity: 1, transform: 'scale(1)', offset: 1.0 })
        ]))
      ])
    ])
  ]
})
export class SharedAppHeaderComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  @Input() config!: HeaderConfig;
  @Input() customNavItemComponent?: any; // Allow custom nav item component
  @Input() themeService?: any; // Allow injecting theme service from parent

  // Angular 19+ Dependency Injection
  private router = inject(Router);
  private cdr = inject(ChangeDetectorRef);
  private elementRef = inject(ElementRef);
  private destroyRef = inject(DestroyRef);

  // Angular 19+ Signals for reactive state management
  currentTheme = signal<'light' | 'dark'>('light');
  profileDropdownOpen = signal<boolean>(false);
  currentRoute = signal<string>('');
  centerContentText = signal<string>('');

  // Traditional properties for compatibility
  userName: string = '';
  userEmail: string = '';
  userDesignation: string = '';
  userAvatar: string = '';
  themeMenuIcon: string = '';
  isLoginPage: boolean = false; // Added to track login pages

  // Computed properties using Angular 19+ signals
  readonly displayCenterContent = computed(() => {
    const config = this.config?.centerContent;
    if (!config) return '';

    switch (config.type) {
      case 'static-text':
        return config.staticText || '';
      case 'dynamic-text':
        return this.centerContentText() || config.defaultText || '';
      case 'navigation':
      default:
        return '';
    }
  });

  readonly shouldShowCenterText = computed(() => {
    const config = this.config?.centerContent;
    return config && (config.type === 'static-text' || config.type === 'dynamic-text');
  });

  readonly shouldShowNavigation = computed(() => {
    const config = this.config?.centerContent;
    return !config || config.type === 'navigation';
  });

  // Animated logo management
  studioLogos: string[] = [
    'assets/svgs/ascendion-logo-dark.svg',
  ];
  studioLogosDark: string[] = []; // Dark theme logos array
  studioNames: string[] = ['Console', 'Experience Studio', 'Product Studio'];
  currentLogoIndex: number = 0;
  currentLogo: string = '';
  darkCurrentLogo: string = '';
  currentStudioName: string = '';
  logoAnimationInterval?: any;
  isLogoAnimating: boolean = false;

  // Language management
  currentLanguage: string = 'en';

  // Computed property for dynamic nav classes based on content
  get navItemsClasses(): string {
    const itemCount = this.config?.navItems?.length || 0;
    let classes = '';

    if (itemCount === 1) {
      classes += 'single-item ';
    } else if (itemCount >= 8) {
      classes += 'very-many-items ';
    } else if (itemCount >= 6) {
      classes += 'many-items ';
    }

    return classes.trim();
  }

  // Events for parent components to handle
  @Output() navigationEvent = new EventEmitter<string>();
  @Output() dropdownItemSelected = new EventEmitter<{
    route: string;
    label: string;
  }>();
  @Output() profileAction = new EventEmitter<string>();
  @Output() themeToggle = new EventEmitter<'light' | 'dark'>();
  @Output() languageChange = new EventEmitter<string>();
  @Output() orgConfigChange = new EventEmitter<any>();

  // Organization selector state (if enabled)
  isOrgDialogOpen: boolean = false;

  // App drawer state
  isAppDrawerOpen: boolean = false;

  // Organization dropdown state
  orgOptions: DropdownOption[] = [];
  domainOptions: DropdownOption[] = [];
  projectOptions: DropdownOption[] = [];
  teamOptions: DropdownOption[] = [];

  // Selected values (IDs for form)
  selectedOrg: string = '';
  selectedDomain: string = '';
  selectedProject: string = '';
  selectedTeam: string = '';

  // Selected names (for dropdown pre-selection)
  selectedOrgName: string = '';
  selectedDomainName: string = '';
  selectedProjectName: string = '';
  selectedTeamName: string = '';

  // Form for organization config
  headerConfigForm!: FormGroup;

  // Store the hierarchy data from API
  private hierarchyData: Organization[] = [];

  @ViewChild('orgPathTrigger', { static: false }) orgPathTrigger!: ElementRef;
  @ViewChild('popover', { static: false }) popoverRef!: ElementRef;
  popoverAlign: 'left' | 'right' = 'left';
  relmLogo = 'assets/svgs/ascendion-logo/header-ascendion-logo.svg';

  // Dropdown portal state
  dropdownPortal: {
    open: boolean;
    rect: DOMRect | null;
    items: SharedDropdownItem[];
    parentLabel: string;
    navItemId: string;
  } = {
    open: false,
    rect: null,
    items: [],
    parentLabel: '',
    navItemId: '',
  };

  // Subscription management
  private subscriptions = new Subscription();

  // Additional injected services
  private tokenStorage = inject(TokenStorageService);
  private authService = inject(AuthService);
  private formBuilder = inject(FormBuilder);

  constructor(private centralizedRedirectService: CentralizedRedirectService) {}

  ngOnInit(): void {
    this.initializeHeader();
    this.setupRouterSubscription();
    this.setupThemeSubscription();
    this.loadUserInfo();
    this.initializeForm();
    this.initializeLogo();
    this.initializeCenterContent();

    // Initialize organization selector if enabled
    if (this.config.showOrgSelector) {
      this.initOrgPathFromCookie();
    }

    // Setup route-based content updates using Angular 19+ patterns
    this.setupRouteBasedContentUpdates();
  }

  /**
   * Initialize center content configuration
   */
  private initializeCenterContent(): void {
    const config = this.config?.centerContent;
    if (!config) return;

    // Set initial content based on current route
    this.updateCenterContentForRoute(this.router.url);
  }

  /**
   * Setup route-based content updates using Angular 19+ takeUntilDestroyed
   */
  private setupRouteBasedContentUpdates(): void {
    this.router.events
      .pipe(
        filter((event): event is NavigationEnd => event instanceof NavigationEnd),
        takeUntilDestroyedOperator(this.destroyRef)
      )
      .subscribe((event: NavigationEnd) => {
        const url = (event as any).urlAfterRedirects ?? event.url;
        this.currentRoute.set(url);
        this.updateCenterContentForRoute(url);
        // Also ensure intendedDestination logic applies here as a safety
        this.resetIntendedDestinationOnMainRoute(url);
      });
  }

  /**
   * Update center content based on current route
   */
  private updateCenterContentForRoute(url: string): void {
    const config = this.config?.centerContent;
    if (!config || config.type !== 'dynamic-text') return;

    // Find matching route content
    const routeMapping = this.config?.routeContentMappings?.find(mapping =>
      url.includes(mapping.route) || url === mapping.route
    );

    if (routeMapping) {
      this.animateContentChange(routeMapping.displayText);
    } else if (config.routeTextMap) {
      // Check route text map
      const routeTextMap = config.routeTextMap instanceof Map
        ? config.routeTextMap
        : new Map(Object.entries(config.routeTextMap));

      for (const [route, text] of routeTextMap) {
        if (url.includes(route) || url === route) {
          this.animateContentChange(text);
          return;
        }
      }

      // Fallback to default text
      this.animateContentChange(config.defaultText || '');
    } else {
      // Fallback to default text
      this.animateContentChange(config.defaultText || '');
    }
  }

  /**
   * Update content immediately without animation
   */
  private animateContentChange(newText: string): void {
    // Simply set the text immediately without any animation
    this.centerContentText.set(newText);
  }







  ngAfterViewInit(): void {
    // Defer to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.updateActiveMenuItemByRoute(this.router.url);
      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe();
    if (this.logoAnimationInterval) {
      clearInterval(this.logoAnimationInterval);
    }
  }

  get headerShadowClass(): string {
  const project = (this.config?.projectName || '').toLowerCase();

  if (project.includes('console')) return 'console-shadow';
  if (project.includes('elder') || project.includes('wand') || project.includes('launchpad')) return 'elder-wand-shadow';
  if (project.includes('experience')) return 'experience-studio-shadow';
  if (project.includes('product')) return 'product-studio-shadow';

  return 'console-shadow'; // fallback
}

get headerWrapperClass(): string {
  const project = (this.config?.projectName || '').toLowerCase();

  if (project.includes('console')) return 'console-wrapper';
  if (project.includes('elder') || project.includes('wand') || project.includes('launchpad')) return 'elder-wand-wrapper';
  if (project.includes('experience')) return 'experience-studio-wrapper';
  if (project.includes('product')) return 'product-studio-wrapper';

  return 'console-wrapper'; // fallback
}


  // ========================================
  // LOGO ANIMATION METHODS
  // ========================================

  private initializeLogo(): void {
    // Check if only Ascendion logo should be displayed
    if (this.config.showOnlyAscendionLogo) {
      this.currentLogo = 'assets/svgs/ascendion-logo-light.svg';
      this.darkCurrentLogo = 'assets/svgs/ascendion-logo-dark.svg';
      this.currentStudioName = this.config.projectName || 'Experience Studio';
      this.studioLogos = [this.currentLogo];
      this.studioNames = [this.currentStudioName];
      this.studioLogosDark = [this.darkCurrentLogo]

      // Start animation only if explicitly enabled for single logo
      if (this.config.enableLogoAnimation === true) {
        this.startLogoAnimation();
      }
      return;
    }

    // Use config logos if provided, otherwise use defaults
    if (this.config.studioLogos && this.config.studioLogos.length > 0) {
      this.studioLogos = this.config.studioLogos;
      console.log('🎨 Using custom studio logos:', this.studioLogos);
    } else {
      console.log('🎨 Using default studio logos:', this.studioLogos);
    }

    // Initialize dark theme logos if theme-aware logos are enabled
    if (
      this.config.enableThemeAwareLogos &&
      this.config.studioLogosDark &&
      this.config.studioLogosDark.length > 0
    ) {
      this.studioLogosDark = this.config.studioLogosDark;
      console.log('🎨 Using custom dark studio logos:', this.studioLogosDark);
    }

    if (this.config.studioNames && this.config.studioNames.length > 0) {
      this.studioNames = this.config.studioNames;
    }

    // Set initial logo - use animated logos when animation is enabled
    if (
      this.config.enableLogoAnimation !== false &&
      this.getActiveStudioLogos().length > 0
    ) {
      // Use first logo from animated array (theme-aware)
      const activeLogos = this.getActiveStudioLogos();
      this.currentLogo = activeLogos[0];
      this.currentStudioName = this.studioNames[0];
      console.log('🎨 Initial animated logo set to:', this.currentLogo);
      console.log('🎨 Initial studio name set to:', this.currentStudioName);
    } else {
      // Fallback to config logoSrc when animation is disabled
      this.currentLogo = this.config.logoSrc;
      this.currentStudioName = this.config.projectName || 'Studio';
      console.log('🎨 Using static logo:', this.currentLogo);
    }

    // Start animation cycle only if enabled (default: true)
    if (this.config.enableLogoAnimation !== false) {
      this.startLogoAnimation();
    }
  }

  /**
   * Get the active studio logos array based on current theme
   * Returns dark logos if theme-aware logos are enabled and current theme is dark
   */
  private getActiveStudioLogos(): string[] {
    if (
      this.config.enableThemeAwareLogos &&
      this.currentTheme() === 'dark' &&
      this.studioLogosDark.length > 0
    ) {
      return this.studioLogosDark;
    }
    return this.studioLogos;
  }

  private startLogoAnimation(): void {
    // Don't start animation if explicitly disabled
    if (this.config.enableLogoAnimation === false) {
      return;
    }

    // Clear any existing interval
    if (this.logoAnimationInterval) {
      clearInterval(this.logoAnimationInterval);
    }

    // For single logo, create a subtle pulse effect instead of logo switching
    if (this.config.showOnlyAscendionLogo || this.studioLogos.length === 1) {
      const interval = this.config.logoAnimationInterval || 5000; // Longer interval for single logo
      this.logoAnimationInterval = setInterval(() => {
        this.animateSingleLogo();
      }, interval);
      return;
    }

    // Use config interval or default to 3 seconds for multiple logos
    const interval = this.config.logoAnimationInterval || 3000;

    // Start the animation cycle for multiple logos
    this.logoAnimationInterval = setInterval(() => {
      this.animateToNextLogo();
    }, interval);
  }

  /**
   * Animate single logo with subtle pulse effect
   */
  private animateSingleLogo(): void {
    if (this.isLogoAnimating) return;

    this.isLogoAnimating = true;

    const logoElement = document.querySelector('.animated-logo');
    if (logoElement) {
      logoElement.classList.add('logo-transitioning');
      logoElement.classList.add('logo-pulse');
    }

    // Remove animation classes after pulse completes
    setTimeout(() => {
      if (logoElement) {
        logoElement.classList.remove('logo-transitioning');
        logoElement.classList.remove('logo-pulse');
      }
      this.isLogoAnimating = false;
    }, 800); // Short pulse duration
  }

  private animateToNextLogo(): void {
    if (this.isLogoAnimating) return;

    this.isLogoAnimating = true;

    // Get animation style from config (default: 'fade')
    const animationStyle = this.config.logoAnimationStyle || 'fade';

    // Add animation classes
    const logoElement = document.querySelector('.animated-logo');
    if (logoElement) {
      logoElement.classList.add('logo-transitioning');

      // Apply specific animation style
      logoElement.classList.add(`logo-${animationStyle}`);
    }

    // Determine timing based on animation style
    const timings = {
      fade: { changeAt: 600, duration: 1200 }, // Professional fade timing
      smooth: { changeAt: 500, duration: 1000 }, // Smooth fade timing
      crossfade: { changeAt: 700, duration: 1400 }, // Crossfade timing
    };

    const timing = timings[animationStyle] || timings['fade'];

    // Change the image source at the optimal point in the animation
    setTimeout(() => {
      // Get active logos array (theme-aware)
      const activeLogos = this.getActiveStudioLogos();

      // Move to next logo index
      this.currentLogoIndex = (this.currentLogoIndex + 1) % activeLogos.length;
      this.currentLogo = activeLogos[this.currentLogoIndex];
      this.currentStudioName = this.studioNames[this.currentLogoIndex];

      // Trigger change detection
      this.cdr.detectChanges();
    }, timing.changeAt);

    // Remove animation classes after animation completes
    setTimeout(() => {
      if (logoElement) {
        logoElement.classList.remove('logo-transitioning');
        logoElement.classList.remove(`logo-${animationStyle}`);
      }
      this.isLogoAnimating = false;
    }, timing.duration);
  }

  public pauseLogoAnimation(): void {
    if (this.logoAnimationInterval && this.config.enableLogoAnimation !== false) {
      clearInterval(this.logoAnimationInterval);
      this.logoAnimationInterval = undefined;
    }
  }

  public resumeLogoAnimation(): void {
    if (!this.logoAnimationInterval && this.config.enableLogoAnimation !== false) {
      this.startLogoAnimation();
    }
  }

  /**
   * Enable logo animation programmatically
   * Useful for Experience Studio to enable animation on demand
   */
  public enableLogoAnimation(): void {
    if (this.config.showOnlyAscendionLogo || this.studioLogos.length === 1) {
      // For single logo, create a subtle animation effect
      this.config.enableLogoAnimation = true;
      this.startLogoAnimation();
    } else if (this.studioLogos.length > 1) {
      // For multiple logos, enable normal animation
      this.config.enableLogoAnimation = true;
      this.startLogoAnimation();
    }
  }

  /**
   * Disable logo animation programmatically
   */
  public disableLogoAnimation(): void {
    this.config.enableLogoAnimation = false;
    this.pauseLogoAnimation();
  }

  /**
   * Toggle logo animation on/off
   * Useful for Experience Studio to provide user control
   */
  public toggleLogoAnimation(): void {
    if (this.config.enableLogoAnimation === false) {
      this.enableLogoAnimation();
    } else {
      this.disableLogoAnimation();
    }
  }

  /**
   * Check if logo animation is currently enabled
   */
  public isLogoAnimationEnabled(): boolean {
    return this.config.enableLogoAnimation !== false;
  }

  private initializeHeader(): void {
    if (!this.config) {
      console.warn('SharedAppHeaderComponent: config is required');
      return;
    }

    // Set default values
    this.config.showOrgSelector = this.config.showOrgSelector ?? false;
    this.config.showThemeToggle = this.config.showThemeToggle ?? true;
    this.config.showAppDrawer = this.config.showAppDrawer ?? false;
    this.config.showProfileDropdown = this.config.showProfileDropdown ?? true;
    this.config.projectName = this.config.projectName ?? 'Application';
    this.config.redirectUrl = this.config.redirectUrl ?? '/';
    this.config.currentApp = this.config.currentApp ?? '';

    // Default studio apps
    this.config.availableApps = this.config.availableApps ?? [
      {
        name: 'Console',
        route: '/console',
        icon: 'assets/svgs/studios/console-icon.svg',
        description: 'Agent & Workflow Management',
      },
      {
        name: 'Experience Studio',
        route: '/experience-studio',
        icon: 'assets/svgs/studios/experience-studio-icon.svg',
        description: 'UI/UX Design & Prototyping',
      },
      {
        name: 'Product Studio',
        route: '/product-studio',
        icon: 'assets/svgs/studios/product-studio-icon.svg',
        description: 'Product Development',
      },
      {
        name: 'Launchpad',
        route: '/launchpad',
        icon: 'assets/svgs/studios/launchpad-icon.svg',
        description: 'Project Launch Hub',
      },
    ];

    // Profile dropdown defaults
    this.config.showThemeToggleInProfile =
      this.config.showThemeToggleInProfile ?? true;
    this.config.showLanguageSwitcher =
      this.config.showLanguageSwitcher ?? false;
    this.config.availableLanguages = this.config.availableLanguages ?? [
      { code: 'en', name: 'English' },
      { code: 'fil', name: 'Filipino' },
      { code: 'es', name: 'Español' },
    ];
  }

  private initializeForm(): void {
    this.headerConfigForm = this.formBuilder.group({
      org: ['', Validators.required],
      domain: ['', Validators.required],
      project: ['', Validators.required],
      team: ['', Validators.required],
    });
  }

  private setupRouterSubscription(): void {
    //Force-close dropdowns, portal, and overlays as soon as navigation starts
    const navigationStartSub = this.router.events
      .pipe(filter((event) => event instanceof NavigationStart))
      .subscribe(() => {
        this.closeDropdownPortal();
        this.profileDropdownOpen.set(false);
        this.isOrgDialogOpen = false;
        this.isAppDrawerOpen = false;
      });
    this.subscriptions.add(navigationStartSub);

    const routerSub = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        // Check if current route is a login page
        this.isLoginPage = this.checkIfLoginPage(event.url);
        this.updateActiveMenuItemByRoute(event.url);

        // Reset intended destination when navigating between main apps
        this.resetIntendedDestinationOnMainRoute(event.urlAfterRedirects ?? event.url);

        // Always close dropdown portal and all dropdowns on navigation
        this.closeDropdownPortal();
      });
    this.subscriptions.add(routerSub);

    // Check initial route - defer to avoid ExpressionChangedAfterItHasBeenCheckedError
    setTimeout(() => {
      this.isLoginPage = this.checkIfLoginPage(this.router.url);
      this.cdr.markForCheck();
    });
  }

  private checkIfLoginPage(url: string): boolean {
    // Check for login routes across different studios
    const loginRoutes = [
      '/login',
      '/auth/login',
      '/experience/login',
      '/product/login',
      '/console/login',
    ];
    return loginRoutes.some((route) => url.includes(route));
  }

  /**
   * Reset intendedDestination when navigating to a main route root
   * or switching between main apps. This keeps post-login redirect sane.
   */
private resetIntendedDestinationOnMainRoute(url: string): void {
  try {
    const mainRoots = ['/product', '/launchpad', '/experience', '/console'];
    const path = new URL(url, window.location.origin).pathname.replace(/\/+$/, '');
    if (mainRoots.includes(path)) {
      this.centralizedRedirectService.storeIntendedDestination(
        `${window.location.origin}${path}` 
      );
    }
  } catch {}
}

  private setupThemeSubscription(): void {
    // If theme service is provided, subscribe to theme changes
    if (this.themeService && this.themeService.themeObservable) {
      this.subscriptions.add(
        this.themeService.themeObservable.subscribe(
          (theme: 'light' | 'dark') => {
            this.currentTheme.set(theme);
            this.updateThemeAssets();
            this.cdr.markForCheck();
          },
        ),
      );
    }
    this.updateThemeAssets();
  }

  private loadUserInfo(): void {
    this.userName = this.tokenStorage.getDaName() || 'User';
    this.userEmail = (this.tokenStorage as any).getDaUserEmail?.() || ''; // Safe call if method exists
    this.userDesignation =
      (this.tokenStorage as any).getDaUserDesignation?.() || 'Employee'; // Safe call if method exists
    this.generateUserAvatar();
  }

  private generateUserAvatar(): void {
    // Generate avatar from user initials if no profile picture is available
    if (this.userName) {
      const nameParts = this.userName.trim().split(' ');
      let initials = '';

      if (nameParts.length >= 2) {
        // First letter of first name and first letter of last name
        initials = nameParts[0][0] + nameParts[nameParts.length - 1][0];
      } else if (nameParts.length === 1) {
        // Just first letter if only one name
        initials = nameParts[0][0];
      } else {
        initials = 'U'; // Default to 'U' for User
      }

      initials = initials.toUpperCase();

      // Generate a colored avatar with initials
      const colors = [
        '#8B5CF6',
        '#06B6D4',
        '#10B981',
        '#F59E0B',
        '#EF4444',
        '#8B5A2B',
        '#6366F1',
        '#EC4899',
      ];
      const colorIndex = this.userName.length % colors.length;
      const backgroundColor = colors[colorIndex];

      this.userAvatar = `data:image/svg+xml;base64,${btoa(`
        <svg width="40" height="40" xmlns="http://www.w3.org/2000/svg">
          <circle cx="20" cy="20" r="20" fill="${backgroundColor}"/>
          <text x="20" y="26" font-family="Inter, Arial, sans-serif" font-size="14" font-weight="600" fill="white" text-anchor="middle">${initials}</text>
        </svg>
      `)}`;
    }
  }

  private updateThemeAssets(): void {
    // Update theme-specific assets
    this.themeMenuIcon =
      this.currentTheme() === 'light'
        ? 'assets/svgs/header/toggle-theme/toggle-to-dark.svg'
        : 'assets/svgs/header/toggle-theme/toggle-to-light.svg';

    // Update current logo if theme-aware logos are enabled and animation is active
    if (
      this.config.enableThemeAwareLogos &&
      this.config.enableLogoAnimation !== false
    ) {
      const activeLogos = this.getActiveStudioLogos();
      if (
        activeLogos.length > 0 &&
        this.currentLogoIndex < activeLogos.length
      ) {
        this.currentLogo = activeLogos[this.currentLogoIndex];
        console.log('🎨 Theme changed - updated logo to:', this.currentLogo);
      }
    }
  }

  // Organization selector methods
  initOrgPathFromCookie(): void {
    const path = this.tokenStorage.getCookie('org_path');
    if (path) {
      const parts = path.split('::');
      const usecasePath = parts[0] || '';
      const usecaseIdPath = parts[1] || '';

      // Parse the IDs
      const ids = usecaseIdPath.split('@').map(Number);

      // Set form values (IDs)
      this.headerConfigForm.patchValue({
        org: ids[0]?.toString() || '',
        domain: ids[1]?.toString() || '',
        project: ids[2]?.toString() || '',
        team: ids[3]?.toString() || '',
      });

      // Store the IDs for form and the names for dropdown pre-selection
      this.selectedOrg = ids[0]?.toString() || '';
      this.selectedDomain = ids[1]?.toString() || '';
      this.selectedProject = ids[2]?.toString() || '';
      this.selectedTeam = ids[3]?.toString() || '';

      // Store the names for dropdown pre-selection
      const pathParts = usecasePath.split('@');
      this.selectedOrgName = pathParts[0] || '';
      this.selectedDomainName = pathParts[1] || '';
      this.selectedProjectName = pathParts[2] || '';
      this.selectedTeamName = pathParts[3] || '';

      // Load dropdown options
      this.loadData();
    } else {
      this.loadData();
    }
  }

  loadData(): void {
    this.authService.getOrganizationHierarchy().subscribe({
      next: (data: Organization[]) => {
        this.hierarchyData = data;
        this.loadOrganizations();

        // After loading organizations, load cascading dropdowns if we have pre-selected values
        if (this.selectedOrg) {
          this.loadDomains(this.selectedOrg);
          if (this.selectedDomain) {
            this.loadProjects(this.selectedDomain);
            if (this.selectedProject) {
              this.loadTeams(this.selectedProject);
            }
          }
        }
      },
      error: (error: any) => {
        console.error('Error loading organization data:', error);
      },
    });
  }

  loadOrganizations(): void {
    this.orgOptions = this.hierarchyData.map((org) => ({
      name: org.organizationName,
      value: org.orgId.toString(),
    }));
  }

  onOrgSelect(event: any): void {
    const selectedOrgId = event.selectedOptions?.[0]?.value;
    const selectedOrgName = event.selectedOptions?.[0]?.name;
    if (selectedOrgId) {
      this.selectedOrg = selectedOrgId;
      this.selectedOrgName = selectedOrgName;
      this.headerConfigForm.patchValue({ org: selectedOrgId });
      this.loadDomains(selectedOrgId);
      // Clear dependent dropdowns
      this.headerConfigForm.patchValue({ domain: '', project: '', team: '' });
      this.selectedDomain = '';
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedDomainName = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
      this.projectOptions = [];
      this.teamOptions = [];
    }
  }

  loadDomains(orgId: string): void {
    const org = this.hierarchyData.find((o) => o.orgId.toString() === orgId);
    if (org) {
      this.domainOptions = org.domains.map((domain) => ({
        name: domain.domainName,
        value: domain.domainId.toString(),
      }));
    } else {
      this.domainOptions = [];
    }
  }

  onDomainSelect(event: any): void {
    const selectedDomainId = event.selectedOptions?.[0]?.value;
    const selectedDomainName = event.selectedOptions?.[0]?.name;
    if (selectedDomainId) {
      this.selectedDomain = selectedDomainId;
      this.selectedDomainName = selectedDomainName;
      this.headerConfigForm.patchValue({ domain: selectedDomainId });
      this.loadProjects(selectedDomainId);
      // Clear dependent dropdowns
      this.headerConfigForm.patchValue({ project: '', team: '' });
      this.selectedProject = '';
      this.selectedTeam = '';
      this.selectedProjectName = '';
      this.selectedTeamName = '';
      this.teamOptions = [];
    }
  }

  loadProjects(domainId: string): void {
    const org = this.hierarchyData.find((o) =>
      o.domains.some((d) => d.domainId.toString() === domainId),
    );
    if (org) {
      const domain = org.domains.find(
        (d) => d.domainId.toString() === domainId,
      );
      if (domain) {
        this.projectOptions = domain.projects.map((project) => ({
          name: project.projectName,
          value: project.projectId.toString(),
        }));
      } else {
        this.projectOptions = [];
      }
    } else {
      this.projectOptions = [];
    }
  }

  onProjectSelect(event: any): void {
    const selectedProjectId = event.selectedOptions?.[0]?.value;
    const selectedProjectName = event.selectedOptions?.[0]?.name;
    if (selectedProjectId) {
      this.selectedProject = selectedProjectId;
      this.selectedProjectName = selectedProjectName;
      this.headerConfigForm.patchValue({ project: selectedProjectId });
      this.loadTeams(selectedProjectId);
      // Clear dependent dropdowns
      this.headerConfigForm.patchValue({ team: '' });
      this.selectedTeam = '';
      this.selectedTeamName = '';
    }
  }

  loadTeams(projectId: string): void {
    const org = this.hierarchyData.find((o) =>
      o.domains.some((d) =>
        d.projects.some((p) => p.projectId.toString() === projectId),
      ),
    );
    if (org) {
      const domain = org.domains.find((d) =>
        d.projects.some((p) => p.projectId.toString() === projectId),
      );
      if (domain) {
        const project = domain.projects.find(
          (p) => p.projectId.toString() === projectId,
        );
        if (project) {
          this.teamOptions = project.teams.map((team) => ({
            name: team.teamName,
            value: team.teamId.toString(),
          }));
        } else {
          this.teamOptions = [];
        }
      } else {
        this.teamOptions = [];
      }
    } else {
      this.teamOptions = [];
    }
  }

  onTeamSelect(event: any): void {
    const selectedTeamId = event.selectedOptions?.[0]?.value;
    const selectedTeamName = event.selectedOptions?.[0]?.name;
    if (selectedTeamId) {
      this.selectedTeam = selectedTeamId;
      this.selectedTeamName = selectedTeamName;
      this.headerConfigForm.patchValue({ team: selectedTeamId });
    }
  }

  get orgLabel(): string {
    // Try to get the org name from the org_path cookie
    const orgPath = this.tokenStorage.getCookie('org_path');
    if (orgPath) {
      const orgName = orgPath.split('::')[0].split('@')[0];
      if (orgName) return orgName;
    }
    // Fallback to dropdown label
    return (
      this.orgOptions.find((o) => o.value === this.selectedOrg)?.name ||
      'Select Organization'
    );
  }

  saveOrgPathAndClose(): void {
    if (this.headerConfigForm.valid) {
      const formValue = this.headerConfigForm.value;

      // Build the org path string
      const orgPath = `${this.selectedOrgName}@${this.selectedDomainName}@${this.selectedProjectName}@${this.selectedTeamName}::${formValue.org}@${formValue.domain}@${formValue.project}@${formValue.team}`;

      // Save to cookie
      this.tokenStorage.setCookie('org_path', orgPath);

      // Emit the change event
      this.orgConfigChange.emit({
        orgPath,
        selectedValues: {
          org: this.selectedOrg,
          domain: this.selectedDomain,
          project: this.selectedProject,
          team: this.selectedTeam,
        },
        selectedNames: {
          org: this.selectedOrgName,
          domain: this.selectedDomainName,
          project: this.selectedProjectName,
          team: this.selectedTeamName,
        },
      });

      this.closeOrgDialog();
    }
  }

  // Navigation methods
  navigateTo(route: string): void {
    // Defensive: Always close dropdowns before navigating
    this.closeDropdownPortal();
    this.router.navigate([route]);
    this.navigationEvent.emit(route);
  }

  selectMenuItem(index: number): void {
    this.config.navItems.forEach((item, i) => {
      item.selected = i === index;
    });
  }

  // Toggle dropdown and optionally force close the portal

  toggleDropdown(index: number, event?: MouseEvent): void {
    const clickedItem = this.config.navItems[index];

    const isAlreadyOpen = clickedItem.dropdownOpen;

    // Close all dropdowns first
    this.config.navItems.forEach((item) => {
      item.dropdownOpen = false;
    });

    if (isAlreadyOpen) {
      this.closeDropdownPortal();
      return;
    }

    // Open the clicked dropdown only
    clickedItem.dropdownOpen = true;
    // Set portal
    if (event) {
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
      this.onDropdownPortalOpen({
        rect,
        items: clickedItem.dropdownItems || [],
        parentLabel: clickedItem.label,
        navItemId: clickedItem.label,
      });
    }
  }

  onDropdownItemSelected(
    event: { route: string; label: string },
    parentIndex: number,
  ): void {
    this.navigateTo(event.route);
    this.dropdownItemSelected.emit(event);
    this.selectMenuItem(parentIndex);
    this.closeDropdownPortal();
  }

  onDropdownPortalOpen(event: {
    rect: DOMRect;
    items: SharedDropdownItem[];
    parentLabel: string;
    navItemId: string;
  }): void {
    this.dropdownPortal = {
      open: true,
      rect: event.rect,
      items: event.items,
      parentLabel: event.parentLabel,
      navItemId: event.navItemId,
    };
    //Force change detection to flush portal render and avoid "frozen" dropdowns
    this.cdr.detectChanges();
  }

  closeDropdownPortal(): void {
    this.dropdownPortal.open = false;
    this.dropdownPortal.navItemId = '';
    this.config.navItems.forEach((item) => (item.dropdownOpen = false));
  }

  // Profile dropdown methods
  toggleProfileDropdown(): void {
    this.profileDropdownOpen.set(!this.profileDropdownOpen());
  }

  logout(): void {
    if (this.tokenStorage.getLoginType() === 'basic') {
      this.authService.basicLogout().subscribe({
        next: () => {
          this.router.navigate(['/']);
          this.tokenStorage.deleteCookie('org_path');
          this.profileAction.emit('logout');
        },
        error: (error) => {
          console.error('Basic logout failed:', error);
          this.router.navigate(['/']);
          this.profileAction.emit('logout');
        },
      });
    } else {
      this.authService.logout('').subscribe({
        next: () => {
          this.tokenStorage.deleteCookie('org_path');
          this.profileAction.emit('logout');
        },
        error: (error) => {
          console.error('SSO logout failed:', error);
          this.profileAction.emit('logout');
        },
      });
    }
    this.profileAction.emit('logout');
  }

  // Theme methods
  toggleTheme(): void {
    if (this.themeService && this.themeService.toggleTheme) {
      // Use the injected theme service if available
      this.themeService.toggleTheme();
    } else {
      // Fallback to local theme toggle
      const newTheme = this.currentTheme() === 'light' ? 'dark' : 'light';
      this.currentTheme.set(newTheme);
      this.updateThemeAssets();
      this.themeToggle.emit(newTheme);
    }
  }

  // Language methods
  switchLanguage(languageCode: string): void {
    this.currentLanguage = languageCode;
    this.languageChange.emit(languageCode);
  }

  getCurrentLanguageName(): string {
    const language = this.config.availableLanguages?.find(
      (lang) => lang.code === this.currentLanguage,
    );
    return language?.name || 'English';
  }

  // App drawer methods
  toggleAppDrawer(): void {
    this.isAppDrawerOpen = !this.isAppDrawerOpen;

    if (this.isAppDrawerOpen) {
      // Calculate position to prevent overflow
      setTimeout(() => {
        this.calculateAppDrawerPosition();
      });
    }
  }

  closeAppDrawer(): void {
    this.isAppDrawerOpen = false;
  }

  navigateToApp(app: StudioApp): void {
    if (app.route.startsWith('http')) {
      // External URL
      window.open(app.route, '_blank');
    } else {
      // Internal route
      this.router.navigate([app.route]);
    }
    this.closeAppDrawer();
    this.navigationEvent.emit(app.route);
  }

  getFilteredApps(): StudioApp[] {
    if (!this.config.availableApps) return [];

    // Filter out the current app
    return this.config.availableApps.filter(
      (app) => app.name !== this.config.currentApp,
    );
  }

  // Organization selector methods (if enabled)
  toggleOrgDialog(): void {
    if (this.config.showOrgSelector) {
      this.isOrgDialogOpen = !this.isOrgDialogOpen;

      if (this.isOrgDialogOpen) {
        // Calculate popover position
        setTimeout(() => {
          this.calculatePopoverPosition();
        });
      }
    }
  }

  closeOrgDialog(): void {
    this.isOrgDialogOpen = false;
  }

  private calculatePopoverPosition(): void {
    if (this.orgPathTrigger && this.popoverRef) {
      const triggerRect =
        this.orgPathTrigger.nativeElement.getBoundingClientRect();
      const popoverRect = this.popoverRef.nativeElement.getBoundingClientRect();
      const viewportWidth = window.innerWidth;

      // Check if popover would overflow on the right
      if (triggerRect.left + popoverRect.width > viewportWidth - 20) {
        this.popoverAlign = 'right';
      } else {
        this.popoverAlign = 'left';
      }
    }
  }

  private calculateAppDrawerPosition(): void {
    // Find the app drawer dropdown element
    const appDrawerDropdown = this.elementRef.nativeElement.querySelector(
      '.app-drawer-dropdown',
    );
    if (appDrawerDropdown) {
      const dropdownRect = appDrawerDropdown.getBoundingClientRect();
      const viewportWidth = window.innerWidth;

      // If dropdown would overflow on the right, adjust position
      if (dropdownRect.right > viewportWidth - 20) {
        const overflow = dropdownRect.right - viewportWidth + 20;
        appDrawerDropdown.style.right = `${overflow}px`;
      } else {
        appDrawerDropdown.style.right = '0px';
      }
    }
  }

  // Navigation item click handler
  onNavItemClick(item: SharedNavItem, index: number, event: MouseEvent): void {
    if (item.disabled) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    if (item.hasDropdown && item.dropdownItems) {
      event.stopPropagation();
      this.toggleDropdown(index, event); //Only one place toggles
    } else {
      this.navigateTo(item.route);
      this.selectMenuItem(index);
    }
  }

  // Route-based active menu item update
  updateActiveMenuItemByRoute(url: string): void {
    // Reset all selections
    this.config.navItems.forEach((item) => {
      item.selected = false;
    });

    // Find the matching parent route or parent of a child route
    const parentItem = this.config.navItems.find((item) => {
      // Check if this is a direct match for the parent route
      if (url === item.route) {
        return true;
      }

      // Check if this is a dropdown parent with a matching child
      if (item.hasDropdown && item.dropdownItems) {
        // Check if the URL starts with the parent route path (for nested routes)
        // OR if any child route exactly matches the URL
        return (
          url.startsWith(item.route + '/') ||
          item.dropdownItems.some((child) => url === child.route)
        );
      }

      // Even if hasDropdown is false, check for dropdownItems
      if (!item.hasDropdown && item.dropdownItems) {
        return item.dropdownItems.some((child) => url === child.route);
      }

      return false;
    });

    if (parentItem) {
      parentItem.selected = true;
    } else {
      // Default to first non-disabled item if no match found
      const defaultItem = this.config.navItems.find((item) => !item.disabled);
      if (defaultItem) {
        defaultItem.selected = true;
      }
    }
  }

  // Document click listener to close dropdowns
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    // Close profile dropdown if clicking outside
    if (
      this.profileDropdownOpen() &&
      !this.elementRef.nativeElement.contains(target)
    ) {
      this.profileDropdownOpen.set(false);
    }

    // Close org dialog if clicking outside
    if (
      this.isOrgDialogOpen &&
      !target.closest('.org-path-dropdown-container')
    ) {
      this.closeOrgDialog();
    }

    // Close app drawer if clicking outside
    if (this.isAppDrawerOpen && !target.closest('.app-drawer-container')) {
      this.closeAppDrawer();
    }

    //SAFELY close dropdowns only if the click is fully outside nav items + dropdown menu
    const path = event.composedPath?.() || [];

    const clickedInsideDropdown = path.some((el: any) =>
      el?.classList?.contains?.('dropdown-portal-menu'),
    );

    const clickedInsideNavItem = path.some((el: any) =>
      el?.classList?.contains?.('nav-item-wrapper'),
    );

    if (
      this.dropdownPortal.open &&
      !clickedInsideDropdown &&
      !clickedInsideNavItem
    ) {
      this.closeDropdownPortal();
    }
  }
}
