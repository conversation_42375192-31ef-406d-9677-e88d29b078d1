<div class="chat-interface-container">
  <div class="messages-scroll-container">
    <div class="messages-container" #messagesContainer>
      <div
        *ngFor="let message of messages"
        class="message-wrapper"
        [ngClass]="message.from"
      >
        <div class="message-bubble">
          <ng-container *ngIf="message.text === '...'">
            <div class="dot-typing">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </ng-container>
          <ng-container *ngIf="message.text !== '...'">
            {{ message.text }}
          </ng-container>
        </div>
      </div>

      <!-- Space to ensure scrolling reaches the bottom -->
      <div class="message-spacer"></div>
    </div>
  </div>

  <div class="prompt-container">
    <div class="prompt-input-wrapper">
      <div *ngIf="files.length" class="files-container">
        <div class="file-bubble" *ngFor="let file of files">
          <div class="file-icon">
            {{ file.type.includes("zip") && "ZIP" }}
          </div>
          <div class="file-details">
            <div class="file-name">
              {{ file.name }}
            </div>
          </div>
          <button
            class="delete-btn"
            (click)="deleteFile(file.name)"
            aria-label="Delete node"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              aria-hidden="true"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
      <textarea
        class="prompt-input"
        [placeholder]="placeholder"
        [(ngModel)]="inputText"
        [disabled]="isDisabled"
        (keydown.enter)="$event.preventDefault(); handleSendMessage()"
      ></textarea>

      <div class="prompt-actions">
        <input
          [attr.accept]="fileType"
          [attr.multiple]="isMultiple"
          type="file"
          #fileInput
          hidden
          (change)="fileInput.files && uploadFile(fileInput.files)"
        />
        <button
          class="action-icon attachment-icon"
          (click)="
            handleIconClick({ name: 'attachment', index: 0 });
            handleFileUpload && fileInput?.click()
          "
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M21.4383 11.6622L12.2483 20.8522C11.1225 21.9781 9.59552 22.6106 8.00334 22.6106C6.41115 22.6106 4.88418 21.9781 3.75834 20.8522C2.63249 19.7264 2 18.1994 2 16.6072C2 15.015 2.63249 13.4881 3.75834 12.3622L12.9483 3.17224C13.697 2.42348 14.7267 2 15.8033 2C16.88 2 17.9097 2.42348 18.6583 3.17224C19.4071 3.92101 19.8306 4.9507 19.8306 6.02735C19.8306 7.10399 19.4071 8.13368 18.6583 8.88245L9.47834 18.0722C9.10396 18.4466 8.58911 18.6584 8.05084 18.6584C7.51257 18.6584 6.99773 18.4466 6.62334 18.0722C6.24896 17.6978 6.03711 17.183 6.03711 16.6447C6.03711 16.1064 6.24896 15.5916 6.62334 15.2172L15.0683 6.78224"
              stroke="#6566CD"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>

        <button
          class="action-icon send-icon"
          (click)="handleSendMessage()"
          [disabled]="isLoading || (!isOptionalInput && !inputText.trim())"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M22 2L11 13"
              stroke="#6566CD"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M22 2L15 22L11 13L2 9L22 2Z"
              stroke="#6566CD"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Warning Popup -->
<!-- this popup will be displayed if the files exceeds give maxFIleSize limit -->
<ava-popup
  [show]="showWarningPopup"
  [showTitle]="false"
  [showHeaderIcon]="false"
  [showInlineMessage]="true"
  [inlineIconName]="'info'"
  [inlineIconSize]="48"
  [inlineIconColor]="'#007bff'"
  [inlineMessage]="'Warning!'"
  [message]="
    'The total size of selected files exceeds {{fileSize}}. Please select files totaling less than {{fileSize}}.'
  "
  [showClose]="true"
  [showCancel]="true"
  [cancelButtonLabel]="'Okay'"
  [cancelButtonVariant]="'primary'"
  [popupWidth]="'400px'"
  (closed)="showWarningPopup = false"
>
</ava-popup>
<!--  -->
