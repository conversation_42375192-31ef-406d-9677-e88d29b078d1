import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  OnInit,
  HostListener,
  AfterViewChecked,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatMessage } from '../chat-window';
import { PopupComponent } from '@ava/play-comp-library';

@Component({
  selector: 'app-chat-interface',
  standalone: true,
  imports: [CommonModule, FormsModule, PopupComponent],
  templateUrl: './chat-interface.component.html',
  styleUrls: ['./chat-interface.component.scss'],
})
export class ChatInterfaceComponent
  implements OnInit, AfterViewChecked, OnChanges
{
  @Input() theme: 'light' | 'dark' = 'light';
  @Input() placeholder: string = 'Ask here';
  @Input() messages: ChatMessage[] = [];
  @Input() isLoading: boolean = false;
  @Input() isDisabled: boolean = false;
  @Input() isOptionalInput = false;
  @Input() handleFileUpload = false;
  @Input() fileType = '';
  @Input() maxFileSize: number | null = null; //Setting 100MB as default for now.
  @Input() isMultiple = false;

  @Output() messageSent = new EventEmitter<string>();
  @Output() attachmentClicked = new EventEmitter<void>();
  @Output() attachmentSelected = new EventEmitter<File[]>();

  fileSize = '';

  @ViewChild('messagesContainer') messagesContainer!: ElementRef;

  files: File[] = [];
  showWarningPopup = false;

  @Input() inputText: string = '';
  private shouldScrollToBottom: boolean = false;
  private previousMessagesLength = 0;

  // Icon configuration for the chat input
  inputIcons = [
    { name: 'attachment', tooltip: 'Add attachment' },
    { name: 'send', tooltip: 'Send message' },
  ];

  constructor() {}

  ngOnInit(): void {
    // Initialize with a welcome message if no messages exist
    if (this.maxFileSize) {
      this.fileSize = `${this.maxFileSize / (1024 * 1024)}MB`;
    }
    if (this.messages.length === 0) {
      this.messages = [
        {
          from: 'ai',
          text: 'Hi there, how can I help you today?',
        },
      ];
    }

    // Initial scroll
    this.shouldScrollToBottom = true;
    setTimeout(() => this.scrollToBottom(), 100);
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Monitor messages array for changes
    if (
      changes['messages'] &&
      !changes['messages'].firstChange &&
      this.messages.length !== this.previousMessagesLength
    ) {
      this.shouldScrollToBottom = true;
      this.previousMessagesLength = this.messages.length;
    }
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  scrollToBottom(): void {
    try {
      if (this.messagesContainer && this.messagesContainer.nativeElement) {
        this.messagesContainer.nativeElement.scrollTop =
          this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  @HostListener('keydown.enter', ['$event'])
  onEnterKeydown(event: KeyboardEvent): void {
    // Only prevent default and send if Shift key is not pressed
    if (!event.shiftKey) {
      event.preventDefault();
      this.handleSendMessage();
    }
  }

  handleSendMessage(): void {
    setTimeout(() => {
      this.files = [];
    });
    if (!this.inputText.trim() || this.isDisabled) {
      this.messageSent.emit('');
      return;
    }

    // Add user message to the chat
    this.messages = [
      ...this.messages,
      {
        from: 'user',
        text: this.inputText,
      },
    ];

    this.shouldScrollToBottom = true;
    this.previousMessagesLength = this.messages.length;

    // Emit the message to parent component
    const messageText = this.inputText;
    this.inputText = '';
    this.messageSent.emit(messageText);

    // If parent doesn't handle loading state, show a loading indicator
    if (!this.isLoading) {
      setTimeout(() => {
        // Add a temporary loading message if parent doesn't handle responses
        this.addLoadingIndicator();
      }, 100);
    }
  }

  handleIconClick(event: { name: string; index: number }): void {
    if (event.name === 'send') {
      this.handleSendMessage();
    } else if (event.name === 'attachment') {
      this.attachmentClicked.emit();
    }
  }

  addLoadingIndicator(showAlways = false): void {
    // Only add loading indicator if no parent handling

    const messages: any[] = [
      ...this.messages,
      {
        from: 'ai',
        text: '...',
      },
    ];

    if (showAlways) {
      this.messages = messages;
    }

    if (
      !this.isLoading &&
      this.messages[this.messages.length - 1].from === 'user'
    ) {
      this.messages = messages;
      this.shouldScrollToBottom = true;
      this.previousMessagesLength = this.messages.length;
    }
  }

  // Public method to add an AI response
  addAiResponse(text: string): void {
    // Remove any loading indicators
    this.messages = this.messages.filter((m) => m.text !== '...');

    // Add the actual response
    this.messages = [
      ...this.messages,
      {
        from: 'ai',
        text: text,
      },
    ];

    this.shouldScrollToBottom = true;
    this.previousMessagesLength = this.messages.length;
  }

  // This can be called by parent components to clear the chat
  clearChat(): void {
    this.messages = [];
    this.inputText = '';
    this.previousMessagesLength = 0;
  }

  uploadFile(file: FileList) {
    const uploadedFiles = [...this.files, ...file];
    const fileSise = uploadedFiles.reduce((a, b) => a + b.size, 0);

    if (this.maxFileSize && fileSise > this.maxFileSize) {
      this.showWarningPopup = true;
      return;
    }
    this.files = [...uploadedFiles];
    this.attachmentSelected.emit(this.files);
  }

  deleteFile(name: string) {
    this.files = this.files.filter((file) => file.name !== name);
    this.attachmentSelected.emit(this.files);
  }
}
