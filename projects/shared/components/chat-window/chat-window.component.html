<div class="chat-wrapper" [ngClass]="theme">
  <div class="chat-messages" #chatScrollContainer>
    <ng-container *ngFor="let message of chatMessages">
      <!-- User Message Card -->
      <div 
        *ngIf="message.from === 'user'"
        class="user-card"
        [ngClass]="theme">
        <div class="card-content markdown-content">
          <!-- Display the image if it exists in the message -->
          <div class="selected-image" *ngIf="message.imageDataUri">
            <div class="image-preview" (click)="showImagePreview(message.imageDataUri, 'Selected image')">
              <img [src]="message.imageDataUri" alt="Selected image" class="thumbnail-image" />
            </div>
          </div>
          <markdown [data]="message.text"></markdown>
        </div>
      </div>

      <!-- AI Message Card -->
      <div
        *ngIf="message.from === 'ai'"
        class="ai-card"
        [ngClass]="theme">
        <div class="card-content markdown-content">
          <markdown [data]="message.text"></markdown>
          <!-- Show loading dots when stepper is active but not yet added to this message -->
          <div *ngIf="showStepper && status !== 'COMPLETED'" class="loading-dots">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
      </div>
    </ng-container>

    <!-- Spacer to ensure content doesn't get hidden behind prompt bar -->
    <div class="prompt-spacer"></div>
  </div>

  <div class="prompt-bar" [ngClass]="theme">
    <input 
      type="text" 
      class="prompt-input"
      [placeholder]="defaultText"
      [(ngModel)]="textValue"
      [disabled]="!isCodeGenerationComplete"
      (keydown.enter)="isCodeGenerationComplete ? enterPressed.emit() : null">
    
    <div class="prompt-icons">
      <div 
        *ngFor="let icon of rightIcons; let i = index"
        class="prompt-icon"
        (click)="handleIconClick({name: icon.name, index: i})">
        <span class="icon-name">{{ icon.name }}</span>
      </div>
    </div>
  </div>
</div>

<!-- Image Preview Overlay -->
<div class="preview-overlay" *ngIf="showPreview && previewImage">
  <div class="preview-content">
    <div class="preview-header">
      <div class="preview-title">{{ previewImage.name }}</div>
      <button
        (click)="closeImagePreview()"
        role="button"
        tabindex="0"
        [attr.aria-label]="'Close preview'">X</button>
    </div>
    <div class="preview-body">
      <img [src]="previewImage.url" [alt]="previewImage.name" />
    </div>
  </div>
</div>