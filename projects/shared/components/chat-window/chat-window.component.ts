import { Component, Input, Output, EventEmitter, ViewChild, ElementRef, AfterViewChecked, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MarkdownModule } from 'ngx-markdown';

// Interface for chat message
export interface ChatMessage {
  from: 'user' | 'ai';
  text: string;
  imageDataUri?: string;
}

@Component({
  selector: 'app-chat-window',
  standalone: true,
  imports: [CommonModule, FormsModule, MarkdownModule],
  templateUrl: './chat-window.component.html',
  styleUrls: ['./chat-window.component.scss']
})
export class ChatWindowComponent implements OnInit, AfterViewChecked, OnDestroy {
  private isUserScrolling = false;
  private scrollTimeout: any = null;
  private isNearBottom = true;
  private _showStepper = false;

  // Preview image
  showPreview = false;
  previewImage: { url: string, name: string } | null = null;

  @Input() set showStepper(value: boolean) {
    this._showStepper = value;
    
    if (value) {
      // When stepper is activated, scroll to bottom to show loading dots
      setTimeout(() => {
        this.scrollToBottom();
      }, 50);
    }
  }
  
  get showStepper(): boolean {
    return this._showStepper;
  }

  private _progress = 0;
  
  @Input() set progress(value: number) {
    this._progress = value;
    // Scroll to bottom when progress changes
    setTimeout(() => {
      this.scrollToBottom();
    }, 50);
  }
  
  get progress(): number {
    return this._progress;
  }

  private _progressDescription = '';
  
  @Input() set progressDescription(value: string) {
    this._progressDescription = value;
    // Scroll to bottom when progress description changes
    setTimeout(() => {
      this.scrollToBottom();
    }, 50);
  }
  
  get progressDescription(): string {
    return this._progressDescription;
  }

  @Input() status: 'RUNNING' | 'COMPLETED' | 'ERROR' = 'RUNNING';

  private _selectedImageDataUri: string | null = null;
  
  @Input() set selectedImageDataUri(value: string | null) {
    this._selectedImageDataUri = value;
    if (value) {
      this.addImageToLastUserMessage();
    }
  }
  
  get selectedImageDataUri(): string | null {
    return this._selectedImageDataUri;
  }

  @Input() defaultText = '';
  @Input() leftIcons: any[] = [];
  @Input() rightIcons: any[] = [];
  @Input() isCodeGenerationComplete = true;

  constructor() {}

  private _theme: 'dark' | 'light' = 'light';
  
  @Input() set theme(value: 'dark' | 'light') {
    this._theme = value;
    // Update the theme for all messages
    this.updateMessagesTheme();
  }
  
  get theme(): 'dark' | 'light' {
    return this._theme;
  }

  private _chatMessages: ChatMessage[] = [];
  private shouldScrollToBottom = true;

  // Method to add image to the last user message
  private addImageToLastUserMessage() {
    if (!this._selectedImageDataUri) return;
    
    // If the last message is from the user, add the image to it
    const userMessages = this._chatMessages.filter(msg => msg.from === 'user');
    if (userMessages.length > 0) {
      const lastUserMessageIndex = this._chatMessages.lastIndexOf(userMessages[userMessages.length - 1]);
      if (lastUserMessageIndex !== -1) {
        this._chatMessages[lastUserMessageIndex].imageDataUri = this._selectedImageDataUri;
        // Trigger a refresh of the messages
        this.chatMessages = [...this._chatMessages];
      }
    }
  }

  // Update theme for all messages
  private updateMessagesTheme() {
    // Force update after the view has been checked
    setTimeout(() => {
      const userCards = document.querySelectorAll('.user-card');
      const aiCards = document.querySelectorAll('.ai-card');
      
      userCards.forEach(card => {
        card.classList.remove('dark', 'light');
        card.classList.add(this.theme);
      });
      
      aiCards.forEach(card => {
        card.classList.remove('dark', 'light');
        card.classList.add(this.theme);
      });
    }, 0);
  }

  @Input() get chatMessages(): ChatMessage[] {
    return this._chatMessages;
  }

  set chatMessages(messages: ChatMessage[]) {
    this._chatMessages = messages;
    
    // Schedule a scroll to bottom after the view updates
    this.shouldScrollToBottom = true;
    
    // Update card themes after the view is checked
    setTimeout(() => {
      this.updateMessagesTheme();
    }, 0);
  }

  private _textValue = '';
  
  @Input() get textValue(): string {
    return this._textValue;
  }
  
  set textValue(value: string) {
    this._textValue = value;
    this.textValueChange.emit(value);
  }

  @Output() textValueChange = new EventEmitter<string>();
  @Output() enterPressed = new EventEmitter<void>();
  @Output() iconClicked = new EventEmitter<{ name: string, index: number }>();
  @Output() imageTransferred = new EventEmitter<string>();

  @ViewChild('chatScrollContainer') private chatScrollContainer!: ElementRef;

  handleIconClick(event: { name: string, index: number }) {
    // If it's the photo icon, emit event to parent
    if (event.name === 'awe_photo') {
      // Delegate handling to parent component
    }
    
    // Emit all icon clicks to parent
    this.iconClicked.emit(event);
  }

  // Image preview methods
  showImagePreview(imageUrl: string, imageName: string) {
    this.previewImage = {
      url: imageUrl,
      name: imageName
    };
    this.showPreview = true;
  }
  
  closeImagePreview() {
    this.showPreview = false;
    this.previewImage = null;
  }
  
  getIconColor() {
    return this.theme === 'dark' ? 'white' : 'black';
  }

  // Called when a step is updated in the stepper
  onStepUpdated(event: any) {
    // Scroll to bottom when a step is updated
    setTimeout(() => {
      this.scrollToBottom();
    }, 50);
  }

  // Public method to manually trigger a scroll to bottom
  scrollToBottomManually() {
    // Set a flag and schedule the scroll
    this.shouldScrollToBottom = true;
    
    // Timeout to ensure the view has been checked
    setTimeout(() => {
      this.scrollToBottom();
    }, 50);
  }

  ngOnInit() {
    // Set up the scroll event listener to detect user scrolling
    setTimeout(() => {
      if (this.chatScrollContainer?.nativeElement) {
        this.setupScrollListener();
      }
    }, 100);
  }

  private setupScrollListener() {
    const scrollContainer = this.chatScrollContainer.nativeElement;
    
    scrollContainer.addEventListener('scroll', () => {
      // Check if user is scrolling
      this.isUserScrolling = true;
      
      // Clear any existing timeout
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout);
      }
      
      // Check if scrolled near bottom
      this.isNearBottom = this.isScrolledNearBottom();
      
      // Set a timeout to detect when scrolling stops
      this.scrollTimeout = setTimeout(() => {
        this.isUserScrolling = false;
      }, 150);
    });
  }

  private isScrolledNearBottom(): boolean {
    const scrollContainer = this.chatScrollContainer.nativeElement;
    const scrollPosition = scrollContainer.scrollTop + scrollContainer.clientHeight;
    const scrollHeight = scrollContainer.scrollHeight;
    
    // Consider within 50px of bottom as "near bottom"
    return scrollHeight - scrollPosition <= 50;
  }

  ngAfterViewChecked() {
    // If we should scroll to bottom and the user isn't currently scrolling
    if (this.shouldScrollToBottom && !this.isUserScrolling) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  ngOnDestroy() {
    // Clean up any timeouts
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }
  }

  // Helper method to scroll to the bottom of the chat
  public scrollToBottomOfChat() {
    if (this.chatScrollContainer?.nativeElement) {
      const scrollContainer = this.chatScrollContainer.nativeElement;
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
    }
  }

  // Main scroll function
  scrollToBottom() {
    if (this.chatScrollContainer?.nativeElement) {
      // If the user isn't currently scrolling or they were already near the bottom
      if (!this.isUserScrolling || this.isNearBottom) {
        this.scrollToBottomOfChat();
      }
    }
  }
}