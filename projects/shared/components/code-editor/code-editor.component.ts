import {
  Component,
  ElementRef,
  ViewChild,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  OnChanges,
  SimpleChanges
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormsModule } from '@angular/forms';
import { ButtonComponent, CubicalLoadingComponent } from '@ava/play-comp-library';
import { MonacoEditorModule, NgxMonacoEditorConfig } from 'ngx-monaco-editor-v2';

declare const monaco: any;

export type CodeLanguage = 'python' | 'javascript' | 'typescript' | 'json' | 'sql' | 'html' | 'css' | 'markdown' | 'yaml' | 'xml' | 'plaintext';
export type CodeEditorTheme = 'light' | 'dark';

export interface EditorActionButton {

  label: string;
  style?: 'primary' | 'secondary' | 'outline' | 'text' | 'danger';
  customClass?: string;
  icon?: string;
}

/**
 * Monaco Code Editor Component
 *
 * @Input title - Editor title displayed in header
 * @Input value - Initial code value
 * @Input language - Programming language (python, javascript, etc.)
 * @Input theme - Editor theme (light/dark)
 * @Input height - Editor height (default: 400px)
 * @Input readonly - Make editor read-only
 * @Input Control - Angular FormControl for form integration
 * @Input actionButtons - Array of custom action buttons
 * @Input footerText - Footer note text
 *
 * @Output valueChange - Emits when code changes
 * @Output primaryButtonSelected - Emits when Run button clicked
 * @Output actionButtonClicked - Emits when action button clicked
 * @Output editorReady - Emits when editor is initialized
 *
 * Usage:
 * <app-code-editor
 *   title="Code Editor"
 *   language="python"
 *   [Control]="formControl"
 *   (primaryButtonSelected)="runCode()">
 * </app-code-editor>
 */
@Component({
  selector: 'app-code-editor',
  standalone: true,
  imports: [CommonModule, FormsModule, ButtonComponent, CubicalLoadingComponent, MonacoEditorModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './code-editor.component.html',
  styleUrls: ['./code-editor.component.scss']
})
export class CodeEditorComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @ViewChild('ngxMonacoEditor', { static: false }) private readonly ngxMonacoEditor: any;

  @Input() title = '';
  @Input() value = '';
  @Input() language: CodeLanguage = 'python';
  @Input() theme: CodeEditorTheme = 'light';
  @Input() height = '400px';
  @Input() readonly = false;
  @Input() customCssClass = '';
  @Input() placeholder = '';
  @Input() Control: FormControl | null = null;
  @Input() actionButtons: EditorActionButton[] = [];
  @Input() footerText = '';
  @Input() isPrimaryButtonDisabled = false;


  @Output() readonly valueChange = new EventEmitter<string>();
  @Output() readonly primaryButtonSelected = new EventEmitter<void>();
  @Output() readonly actionButtonClicked = new EventEmitter<number>();
  @Output() readonly editorReady = new EventEmitter<any>();
  @Input() titleRequired = false;

  readonly state = {
    loading: true,
    error: false,
    errorMessage: null as string | null,
    processing: false,
  };

  // Monaco Editor options for ngx-monaco-editor-v2
  editorOptions: any = {};

  private editor: any = null;
  private valueChangeTimeout: any = null;
  private monacoModule: any;

  constructor(private readonly cdr: ChangeDetectorRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    // Update editor options for ngx-monaco-editor
    if (changes['language'] || changes['theme']) {
      this.setupEditorOptions();
    }

    if (this.editor) {
      if (changes['value'] && changes['value'].currentValue !== this.editor.getValue()) {
        this.editor.setValue(changes['value'].currentValue || '');
      }
      if (changes['language'] && changes['language'].currentValue && this.monacoModule) {
        this.monacoModule.editor.setModelLanguage(this.editor.getModel(), changes['language'].currentValue);
      }
      if (changes['theme'] && changes['theme'].currentValue) {
        this.applyTheme();
      }
    }
  }

  ngOnInit(): void {
    this.setupEditorOptions();
    if (this.Control?.value && !this.value) {
      this.value = this.Control.value;
    }
  }

  ngAfterViewInit(): void {
    // ngx-monaco-editor-v2 handles initialization automatically
    this.state.loading = false;
    this.cdr.markForCheck();
  }

  ngOnDestroy(): void {
    this.editor?.dispose();
  }

  private setupEditorOptions(): void {
    this.editorOptions = {
      theme: this.theme === 'dark' ? 'vs-dark' : 'vs',
      language: this.language,
      automaticLayout: true,
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      readOnly: this.readonly,
      fontSize: 14,
      fontFamily: "'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', 'Consolas', 'Courier New', monospace",
      tabSize: 4,
      lineHeight: 22,
      wordWrap: 'on',
      lineNumbers: 'on',
      folding: true,
      autoIndent: 'full',
      formatOnPaste: true,
      formatOnType: true,
      autoClosingBrackets: 'always',
      autoClosingQuotes: 'always',
      bracketPairColorization: { enabled: true },
      guides: { bracketPairs: true, indentation: true }
    };
  }

  // Called when ngx-monaco-editor is initialized
  onEditorInit(editor: any): void {
    this.editor = editor;
    this.monacoModule = (window as any).monaco;

    // Apply custom themes if available
    if (this.monacoModule) {
      this.defineCustomThemes(this.monacoModule);
      // Apply the custom theme
      this.editor.updateOptions({
        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'
      });
    }

    this.state.loading = false;
    this.editorReady.emit(this.editor);
    this.cdr.markForCheck();
  }

  private defineCustomThemes(monaco: any): void {
    // Custom themes are now defined in app.config.ts
    // This method is kept for compatibility but themes are already defined globally
  }

  private applyTheme(): void {
    if (this.editor && this.monacoModule) {
      this.editor.updateOptions({
        theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'
      });
    }
    // Also update the editor options for ngx-monaco-editor
    this.editorOptions = {
      ...this.editorOptions,
      theme: this.theme === 'dark' ? 'code-editor-dark' : 'code-editor-light'
    };
  }

  // Handle value changes from ngx-monaco-editor
  onValueChange(value: string): void {
    if (this.valueChangeTimeout) clearTimeout(this.valueChangeTimeout);
    this.valueChangeTimeout = setTimeout(() => {
      this.valueChange.emit(value);
      if (this.Control) {
        this.Control.setValue(value);
      }
    }, 200);
  }

  selectAll(): void {
    if (this.editor) {
      this.editor.setSelection(this.editor.getModel()!.getFullModelRange());
      this.editor.focus();
    }
  }

  clear(): void {
    if (this.editor) {
      this.editor.setValue('');
      this.editor.focus();
    }
  }

  showProcessingLoader(): void {
    this.state.processing = true;
    this.cdr.markForCheck();
  }

  hideProcessingLoader(): void {
    this.state.processing = false;
    this.cdr.markForCheck();
  }

  onPrimaryButtonClick(): void {
    this.showProcessingLoader();
    this.primaryButtonSelected.emit();
  }

  getValue(): string {
    return this.editor?.getValue() ?? '';
  }

  setValue(newValue: string): void {
    this.editor?.setValue(newValue);
  }

  setTheme(newTheme: CodeEditorTheme): void {
    this.theme = newTheme;
    this.applyTheme();
  }

  focus(): void {
    this.editor?.focus();
  }

  get isReady(): boolean {
    return !!this.editor && !this.state.loading;
  }

  onActionButtonClick(idx: number): void {
    this.actionButtonClicked.emit(idx);
  }

  retryInitialization(): void {
    this.state.error = false;
    this.state.loading = true;
    this.cdr.markForCheck();
    // The ngx-monaco-editor will reinitialize automatically
  }
}
