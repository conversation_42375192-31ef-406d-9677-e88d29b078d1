<div
  class="ava-console-card"
  [ngClass]="[variant, size]"
  [class.disabled]="disabled"
  [class.loading]="loading"
  [class.skeleton]="skeleton"
  [attr.role]="'listitem'"
  [attr.aria-disabled]="disabled"
>
  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="loading">
    <div class="loading-spinner"></div>
  </div>

  <!-- Skeleton Loader -->
  <div class="skeleton-loader" *ngIf="skeleton">
    <!-- Skeleton Header -->
    <div class="skeleton-header">
      <div class="skeleton-category">
        <div class="skeleton-icon"></div>
        <div class="skeleton-text skeleton-category-title"></div>
      </div>
      <div class="skeleton-value">
        <div class="skeleton-icon-small"></div>
        <div class="skeleton-text skeleton-number"></div>
      </div>
    </div>

    <!-- Skeleton Content -->
    <div class="skeleton-body">
      <div class="skeleton-text skeleton-title"></div>
      <div class="skeleton-text skeleton-description-1"></div>
      <div class="skeleton-text skeleton-description-2"></div>
      <div class="skeleton-text skeleton-description-3"></div>
    </div>

    <!-- Skeleton Footer -->
    <div class="skeleton-footer">
      <div class="skeleton-metadata">
        <div class="skeleton-meta-item">
          <div class="skeleton-icon-small"></div>
          <div class="skeleton-text skeleton-author"></div>
        </div>
        <div class="skeleton-meta-item">
          <div class="skeleton-icon-small"></div>
          <div class="skeleton-text skeleton-date"></div>
        </div>
      </div>
      <div class="skeleton-actions">
        <div class="skeleton-button"></div>
        <div class="skeleton-button"></div>
        <div class="skeleton-button"></div>
        <div class="skeleton-button skeleton-button-primary"></div>
      </div>
    </div>
  </div>

  <!-- Regular Content (hidden when skeleton is active) -->
  <div class="card-content" *ngIf="!skeleton">
    <!-- Header Section -->
    <div class="card-header">
      <div class="category-section">
        <div class="category-icon">
          <ava-icon [iconName]="categoryIcon" [iconSize]="18"></ava-icon>
        </div>
        <span class="category-title">{{ categoryTitle }}</span>
      </div>
      <div class="category-value">
        <ava-icon iconName="user" [iconSize]="14"></ava-icon>
        <span>{{ categoryValue }}</span>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="card-body">
      <h3 class="card-title" [title]="title" [attr.aria-label]="title">
        {{ title }}
      </h3>
      <p
        class="card-description"
        [class.truncated]="isDescriptionTruncated"
        [title]="isDescriptionTruncated ? description : ''"
        [attr.aria-label]="description"
      >
        {{ truncatedDescription }}
      </p>
    </div>

    <!-- Footer Section -->
    <div class="card-footer">
      <div class="metadata">
        <div class="author" *ngIf="author">
          <ava-icon iconName="user" [iconSize]="12"></ava-icon>
          <span>{{ author }}</span>
        </div>
        <div class="date" *ngIf="date">
          <ava-icon iconName="calendar-days" [iconSize]="12"></ava-icon>
          <span>{{ date }}</span>
        </div>
      </div>
      <div class="actions">
        <button
          *ngFor="let action of actions"
          class="action-btn"
          [class.primary]="action.isPrimary"
          [disabled]="action.disabled || disabled || loading || skeleton"
          type="button"
          [attr.aria-label]="action.label"
          [title]="action.tooltip"
          [attr.data-tooltip]="action.tooltip"
          (click)="onActionClick(action, $event)"
        >
          <ava-icon [iconName]="action.icon" [iconSize]="14"></ava-icon>
        </button>
      </div>
    </div>
  </div>
</div>
