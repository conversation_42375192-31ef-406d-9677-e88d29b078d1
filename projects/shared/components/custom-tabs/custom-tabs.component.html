<div class="custom-tabs-wrapper">
  <button
    *ngFor="let tab of tabs"
    class="tab-item"
    [class.active]="isActiveTab(tab.value)"
    [class.disabled]="isTabDisabled(tab.value)"
    (click)="onTabClick(tab.value)"
    [attr.aria-selected]="isActiveTab(tab.value)"
    [attr.aria-disabled]="isTabDisabled(tab.value)"
    role="tab"
  >
    <!-- Icon Box -->
    <div class="tab-icon-box" *ngIf="tab.icon && variant === 'icon'">
      <!-- Lucide icons for all tabs including prompts -->
      <lucide-icon
        [name]="getTabIcon(tab)"
        [size]="18"
        [color]="isActiveTab(tab.value) ? '#215AD6' : '#6B7280'"
      >
      </lucide-icon>
    </div>

    <!-- Label Below Icon -->
    <span class="tab-label">{{ tab.label }}</span>
  </button>
</div>
