<div class="drop-zone-canvas-container">
  <!-- Central Progress Bar -->
  <div class="central-progress">
    <div class="progress-ring">
      <svg class="progress-circle" width="120" height="120">
        <defs>
          <linearGradient
            id="progressGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop
              offset="33.91%"
              style="stop-color: var(--color-brand-primary)"
            />
            <stop
              offset="100%"
              style="stop-color: var(--global-color-aqua-500)"
            />
          </linearGradient>
        </defs>
        <circle
          class="progress-background"
          cx="60"
          cy="60"
          r="50"
          fill="none"
          stroke="#e5e7eb"
          stroke-width="8"
        />
        <circle
          class="progress-bar"
          cx="60"
          cy="60"
          r="50"
          fill="none"
          stroke="url(#progressGradient)"
          stroke-width="8"
          stroke-linecap="round"
          [style.stroke-dasharray]="314"
          [style.stroke-dashoffset]="314 - (314 * completionPercentage) / 100"
          transform="rotate(180 60 60)"
        />
      </svg>
      <div class="progress-content">
        <div class="progress-percentage">{{ completionPercentage }}%</div>
        <div class="progress-label">Complete</div>
      </div>
    </div>
  </div>

  <!-- Drop Zones -->
  <!-- <div class="drop-zones"> -->
  <!-- North: Prompts -->

  <div id="parent-box">
    <div id="box1">
      <div
        class="drop-zone north-zone prompts-zone"
        [class.has-nodes]="promptNodes.length > 0"
        [class.collapsed]="!isZoneExpanded('prompt')"
        (dragover)="onDragOver($event, 'prompt')"
        (drop)="onDrop($event, 'prompt')"
      >
        <div class="zone-header" (click)="toggleZone('prompt')">
          <div class="header-content">
            <div class="header-icon">
              <svg
                width="45"
                height="44"
                viewBox="0 0 45 44"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="0.679688"
                  width="44"
                  height="44"
                  rx="8"
                  fill="#5082EF"
                />
                <path
                  d="M20.6166 25.5C20.5273 25.1539 20.347 24.8381 20.0942 24.5854C19.8415 24.3327 19.5257 24.1523 19.1796 24.063L13.0446 22.481C12.94 22.4513 12.8478 22.3883 12.7822 22.3014C12.7166 22.2146 12.6812 22.1088 12.6812 22C12.6812 21.8912 12.7166 21.7854 12.7822 21.6986C12.8478 21.6118 12.94 21.5487 13.0446 21.519L19.1796 19.936C19.5256 19.8468 19.8413 19.6666 20.094 19.414C20.3467 19.1615 20.5272 18.8459 20.6166 18.5L22.1986 12.365C22.228 12.2599 22.291 12.1673 22.3779 12.1014C22.4649 12.0355 22.571 11.9998 22.6801 11.9998C22.7892 11.9998 22.8954 12.0355 22.9823 12.1014C23.0692 12.1673 23.1322 12.2599 23.1616 12.365L24.7426 18.5C24.8319 18.8461 25.0123 19.1619 25.265 19.4146C25.5177 19.6673 25.8336 19.8477 26.1796 19.937L32.3146 21.518C32.4201 21.5471 32.5132 21.61 32.5795 21.6971C32.6458 21.7841 32.6817 21.8906 32.6817 22C32.6817 22.1094 32.6458 22.2159 32.5795 22.3029C32.5132 22.39 32.4201 22.4529 32.3146 22.482L26.1796 24.063C25.8336 24.1523 25.5177 24.3327 25.265 24.5854C25.0123 24.8381 24.8319 25.1539 24.7426 25.5L23.1606 31.635C23.1312 31.7401 23.0682 31.8327 22.9813 31.8986C22.8944 31.9646 22.7882 32.0003 22.6791 32.0003C22.57 32.0003 22.4639 31.9646 22.3769 31.8986C22.29 31.8327 22.227 31.7401 22.1976 31.635L20.6166 25.5Z"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M30.6797 13V17"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M32.6797 15H28.6797"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M14.6797 27V29"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M15.6797 28H13.6797"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <h3 class="zone-title">System Prompt</h3>
          </div>
          <div class="header-actions">
            <span class="required-badge">Required</span>
            <button
              class="accordion-toggle"
              type="button"
              (click)="toggleZone('prompt'); $event.stopPropagation()"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                [style.transform]="
                  isZoneExpanded('prompt') ? 'rotate(180deg)' : 'rotate(0deg)'
                "
              >
                <path
                  d="M6 9L12 15L18 9"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="zone-content" *ngIf="isZoneExpanded('prompt')">
          <div *ngIf="promptNodes.length === 0" class="empty-state">
            Drop a prompt here
          </div>
          <div class="nodes-list">
            <div
              *ngFor="let node of promptNodes; trackBy: trackByNodeId"
              class="kanban-card"
            >
              <span class="card-title">{{ node.data?.name || "Prompt" }}</span>
              <button class="delete-btn" (click)="onDeleteNode(node.id)">
                ×
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- West: Knowledge -->
      <div
        class="drop-zone west-zone knowledge-zone"
        [class.has-nodes]="knowledgeNodes.length > 0"
        [class.collapsed]="!isZoneExpanded('knowledge')"
        (dragover)="onDragOver($event, 'knowledge')"
        (drop)="onDrop($event, 'knowledge')"
      >
        <div class="zone-header" (click)="toggleZone('knowledge')">
          <div class="header-content">
            <div class="header-icon">
              <svg
                width="45"
                height="44"
                viewBox="0 0 45 44"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="0.679688"
                  width="44"
                  height="44"
                  rx="8"
                  fill="#308666"
                />
                <path
                  d="M22.6797 17V31"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M26.6797 22H28.6797"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M26.6797 18H28.6797"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M13.6797 28C13.4145 28 13.1601 27.8946 12.9726 27.7071C12.785 27.5196 12.6797 27.2652 12.6797 27V14C12.6797 13.7348 12.785 13.4804 12.9726 13.2929C13.1601 13.1054 13.4145 13 13.6797 13H18.6797C19.7406 13 20.758 13.4214 21.5081 14.1716C22.2583 14.9217 22.6797 15.9391 22.6797 17C22.6797 15.9391 23.1011 14.9217 23.8513 14.1716C24.6014 13.4214 25.6188 13 26.6797 13H31.6797C31.9449 13 32.1993 13.1054 32.3868 13.2929C32.5743 13.4804 32.6797 13.7348 32.6797 14V27C32.6797 27.2652 32.5743 27.5196 32.3868 27.7071C32.1993 27.8946 31.9449 28 31.6797 28H25.6797C24.884 28 24.121 28.3161 23.5584 28.8787C22.9958 29.4413 22.6797 30.2044 22.6797 31C22.6797 30.2044 22.3636 29.4413 21.801 28.8787C21.2384 28.3161 20.4753 28 19.6797 28H13.6797Z"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M16.6797 22H18.6797"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M16.6797 18H18.6797"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <h3 class="zone-title">Knowledgebase</h3>
          </div>
          <div class="header-actions">
            <span class="optional-badge">Optional</span>
            <button
              class="accordion-toggle"
              type="button"
              (click)="toggleZone('knowledge'); $event.stopPropagation()"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                [style.transform]="
                  isZoneExpanded('knowledge')
                    ? 'rotate(180deg)'
                    : 'rotate(0deg)'
                "
              >
                <path
                  d="M6 9L12 15L18 9"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="zone-content" *ngIf="isZoneExpanded('knowledge')">
          <div *ngIf="knowledgeNodes.length === 0" class="empty-state">
            Drop knowledge here
          </div>
          <div class="nodes-list">
            <div
              *ngFor="let node of knowledgeNodes; trackBy: trackByNodeId"
              class="kanban-card"
            >
              <span class="card-title">{{
                node.data?.name || "Knowledge"
              }}</span>
              <button class="delete-btn" (click)="onDeleteNode(node.id)">
                ×
              </button>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="central-progress">
        <div class="progress-ring">
          <svg class="progress-circle" width="120" height="120">
            <circle
              class="progress-background"
              cx="60"
              cy="60"
              r="50"
              fill="none"
              stroke="#e5e7eb"
              stroke-width="8"
            />
            <circle
              class="progress-bar"
              cx="60"
              cy="60"
              r="50"
              fill="none"
              stroke="#3b82f6"
              stroke-width="8"
              stroke-linecap="round"
              [style.stroke-dasharray]="314"
              [style.stroke-dashoffset]="
                314 - (314 * completionPercentage) / 100
              "
              transform="rotate(-90 60 60)"
            />
          </svg>
          <div class="progress-content">
            <div class="progress-percentage">{{ completionPercentage }}%</div>
            <div class="progress-label">Complete</div>
          </div>
        </div>
      </div> -->
    </div>
    <div id="box2">
      <!-- East: Models -->
      <div
        class="drop-zone east-zone models-zone"
        [class.has-nodes]="modelNodes.length > 0"
        [class.collapsed]="!isZoneExpanded('model')"
        (dragover)="onDragOver($event, 'model')"
        (drop)="onDrop($event, 'model')"
      >
        <div class="zone-header" (click)="toggleZone('model')">
          <div class="header-content">
            <div class="header-icon">
              <svg
                width="45"
                height="44"
                viewBox="0 0 45 44"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="0.679688"
                  width="44"
                  height="44"
                  rx="8"
                  fill="#997BCF"
                />
                <path
                  d="M31.6797 18C31.6793 17.6493 31.5867 17.3048 31.4112 17.0012C31.2357 16.6975 30.9834 16.4454 30.6797 16.27L23.6797 12.27C23.3756 12.0945 23.0308 12.0021 22.6797 12.0021C22.3286 12.0021 21.9837 12.0945 21.6797 12.27L14.6797 16.27C14.376 16.4454 14.1237 16.6975 13.9481 17.0012C13.7726 17.3048 13.68 17.6493 13.6797 18V26C13.68 26.3508 13.7726 26.6952 13.9481 26.9989C14.1237 27.3025 14.376 27.5547 14.6797 27.73L21.6797 31.73C21.9837 31.9056 22.3286 31.998 22.6797 31.998C23.0308 31.998 23.3756 31.9056 23.6797 31.73L30.6797 27.73C30.9834 27.5547 31.2357 27.3025 31.4112 26.9989C31.5867 26.6952 31.6793 26.3508 31.6797 26V18Z"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M13.9795 17L22.6795 22L31.3795 17"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
                <path
                  d="M22.6797 32V22"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <h3 class="zone-title">AI Model</h3>
          </div>
          <div class="header-actions">
            <span class="required-badge">Required</span>
            <button
              class="accordion-toggle"
              type="button"
              (click)="toggleZone('model'); $event.stopPropagation()"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                [style.transform]="
                  isZoneExpanded('model') ? 'rotate(180deg)' : 'rotate(0deg)'
                "
              >
                <path
                  d="M6 9L12 15L18 9"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <div class="zone-content" *ngIf="isZoneExpanded('model')">
          <div *ngIf="modelNodes.length === 0" class="empty-state">
            Drop a model here
          </div>
          <div class="nodes-list">
            <div
              *ngFor="let node of modelNodes; trackBy: trackByNodeId"
              class="kanban-card"
            >
              <span class="card-title">{{ node.data?.name || "Model" }}</span>
              <button class="delete-btn" (click)="onDeleteNode(node.id)">
                ×
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- South: Guardrails/Tools -->
      <div
        class="drop-zone south-zone"
        [class.guardrails-zone]="config.agentType === 'individual'"
        [class.tools-zone]="config.agentType === 'collaborative'"
        [class.has-nodes]="bottomNodes.length > 0"
        [class.collapsed]="
          !isZoneExpanded(
            config.agentType === 'individual' ? 'guardrail' : 'tool'
          )
        "
        (dragover)="
          onDragOver(
            $event,
            config.agentType === 'individual' ? 'guardrail' : 'tool'
          )
        "
        (drop)="
          onDrop(
            $event,
            config.agentType === 'individual' ? 'guardrail' : 'tool'
          )
        "
      >
        <div
          class="zone-header"
          (click)="
            toggleZone(config.agentType === 'individual' ? 'guardrail' : 'tool')
          "
        >
          <div class="header-content">
            <div class="header-icon">
              <svg
                width="45"
                height="44"
                viewBox="0 0 45 44"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                *ngIf="config.agentType === 'individual'"
              >
                <rect
                  x="0.679688"
                  width="44"
                  height="44"
                  rx="8"
                  fill="#DC2626"
                />
                <path
                  d="M30.6797 23C30.6797 28 27.1797 30.5 23.0197 31.95C22.8019 32.0238 22.5652 32.0202 22.3497 31.94C18.1797 30.5 14.6797 28 14.6797 23V16C14.6797 15.7347 14.785 15.4804 14.9726 15.2929C15.1601 15.1053 15.4145 15 15.6797 15C17.6797 15 20.1797 13.8 21.9197 12.28C22.1315 12.099 22.401 11.9995 22.6797 11.9995C22.9583 11.9995 23.2278 12.099 23.4397 12.28C25.1897 13.81 27.6797 15 29.6797 15C29.9449 15 30.1993 15.1053 30.3868 15.2929C30.5743 15.4804 30.6797 15.7347 30.6797 16V23Z"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>

              <svg
                width="45"
                height="44"
                viewBox="0 0 45 44"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                *ngIf="config.agentType === 'collaborative'"
              >
                <rect
                  x="0.679688"
                  width="44"
                  height="44"
                  rx="8"
                  fill="#D97706"
                />
                <path
                  d="M25.3795 16.3C25.1963 16.4869 25.0937 16.7382 25.0937 17C25.0937 17.2617 25.1963 17.5131 25.3795 17.7L26.9795 19.3C27.1665 19.4832 27.4178 19.5858 27.6795 19.5858C27.9413 19.5858 28.1926 19.4832 28.3795 19.3L32.1495 15.53C32.6524 16.6412 32.8046 17.8792 32.586 19.0791C32.3674 20.279 31.7883 21.3838 30.9258 22.2463C30.0634 23.1087 28.9586 23.6878 27.7587 23.9064C26.5588 24.1251 25.3207 23.9728 24.2095 23.47L17.2995 30.38C16.9017 30.7778 16.3622 31.0013 15.7995 31.0013C15.2369 31.0013 14.6974 30.7778 14.2995 30.38C13.9017 29.9822 13.6782 29.4426 13.6782 28.88C13.6782 28.3174 13.9017 27.7778 14.2995 27.38L21.2095 20.47C20.7067 19.3588 20.5545 18.1207 20.7731 16.9208C20.9917 15.7209 21.5708 14.6161 22.4333 13.7537C23.2957 12.8913 24.4005 12.3121 25.6004 12.0935C26.8003 11.8749 28.0384 12.0271 29.1495 12.53L25.3895 16.29L25.3795 16.3Z"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
            <h3 class="zone-title">
              {{ config.agentType === "individual" ? "Guardrails" : "Tools" }}
            </h3>
          </div>
          <div class="header-actions">
            <span class="optional-badge">Optional</span>
            <button
              class="accordion-toggle"
              type="button"
              (click)="
                toggleZone(
                  config.agentType === 'individual' ? 'guardrail' : 'tool'
                );
                $event.stopPropagation()
              "
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                [style.transform]="
                  isZoneExpanded(
                    config.agentType === 'individual' ? 'guardrail' : 'tool'
                  )
                    ? 'rotate(180deg)'
                    : 'rotate(0deg)'
                "
              >
                <path
                  d="M6 9L12 15L18 9"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
        <div
          class="zone-content"
          *ngIf="
            isZoneExpanded(
              config.agentType === 'individual' ? 'guardrail' : 'tool'
            )
          "
        >
          <div *ngIf="bottomNodes.length === 0" class="empty-state">
            Drop
            {{ config.agentType === "individual" ? "guardrails" : "tools" }}
            here
          </div>
          <div class="nodes-list">
            <div
              *ngFor="let node of bottomNodes; trackBy: trackByNodeId"
              class="kanban-card"
              [class.guardrail-card]="config.agentType === 'individual'"
            >
              <div class="card-content">
                <span class="card-title">{{
                  node.data?.name ||
                    (config.agentType === "individual" ? "Guardrail" : "Tool")
                }}</span>

                <!-- Toggle switch for guardrails only -->
                <div
                  *ngIf="config.agentType === 'individual'"
                  class="guardrail-toggle"
                >
                  <label class="toggle-switch">
                    <input
                      type="checkbox"
                      [checked]="isGuardrailEnabled(node.id)"
                      (change)="onGuardrailCheckboxChange($event, node.id)"
                    />
                    <span class="toggle-slider"></span>
                  </label>
                </div>
                <button
                  *ngIf="config.agentType === 'individual'"
                  class="delete-btn"
                  (click)="onDeleteNode(node.id)"
                >
                  ×
                </button>
              </div>
              <button
                *ngIf="config.agentType === 'collaborative'"
                class="delete-btn"
                (click)="onDeleteNode(node.id)"
              >
                ×
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- </div> -->
</div>
