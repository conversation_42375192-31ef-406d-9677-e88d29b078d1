import { CommonModule } from '@angular/common';
import {
  Component,
  Input,
  Output,
  EventEmitter,
  TemplateRef,
  ContentChild,
} from '@angular/core';
import { CanvasNode } from '../canvas-board/canvas-board.component';

export interface DropZoneNode {
  id: string;
  type: 'prompt' | 'model' | 'knowledge' | 'guardrail' | 'tool';
  data: any;
}

export interface DropZoneConfig {
  agentType: 'individual' | 'collaborative';
  allowedCategories: {
    prompts: { max: number; required: boolean };
    models: { max: number; required: boolean };
    knowledge: { max: number; required: boolean };
    guardrails: { max: number; required: boolean };
    tools: { max: number; required: boolean };
  };
}

@Component({
  selector: 'app-drop-zone-canvas',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './drop-zone-canvas.component.html',
  styleUrls: ['./drop-zone-canvas.component.scss'],
})
export class DropZoneCanvasComponent {
  @ContentChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;

  @Input() config!: DropZoneConfig;
  @Input() nodes: CanvasNode[] = [];

  @Output() nodeDropped = new EventEmitter<{
    node: any;
    zone: string;
    position: { x: number; y: number };
  }>();
  @Output() nodeDeleted = new EventEmitter<string>();
  @Output() guardrailToggled = new EventEmitter<{
    nodeId: string;
    enabled: boolean;
  }>();

  // Accordion state management
  zoneStates = {
    prompt: true, // expanded by default
    model: true, // expanded by default
    knowledge: true, // expanded by default
    guardrail: true, // expanded by default
    tool: true, // expanded by default
  };

  // Track guardrail enable/disable states
  guardrailStates: { [nodeId: string]: boolean } = {};

  // Computed properties for categorized nodes
  get promptNodes(): CanvasNode[] {
    return this.nodes.filter((node) => node.data?.type === 'prompt');
  }

  get modelNodes(): CanvasNode[] {
    return this.nodes.filter((node) => node.data?.type === 'model');
  }

  get knowledgeNodes(): CanvasNode[] {
    return this.nodes.filter((node) => node.data?.type === 'knowledge');
  }

  get bottomNodes(): CanvasNode[] {
    if (this.config.agentType === 'individual') {
      return this.nodes.filter((node) => node.data?.type === 'guardrail');
    } else {
      return this.nodes.filter((node) => node.data?.type === 'tool');
    }
  }

  // Progress calculation
  get completionPercentage(): number {
    let completed = 0;
    let total = 2; // Only 2 required: prompt and model

    // Check required components only
    if (this.promptNodes.length > 0) completed++;
    if (this.modelNodes.length > 0) completed++;

    // Knowledge, Guardrails, and Tools are now optional (don't count toward completion)

    return Math.round((completed / total) * 100);
  }

  onDragOver(event: DragEvent, zoneType: string): void {
    event.preventDefault();
    // Try both possible drag data formats
    const dragData =
      event.dataTransfer?.getData('application/reactflow') ||
      event.dataTransfer?.getData('text/plain');
    if (dragData) {
      try {
        const data = JSON.parse(dragData);
        // Check if the dragged item is compatible with this zone
        if (this.isCompatibleWithZone(data, zoneType)) {
          event.dataTransfer!.dropEffect = 'copy';
        } else {
          event.dataTransfer!.dropEffect = 'none';
        }
      } catch (e) {
        event.dataTransfer!.dropEffect = 'none';
      }
    }
  }

  onDrop(event: DragEvent, zoneType: string): void {
    event.preventDefault();
    // Try both possible drag data formats
    const dragData =
      event.dataTransfer?.getData('application/reactflow') ||
      event.dataTransfer?.getData('text/plain');
    if (dragData) {
      try {
        const data = JSON.parse(dragData);

        console.log('🎯 Drop zone received data:', { data, zoneType });

        // Check compatibility
        if (!this.isCompatibleWithZone(data, zoneType)) {
          console.log(
            'Incompatible node type for this zone:',
            data.type,
            'vs',
            zoneType,
          );
          return; // Bounce back
        }

        console.log('✅ Compatible drop detected');

        // Check limits
        if (!this.canAddToZone(zoneType)) {
          console.log('Zone limit reached, replacing existing node');
          // For single-node zones, we'll replace the existing node
        }

        // Calculate position within the zone
        const rect = (event.currentTarget as Element).getBoundingClientRect();
        const position = {
          x: event.clientX - rect.left,
          y: event.clientY - rect.top,
        };

        console.log('✅ Emitting nodeDropped event');
        this.nodeDropped.emit({
          node: data,
          zone: zoneType,
          position,
        });
      } catch (e) {
        console.error('Invalid drag data:', e);
      }
    }
  }

  onDeleteNode(nodeId: string): void {
    // Clean up guardrail state if it's a guardrail node
    this.cleanupGuardrailState(nodeId);

    this.nodeDeleted.emit(nodeId);
  }

  trackByNodeId(index: number, node: any): string {
    return node.id;
  }

  // Guardrail toggle functionality
  onGuardrailToggle(nodeId: string, enabled: boolean): void {
    this.guardrailStates[nodeId] = enabled;
    this.guardrailToggled.emit({ nodeId, enabled });
  }

  onGuardrailCheckboxChange(event: Event, nodeId: string): void {
    const checkbox = event.target as HTMLInputElement;
    if (checkbox) {
      this.onGuardrailToggle(nodeId, checkbox.checked);
    }
  }

  isGuardrailEnabled(nodeId: string): boolean {
    // Default to true (enabled) if not explicitly set
    return this.guardrailStates[nodeId] !== false;
  }

  // Initialize guardrail state when node is added
  initializeGuardrailState(nodeId: string, enabled: boolean = true): void {
    if (this.guardrailStates[nodeId] === undefined) {
      this.guardrailStates[nodeId] = enabled;
    }
  }

  // Clean up guardrail state when node is deleted
  cleanupGuardrailState(nodeId: string): void {
    delete this.guardrailStates[nodeId];
  }

  // Accordion toggle functionality
  toggleZone(zoneType: string): void {
    if (zoneType === 'guardrail' || zoneType === 'tool') {
      // Handle both guardrail and tool zones
      const actualType =
        this.config.agentType === 'individual' ? 'guardrail' : 'tool';
      this.zoneStates[actualType as keyof typeof this.zoneStates] =
        !this.zoneStates[actualType as keyof typeof this.zoneStates];
    } else {
      this.zoneStates[zoneType as keyof typeof this.zoneStates] =
        !this.zoneStates[zoneType as keyof typeof this.zoneStates];
    }
  }

  // Get zone state for template
  isZoneExpanded(zoneType: string): boolean {
    if (zoneType === 'guardrail' || zoneType === 'tool') {
      const actualType =
        this.config.agentType === 'individual' ? 'guardrail' : 'tool';
      return this.zoneStates[actualType as keyof typeof this.zoneStates];
    }
    return this.zoneStates[zoneType as keyof typeof this.zoneStates];
  }

  private isCompatibleWithZone(dragData: any, zoneType: string): boolean {
    const nodeType = dragData.type || dragData.data?.type;

    switch (zoneType) {
      case 'prompt':
        return nodeType === 'prompt';
      case 'model':
        return nodeType === 'model';
      case 'knowledge':
        return nodeType === 'knowledge';
      case 'guardrail':
        return nodeType === 'guardrail';
      case 'tool':
        return nodeType === 'tool';
      default:
        return false;
    }
  }

  private canAddToZone(zoneType: string): boolean {
    switch (zoneType) {
      case 'prompt':
        return (
          this.promptNodes.length < this.config.allowedCategories.prompts.max
        );
      case 'model':
        return (
          this.modelNodes.length < this.config.allowedCategories.models.max
        );
      case 'knowledge':
        return (
          this.knowledgeNodes.length <
          this.config.allowedCategories.knowledge.max
        );
      case 'guardrail':
        return (
          this.bottomNodes.length < this.config.allowedCategories.guardrails.max
        );
      case 'tool':
        return (
          this.bottomNodes.length < this.config.allowedCategories.tools.max
        );
      default:
        return false;
    }
  }
}
