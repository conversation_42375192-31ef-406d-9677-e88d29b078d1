import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaginationComponent } from '../pagination/pagination.component';

@Component({
  selector: 'app-page-footer',
  standalone: true,
  imports: [CommonModule, PaginationComponent],
  templateUrl: './page-footer.component.html',
  styleUrls: ['./page-footer.component.scss']
})
export class PageFooterComponent {
  @Input() totalItems: number = 0;
  @Input() currentPage: number = 1;
  @Input() itemsPerPage: number = 12;
  @Input() visiblePageCount: number = 5;
  
  @Output() pageChange = new EventEmitter<number>();
  
  onPageChange(page: number): void {
    this.pageChange.emit(page);
  }
} 