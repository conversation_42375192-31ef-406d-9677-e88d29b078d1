<div class="pagination-container" *ngIf="totalPages > 0">
  <!-- Previous Page Button -->
  <button 
    class="page-nav prev" 
    [class.disabled]="currentPage === 1"
    (click)="previousPage()"
    [attr.aria-disabled]="currentPage === 1"
    aria-label="Previous page"
  >
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </button>
  
  <!-- Page Numbers -->
  <div class="page-numbers">
    <!-- First page -->
    <ng-container *ngIf="pages.length > 0">
      <button 
        class="page-number" 
        [class.active]="pages[0] === currentPage"
        (click)="changePage(pages[0])"
        [attr.aria-current]="pages[0] === currentPage ? 'page' : null"
      >
        {{ pages[0] }}
      </button>
      
      <!-- Start ellipsis -->
      <span class="ellipsis" *ngIf="showStartEllipsis">...</span>
    </ng-container>
    
    <!-- Middle pages -->
    <ng-container *ngFor="let page of pages; let i = index">
      <ng-container *ngIf="i > 0 && i < pages.length - 1">
        <button 
          class="page-number" 
          [class.active]="page === currentPage"
          (click)="changePage(page)"
          [attr.aria-current]="page === currentPage ? 'page' : null"
        >
          {{ page }}
        </button>
      </ng-container>
    </ng-container>
    
    <!-- Last page -->
    <ng-container *ngIf="pages.length > 1">
      <!-- End ellipsis -->
      <span class="ellipsis" *ngIf="showEllipsis">...</span>
      
      <button 
        class="page-number" 
        [class.active]="pages[pages.length-1] === currentPage"
        (click)="changePage(pages[pages.length-1])"
        [attr.aria-current]="pages[pages.length-1] === currentPage ? 'page' : null"
      >
        {{ pages[pages.length-1] }}
      </button>
    </ng-container>
  </div>
  
  <!-- Next Page Button -->
  <button 
    class="page-nav next" 
    [class.disabled]="currentPage === totalPages"
    (click)="nextPage()"
    [attr.aria-disabled]="currentPage === totalPages"
    aria-label="Next page"
  >
    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>
  </button>
</div>