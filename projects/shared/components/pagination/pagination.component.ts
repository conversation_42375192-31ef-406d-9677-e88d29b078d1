import { Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-pagination',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.scss']
})
export class PaginationComponent implements OnChanges {
  @Input() totalItems: number = 0;
  @Input() currentPage: number = 1;
  @Input() itemsPerPage: number = 12;
  @Input() visiblePageCount: number = 5;
  @Output() pageChange = new EventEmitter<number>();
  
  totalPages: number = 0;
  pages: number[] = [];
  showEllipsis: boolean = false;
  showStartEllipsis: boolean = false;

  ngOnChanges(changes: SimpleChanges): void {
    this.calculatePages();
  }

  calculatePages(): void {
    this.totalPages = Math.ceil(this.totalItems / this.itemsPerPage);
    
    if (this.totalPages <= this.visiblePageCount) {
      // If we have fewer pages than the visible count, show all pages
      this.pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);
      this.showEllipsis = false;
      this.showStartEllipsis = false;
    } else {
      const maxPagesBeforeCurrentPage = Math.floor((this.visiblePageCount - 1) / 2);
      const maxPagesAfterCurrentPage = Math.ceil((this.visiblePageCount - 1) / 2);
      
      let startPage = Math.max(1, this.currentPage - maxPagesBeforeCurrentPage);
      let endPage = Math.min(this.totalPages, startPage + this.visiblePageCount - 1);
      
      // Adjust if we're near the end of the page range
      if (endPage >= this.totalPages) {
        startPage = Math.max(1, this.totalPages - this.visiblePageCount + 1);
        endPage = this.totalPages;
      }
      
      // Generate page numbers
      this.pages = Array.from(
        { length: endPage - startPage + 1 }, 
        (_, i) => startPage + i
      );
      
      // Add indicators for ellipses
      this.showStartEllipsis = startPage > 1;
      this.showEllipsis = endPage < this.totalPages;
      
      // Always include first and last page if needed
      if (startPage > 1) {
        this.pages.unshift(1);
      }
      
      if (endPage < this.totalPages) {
        this.pages.push(this.totalPages);
      }
    }
  }

  changePage(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.pageChange.emit(this.currentPage);
      this.calculatePages();
    }
  }

  previousPage(): void {
    this.changePage(this.currentPage - 1);
  }

  nextPage(): void {
    this.changePage(this.currentPage + 1);
  }
} 