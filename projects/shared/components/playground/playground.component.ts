import { CommonModule } from '@angular/common';
import {
  AfterViewChecked,
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  Input,
  OnChanges,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import { ChatMessage } from '../chat-window';
import { FormsModule, FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  IconComponent,
  ToggleComponent,
  DropdownOption,
} from '@ava/play-comp-library';

interface Tool {
  id: number;
  name: string;
}

@Component({
  selector: 'app-playground',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ToggleComponent,
    IconComponent,
  ],
  templateUrl: './playground.component.html',
  styleUrl: './playground.component.scss',
})
export class PlaygroundComponent
  implements OnInit, OnChanges, AfterViewChecked
{
  isMenuOpen = false;
  isToolMenuOpen = false;
  @Output() promptChange = new EventEmitter<DropdownOption>();
  @Input() promptOptions: DropdownOption[] = [];
  @Input() selectedValue: string = 'default'; // Input for pre-selected value
  @Input() agentType: string = 'individual'; // Input for agent type ('individual' or 'collaborative')
  @Input() showChatInteractionToggles: boolean = false; // Input to show conversational and template toggles
  @Input() showAiPrincipleToggle: boolean = false; // Input to show AI principle toggle
  @Input() showDropdown: boolean = true; // Input to control dropdown visibility
  @Input() showAgentNameInput: boolean = false; // Input to show disabled agent name input field
  @Input() agentNamePlaceholder: string = 'Agent Name'; // Placeholder for agent name input
  @Input() displayedAgentName: string = ''; // Agent name to display in disabled input
  @Input() showFileUploadButton: boolean = false; // Controls visibility of attach file button
  selectedPrompt: string = 'default';
  @Input() playgroundTitle: string = '';
  // Form control for agent name display
  agentNameDisplayControl = new FormControl({ value: '', disabled: true });

  // Chat data
  showCopiedToast = false;
  inputText: string = '';
  previousMessagesLength = 0;
  shouldScrollToBottom = false;
  @Input() messages: ChatMessage[] = [];
  @Input() isLoading: boolean = false;
  @Input() isDisabled: boolean = false;
  @Input() showLoader: boolean = true;
  @Output() messageSent = new EventEmitter<string>();
  @Output() conversationalToggle = new EventEmitter<boolean>();
  @Output() templateToggle = new EventEmitter<boolean>();
  @Output() filesSelected = new EventEmitter<any[]>();
  @ViewChild('messagesContainer') messagesContainer!: ElementRef;
  @ViewChild('fileInput') fileInput!: ElementRef;
  @Input() showApprovalButton: boolean = true;
  @Output() approvalRequested = new EventEmitter<void>();
  @Input() isMinimalView: boolean = false;

  // Simple toggle properties for display only
  public isConvChecked: boolean = true;
  public isUseTemplate: boolean = false;

  // File upload properties
  public filesUploadedData: any[] = [];
  @Input() acceptedFileType: string =
    '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';

  ngOnInit(): void {
    this.messages = [
      {
        from: 'ai',
        text: 'Hi there, how can I help you today?',
      },
    ];
    this.shouldScrollToBottom = true;

    // Set selected prompt from input
    if (this.selectedValue) {
      this.selectedPrompt = this.selectedValue;
      // Update displayed agent name if showing agent name input
      if (this.showAgentNameInput && !this.displayedAgentName) {
        this.displayedAgentName = this.selectedValue;
        this.agentNameDisplayControl.setValue(this.selectedValue);
      }
    }

    // Initialize agent name display control
    if (this.displayedAgentName) {
      this.agentNameDisplayControl.setValue(this.displayedAgentName);
    }
  }

  ngOnChanges(changes: any): void {
    // Update selectedPrompt when selectedValue input changes
    if (changes['selectedValue'] && changes['selectedValue'].currentValue) {
      // The selectedValue from parent should be the name (for dropdown display)
      this.selectedPrompt = changes['selectedValue'].currentValue;
    }

    // Update agent name display control when displayedAgentName input changes
    if (
      changes['displayedAgentName'] &&
      changes['displayedAgentName'].currentValue !== undefined
    ) {
      this.agentNameDisplayControl.setValue(
        changes['displayedAgentName'].currentValue,
      );
    }
  }

  ngAfterViewChecked() {
    if (this.shouldScrollToBottom) {
      this.scrollToBottom();
      this.shouldScrollToBottom = false;
    }
  }

  scrollToBottom(): void {
    try {
      if (this.messagesContainer && this.messagesContainer.nativeElement) {
        // Scroll to bottom to show latest messages
        this.messagesContainer.nativeElement.scrollTop =
          this.messagesContainer.nativeElement.scrollHeight;
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }

  handleSendMessage(): void {
    if (!this.inputText.trim() || this.isDisabled) {
      return;
    }

    // Add user message to the chat
    this.messages = [
      ...this.messages,
      {
        from: 'user',
        text: this.inputText,
      },
    ];
    this.shouldScrollToBottom = true;

    // Emit the message to parent component
    const messageText = this.inputText;
    this.inputText = '';
    this.messageSent.emit(messageText);

    // Clear uploaded files after sending message
    this.clearFiles();
  }

  private clearFiles(): void {
    this.filesUploadedData = [];
    this.filesSelected.emit(this.filesUploadedData);
  }

  toggleMenu() {
    this.isMenuOpen = !this.isMenuOpen;
  }

  onAiPrincipleToggle(event: any) {
    console.log('AI Principles toggle:', event);
  }

  onConversationalToggle(event: any) {
    this.isConvChecked = event;

    // If conversational is enabled, disable template
    if (event && this.isUseTemplate) {
      this.isUseTemplate = false;
      this.templateToggle.emit(false);
    }

    console.log('Conversational mode:', event);
    this.conversationalToggle.emit(event);
  }

  onTemplateToggle(event: any) {
    this.isUseTemplate = event;

    // If template is enabled, disable conversational
    if (event && this.isConvChecked) {
      this.isConvChecked = false;
      this.conversationalToggle.emit(false);
    }

    console.log('Use template:', event);
    this.templateToggle.emit(event);
  }

  onPromptChange(selectionData: any): void {
    // The dropdown component emits an object with selectedOptions and selectedValue
    // selectedValue contains the name of the selected option
    let selectedName: string;

    if (typeof selectionData === 'string') {
      selectedName = selectionData;
    } else if (selectionData && selectionData.selectedValue) {
      selectedName = selectionData.selectedValue;
    } else if (
      selectionData &&
      selectionData.selectedOptions &&
      selectionData.selectedOptions.length > 0
    ) {
      selectedName = selectionData.selectedOptions[0].name;
    } else {
      return;
    }

    this.selectedPrompt = selectedName;

    // Update displayed agent name if showing agent name input
    if (this.showAgentNameInput) {
      this.displayedAgentName = selectedName;
      this.agentNameDisplayControl.setValue(selectedName);
    }

    // Find the option by name
    const selectedOption = this.promptOptions.find(
      (opt) => opt.name === selectedName,
    );

    if (selectedOption) {
      this.promptChange.emit(selectedOption);
    }
  }

  copyToClipboard(text: string): void {
    navigator.clipboard.writeText(text).then(() => {
      this.showCopiedToast = true;
      setTimeout(() => {
        this.showCopiedToast = false;
      }, 2000);
    });
  }
  save() {
    this.isMenuOpen = false;
    console.log('Save clicked');
    // your save logic here
  }

  export() {
    this.isMenuOpen = false;
    console.log('Export clicked');
    // your export logic here
  }

  // Hide menu when clicking outside
  @HostListener('document:click', ['$event'])
  onClickOutside(event: Event) {
    const target = event.target as HTMLElement;
    if (!target.closest('.btn-menu')) {
      this.isMenuOpen = false;
    }
  }

  @HostListener('keydown.enter', ['$event'])
  onEnterKeydown(event: KeyboardEvent): void {
    // Only prevent default and send if Shift key is not pressed
    if (!event.shiftKey) {
      event.preventDefault();
      this.handleSendMessage();
    }
  }

  // File upload methods
  onFileSelected(event: any): void {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.filesUploadedData = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        this.filesUploadedData.push({
          id: `file_${Date.now()}_${i}`,
          documentName: file.name,
          isImage: file.type.startsWith('image/'),
          file: file,
        });
      }

      console.log('Files selected:', this.filesUploadedData);
      this.filesSelected.emit(this.filesUploadedData);
    }
  }

  removeFile(index: number): void {
    this.filesUploadedData.splice(index, 1);
    this.filesSelected.emit(this.filesUploadedData);
  }

  // Track by function for ngFor performance
  trackByIndex(index: number, _item: any): number {
    return index;
  }

  onApprovalClick() {
    this.approvalRequested.emit();
  }

  autoResize(textarea: HTMLTextAreaElement): void {
    // Reset height to auto to get the correct scrollHeight
    textarea.style.height = 'auto';

    // Set the height to the scrollHeight to fit content
    const newHeight = Math.min(textarea.scrollHeight, 200); // Max height of 200px
    textarea.style.height = newHeight + 'px';
  }
}
