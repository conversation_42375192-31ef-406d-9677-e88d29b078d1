<div
  class="toggle-container"
  [style.background]="containerBackgroundColor"
  [style.border-color]="containerBorderColor"
>
  <div
    *ngFor="let option of options"
    class="toggle-option"
    [class.selected]="option.value === selectedValue"
    [class.disabled]="disabled"
    [style.background]="
      option.value === selectedValue ? selectedBackgroundColor : 'transparent'
    "
    [style.border-color]="
      option.value === selectedValue ? selectedBorderColor : 'transparent'
    "
    [style.color]="
      option.value === selectedValue ? selectedTextColor : unselectedTextColor
    "
    (click)="onSelect(option.value)"
  >
    {{ option.label }}
  </div>
</div>
