import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'shared-toggle-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './toggle-button.component.html',
  styleUrls: ['./toggle-button.component.scss'],
})
export class ToggleButtonComponent {
  @Input() options: Array<{ label: string; value: string }> = [];
  @Input() selectedValue: string = '';
  @Input() disabled: boolean = false;

  // Color props
  @Input() selectedBackgroundColor: string =
    'var(--build-agents-toggle-active-bg) !important';
  @Input() selectedBorderColor: string = '#6C96F2';
  @Input() selectedTextColor: string = '#FFFFFF';
  @Input() unselectedTextColor: string = '#3B3F46';
  @Input() containerBackgroundColor: string = '#FFFFFF';
  @Input() containerBorderColor: string = '#F0F1F2';

  @Output() selectionChange = new EventEmitter<string>();

  onSelect(value: string) {
    if (!this.disabled && value !== this.selectedValue) {
      this.selectedValue = value;
      this.selectionChange.emit(value);
    }
  }
}
