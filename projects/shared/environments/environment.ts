// Helper function to safely get environment variables from window.env
// It will throw an error if a key is not found, ensuring all required envs are present.
const getRequiredEnv = (key: string): string => {
  // Extend the Window interface to include 'env'
  interface EnvWindow extends Window {
    env?: Record<string, string>;
  }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(
      `Environment variable '${key}' is not defined in window.env.`,
    );
  }
  return String(value); // Ensure the value is returned as a string
};

// Helper function to safely get environment variables with fallback
const getEnvWithFallback = (key: string, fallback: string): string => {
  try {
    return getRequiredEnv(key);
  } catch {
    return fallback;
  }
};

// Detect current project context
const detectProjectContext = (): 'console' | 'elder-wand' | 'unknown' => {
  try {
    const currentPath = window.location.pathname;
    const currentPort = window.location.port;

    // Check for elder-wand specific indicators
    if (currentPath.includes('/launchpad/') || currentPort === '52906') {
      return 'elder-wand';
    }

    // Check for console specific indicators
    if (currentPath.includes('/console/') || currentPort === '4203') {
      return 'console';
    }

    // Default context detection based on available environment variables
    try {
      getRequiredEnv('elderWandUrl');
      return 'elder-wand';
    } catch {
      return 'console';
    }
  } catch {
    return 'unknown';
  }
};

// ---

// Shared Environment configuration
// This environment file can be used by both Console and Elder Wand applications
// It dynamically detects the project context and provides appropriate defaults
export const environment = {
  production: false, // This often remains a static build-time flag

  // Application URLs (constructed dynamically)
  elderWandUrl: getEnvWithFallback(
    'elderWandUrl',
    'http://localhost:52906/launchpad',
  ),
  experienceStudioUrl: getEnvWithFallback(
    'experienceStudioUrl',
    'http://localhost:4201/experience',
  ),
  productStudioUrl: getEnvWithFallback(
    'productStudioUrl',
    'http://localhost:4202/product',
  ),
  consoleRedirectUrl: getEnvWithFallback(
    'consoleRedirectUrl',
    'http://localhost:4203/console',
  ),
  consoleUrl: getEnvWithFallback('consoleUrl', 'http://localhost:4203/console'),
  consoleRedirectUri: getEnvWithFallback(
    'consoleRedirectUri',
    'http://localhost:4203/console',
  ),

  // API Configuration (constructed dynamically or directly from window.env)
  apiVersion: getEnvWithFallback('apiVersion', 'v1'),
  baseUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),
  consoleApi: getEnvWithFallback('consoleApi', 'http://localhost:3000/api'),
  consoleApiV2: getEnvWithFallback(
    'consoleApiV2',
    'http://localhost:3000/api/v2',
  ),
  consoleApiAuthUrl: getEnvWithFallback(
    'consoleApiAuthUrl',
    'http://localhost:3000/auth',
  ),
  consoleEmbeddingApi: getEnvWithFallback(
    'consoleEmbeddingApi',
    'http://localhost:3000/embedding',
  ),
  consoleInstructionApi: getEnvWithFallback(
    'consoleInstructionApi',
    'http://localhost:3000/instruction',
  ),
  consoleLangfuseUrl: getEnvWithFallback(
    'consoleLangfuseUrl',
    'http://localhost:3000/langfuse',
  ),
  consoleTruelensUrl: getEnvWithFallback(
    'consoleTruelensUrl',
    'http://localhost:3000/truelens',
  ),
  consolePipelineApi: getEnvWithFallback(
    'consolePipelineApi',
    'http://localhost:3000/pipeline',
  ),
  experienceApiUrl: getEnvWithFallback(
    'experienceApiUrl',
    'http://localhost:3000/experience',
  ),
  productApiUrl: getEnvWithFallback(
    'productApiUrl',
    'http://localhost:3000/product',
  ),

  // Legacy properties for backward compatibility
  apiBaseUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),
  apiUrl: getEnvWithFallback('baseUrl', 'http://localhost:3000'),
  elderWandApiAuthUrl: getEnvWithFallback(
    'consoleApiAuthUrl',
    'http://localhost:3000/auth',
  ),
  elderWandRedirectUrl: getEnvWithFallback(
    'elderWandUrl',
    'http://localhost:52906/launchpad',
  ),

  // Logging and App Specific
  enableLogStreaming: getEnvWithFallback('enableLogStreaming', 'all'),
  logStreamingApiUrl: getEnvWithFallback(
    'logStreamingApiUrl',
    'http://localhost:3000/logs',
  ),
  appVersion: getEnvWithFallback('appVersion', '1.0.0'),
  workflowExecutionMode: getEnvWithFallback('workflowExecutionMode', 'local'),
  useBasicLogin: getEnvWithFallback('useBasicLogin', 'false'),

  // Project context detection
  projectContext: detectProjectContext(),

  // Utility function for constructing API URLs
  getApiUrl: (endpoint: string) => {
    const baseUrl = getEnvWithFallback('baseUrl', 'http://localhost:3000');
    return `${baseUrl}${endpoint}`;
  },
};

// Log the environment configuration for debugging purposes
console.log('Shared Environment configuration loaded:', {
  projectContext: environment.projectContext,
  enableLogStreaming: environment.enableLogStreaming,
  baseUrl: environment.baseUrl,
});
