export enum ExecutionStatus {
  notStarted = 'notStarted',
  running = 'running',
  completed = 'completed',
}

export interface ActivityLog {
  content: string;
  timestamp: string;
  message: string;
  color: string;
  type?: 'info' | 'success' | 'warning' | 'error';
}

export interface ExecutionDetails {
  startTime?: Date;
  endTime?: Date;
  duration?: string;
  status?: ExecutionStatus;
}

export interface OutputItem {
  type: string;
  content: any;
  timestamp: string;
} 