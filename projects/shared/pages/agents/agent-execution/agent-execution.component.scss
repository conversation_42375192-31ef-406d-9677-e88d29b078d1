.agent-execution-container {
  height: calc(100vh - 84px);
  display: flex;
  flex-direction: column;
  color: var(--color-text-primary);
  overflow: hidden;
}

// Top Navigation Bar
.top-nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
}

.nav-left {
  display: flex;
  align-items: center;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0px 24px;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--color-background-tertiary);
  }

  .agent-name {
    font-size: 24px;
    font-weight: 700;
    color: var(--build-agents-tool-name);
  }
}

// Main Content Layout
.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px 16px 8px 16px;
  padding-top: 0px;
  height: calc(100vh - 150px);
  overflow: hidden;
}

// Left Panel
.left-panel {
  flex: 1;
  min-width: 400px;
  max-width: 600px;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.3s ease;

  &.collapsed {
    flex: 0 0 48px;
    min-width: 48px;
    max-width: 48px;
  }
}

// Right Panel
.right-panel {
  flex: 1;
  min-width: 400px;
  background: white;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

// Panel Headers
.panel-header {
  padding: 16px 20px;
  background: var(--execution-header-background);
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-height: 45px;
  border-radius: 8px 8px 0 0;
}

.collapse-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  color: var(--console-card-primary-title);

  &:hover {
    background: var(--color-background-quaternary);
  }
}

.history-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  color: var(--pagination-primary-text);
  transition: all 0.2s ease;

  &:disabled {
    cursor: not-allowed;
  }

  &:hover {
    color: var(--color-text-primary);
    background: var(--color-background-quaternary);
  }
}

// Right Panel Tabs
.tabs-container {
  display: flex;
  gap: 8px;
  padding: 4px;
}

.tab-btn {
  padding: 6px 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: transparent;
  white-space: nowrap;
  color: var(--console-card-primary-title);

  &.active {
    background: white;
    color: var(--build-agents-execute-connection);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-weight: 600;
  }
}

// Panel Content
.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  &.hidden {
    display: none;
  }
}

// Mock Content Areas
.mock-content {
  padding: 24px;
  height: 100%;
  overflow-y: auto;

  h3 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  p {
    margin: 0 0 24px 0;
    color: var(--color-text-secondary);
    font-size: 14px;
    line-height: 1.5;
  }
}

// Blueprint Content Styles
.blueprint-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
}

.blueprint-header {
  padding: 10px;

  h3 {
    color: black;
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
  }

  p {
    color: var(--color-text-secondary);
    margin: 0;
    font-size: 14px;
  }
}

.blueprint-canvas-container {
  flex: 1;
  overflow: auto;
  padding: 10px;
  background: var(--color-background-primary);
}

// Custom Blueprint Display Styles
.custom-blueprint-container {
  height: 100%;
  width: 100%;
  min-height: 500px;
  background-color: var(--plaground-execution-border);
  background-image: radial-gradient(circle, #d1d5db 1px, transparent 1px);
  background-size: 20px 20px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--build-agents-canvas-edge);
  position: relative;
  padding: 5%;
  border-radius: 8px;
}

// Central Progress Bar
.central-progress {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background-color: white;
  border-radius: 50%;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .progress-ring {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .progress-circle {
    transform: rotate(-90deg);
  }

  .progress-background {
    opacity: 0.15;
  }

  .progress-bar {
    transition: stroke-dashoffset 0.3s ease;
  }

  .progress-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--build-agents-field-label);
  }

  .progress-percentage {
    font-size: 18px;
    font-weight: 600;
    line-height: 1;
  }

  .progress-label {
    font-size: 12px;
    color: var(--build-agents-tab-label);
    margin-top: 2px;
  }
}

// Parent and Box Layout
#parent-box {
  height: 100%;
  width: 100%;
  gap: 2rem;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

// Container for the box layout
.blueprint-zones-container {
  position: relative;
  width: 80%;
  height: 80%;
  max-width: 800px;
  max-height: 600px;
}

#box1 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 300px;
  position: relative;
  border: 1px solid #bbbec5;
  position: fixed;
  top: 32%;
  left: 40%;
  border-left: none;
}

#box2 {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 300px;
  position: relative;
  border: 1px solid #bbbec5;
  position: fixed;
  top: 32%;
  left: 60%;
  border-right: none;
}

// Blueprint Zone Styles
.blueprint-zone {
  border-radius: 8px;
  padding: 16px;
  transition: all 0.3s ease;
  position: absolute;
  width: 280px;
  z-index: 5;

  &.has-nodes {
    border-style: solid;
  }

  &.collapsed {
    min-height: 40px;

    .zone-header {
      margin-bottom: 0;
    }
  }
}

// Corner Zone Positioning (Box Layout)
.north-zone {
  background-color: #ecf0fa;
  color: #005eb5;
  border: 2px solid #9ab7f6;
  top: 0;
  left: 0;
  overflow: hidden;
}

.east-zone {
  // Top-Right Corner
  background-color: #f2ebfd;
  border: 2px solid #d6c2f9;
  color: #d6c2f9;
  top: 0;
  right: 0;
  overflow: hidden;
}

.south-zone {
  // Bottom-Right Corner
  background-color: #fbf6f7;
  border: 2px solid #fecaca;
  color: #dc2626 !important;
  bottom: 0;
  right: 0;
  overflow: hidden;

  &.has-nodes {
    height: auto;
    min-height: 100px;
    max-height: 200px;
    &:hover {
      overflow-y: auto;
      scrollbar-width: none !important;
    }
  }
}

.west-zone {
  // Bottom-Left Corner
  background-color: #ecf8f4;
  border: 2px solid #a9e1cc;
  color: #25684f;
  bottom: 0;
  left: 0;
  overflow: hidden;

  &.has-nodes {
    height: auto;
    min-height: 100px;
    max-height: 200px;
    &:hover {
      overflow-y: auto;
      scrollbar-width: none !important;
    }
  }
}

// SVG Connection Lines Styling
.connection-lines {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;

  line {
    opacity: 0.7;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }
  }
}

// Ensure central progress is above the lines
.central-progress {
  z-index: 10;
}

// Ensure zones are above the lines
.blueprint-zone {
  z-index: 5;
}

// Zone Headers
.zone-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;

  .header-content {
    display: flex;
    align-items: center;
    gap: 8px;

    .header-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background-color: rgba(59, 130, 246, 0.1);
      color: #3b82f6;
    }

    .zone-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--build-agents-field-label);
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .accordion-toggle {
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      color: var(--build-agents-tab-label);

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
        color: var(--build-agents-field-label);
      }

      svg {
        transition: transform 0.3s ease;
      }
    }
  }
}

.required-badge {
  background-color: var(--status-error);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.optional-badge {
  background-color: var(--status-warning);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

// Zone Content
.zone-content {
  min-height: 60px;
  flex: 1;
  transition: all 0.3s ease-in-out;
  opacity: 1;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  color: var(--text-secondary);
  font-size: 12px;
  padding: 20px 0;
  font-style: italic;
}

// Kanban Cards
.nodes-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.kanban-card {
  background: white;
  border: 1px solid var(--build-agents-canvas-node-border);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;

  &:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .card-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--build-agents-field-label);
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// Legacy Blueprint Placeholder (keeping for backward compatibility)
.blueprint-placeholder {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.blueprint-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-background-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--color-border-secondary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

.blueprint-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.blueprint-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

// Output Content
.output-placeholder {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.output-section {
  background: var(--color-background-primary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  overflow: hidden;

  h4 {
    margin: 0;
    padding: 16px 16px 8px 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .output-meta {
    margin: 0;
    padding: 0 16px 16px 16px;
    font-size: 12px;
    color: var(--color-text-tertiary);
  }
}

.output-preview {
  border-top: 1px solid var(--color-border-primary);

  .code-block {
    padding: 16px;
    background: var(--color-background-secondary);
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 12px;
    line-height: 1.6;
    color: var(--color-text-primary);
    border-bottom: 1px solid var(--color-border-primary);
  }

  .preview-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-brand-primary);
    transition: all 0.2s ease;
    width: 100%;
    text-align: left;

    &:hover {
      background: var(--color-background-tertiary);
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .execution-content {
    flex-direction: column;
  }

  .playground-column,
  .output-column {
    flex: none;
    height: 50%;
  }

  .playground-column {
    border-right: none;
    border-bottom: 1px solid var(--color-border-primary);
  }
}

// Row and column utilities
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
}

.col-7 {
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.col-5 {
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

// Ensure proper spacing and alignment
* {
  box-sizing: border-box;
}

// Custom scrollbar styling
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border-primary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-secondary);
}

// Placeholder components styles
.activity-placeholder,
.output-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;

  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  p {
    margin: 0;
    color: var(--color-text-secondary);
    font-size: 14px;
  }
}

.activity-list,
.output-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  padding: 16px;
  background: var(--color-background-secondary);
}

.activity-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border-primary);

  &:last-child {
    border-bottom: none;
  }

  .timestamp {
    font-size: 12px;
    color: var(--color-text-tertiary);
    font-weight: 500;
  }

  .content {
    font-size: 14px;
    color: var(--color-text-primary);
  }
}

.output-item {
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border-primary);

  &:last-child {
    border-bottom: none;
  }

  .output-content {
    font-size: 14px;
    color: var(--color-text-primary);
    line-height: 1.5;
  }
}

// Agent Output Styles
.individual-output,
.collaborative-output {
  padding: 16px;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  p {
    color: var(--color-text-secondary);
    font-size: 14px;
    margin: 0;
  }
}

// New Execution History Styles
.output-content {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary);
  }
}

.execution-history-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.no-output-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  p {
    color: var(--color-text-secondary);
    font-size: 14px;
    text-align: center;
  }
}

.execution-history-list {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-background-secondary);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-border-primary);
    border-radius: 3px;

    &:hover {
      background: var(--color-border-secondary);
    }
  }
}

.execution-item {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.2s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &.success {
    border-left: 4px solid #10b981;
  }

  &.failed {
    border-left: 4px solid #ef4444;
  }

  &.pending {
    border-left: 4px solid #f59e0b;
  }
}

.execution-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-primary);
}

.agent-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--color-text-primary);
}

.execution-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.success {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }

  &.failed {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.pending {
    background: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
}

.execution-details {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border-primary);
}

.user-message {
  font-size: 14px;
  color: var(--color-text-primary);
  margin-bottom: 8px;
  line-height: 1.4;

  strong {
    color: var(--color-text-primary);
  }
}

.execution-timestamp {
  font-size: 12px;
  color: var(--color-text-tertiary);
}

.execution-response {
  padding: 16px;
}

.response-section {
  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
  }
}

.response-text {
  font-size: 14px;
  line-height: 1.6;
  color: var(--color-text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
  background: var(--color-background-primary);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--color-border-secondary);
  max-height: 300px;
  overflow-y: auto;
}

.task-output {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.task-header {
  h4 {
    margin: 12px 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
  }
}

.task-description,
.task-expected {
  margin-bottom: 12px;
  font-size: 14px;
  color: var(--color-text-secondary);

  strong {
    color: var(--color-text-primary);
  }
}

.task-content {
  font-size: 14px;
  line-height: 1.6;
  color: var(--color-text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
  background: var(--color-background-primary);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--color-border-secondary);
  max-height: 300px;
  overflow-y: auto;
}

.execution-error {
  padding: 16px;
}

.error-message {
  font-size: 14px;
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid rgba(239, 68, 68, 0.2);

  strong {
    color: #ef4444;
  }
}

.response-header {
  margin-bottom: 8px;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
    
    .response-title {
      margin: 0;
      font-size: 14px;
      font-weight: 600;
      color: var(--color-text-primary);
    }
    
    ava-button {
      flex-shrink: 0;
    }
  }
}

.output-box {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-primary);
  border-radius: 8px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.output-section {
  padding: 16px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .task-description,
  .task-expected {
    margin-bottom: 12px;
    font-size: 14px;
    color: var(--color-text-secondary);

    strong {
      color: var(--color-text-primary);
    }
  }
}

.output-text {
  font-size: 14px;
  line-height: 1.6;
  color: var(--color-text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
  background: var(--color-background-primary);
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--color-border-secondary);
  max-height: 400px;
  overflow-y: auto;
}

// Zone-specific styles
.tools-zone {
  border-color: #ff8c00;
  // background: rgba(255, 140, 0, 0.05);

  .zone-header {
    border-bottom-color: rgba(255, 140, 0, 0.2);
  }
}

.guardrails-zone {
  border-color: #dc2626;

  .zone-header {
    border-bottom-color: rgba(220, 38, 38, 0.2);
  }

  // &.has-nodes .zone-header {
  //   background: rgba(220, 38, 38, 0.1);
  // }
}

.output-content {
  position: relative;
}

.task-summary{
  padding-top: 1rem;
}

.loader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

::ng-deep .panel-header svg {
  stroke: var(--pagination-primary-text);
}
