// Build Agent Node Styles
.build-agent-node {
  position: relative;
  cursor: pointer;
  user-select: none;
  transition: opacity 0.1s ease; // Only transition opacity for smooth visual feedback
  display: inline-block;
  margin: 0;
  padding: 0;
  box-sizing: border-box;

  // Default build mode dimensions
  width: auto;
  height: 48px;

  &.selected {
    z-index: 10;
  }

  &.dragging {
    z-index: 20;
    opacity: 0.9; // Better visibility during drag
    transition: none; // No transitions during drag
  }

  &.disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  // Execute mode styling
  &.execute-mode {
    width: 55px;
    height: 55px;
    background: white;
    border-radius: 40px;
    border: 7px solid white; // White border around the icon
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    position: absolute !important; // Force absolute positioning
    transform: none !important; // Override any CDK Drag transforms
    left: var(--node-x, 0px) !important; // Use CSS variables for positioning
    top: var(--node-y, 0px) !important;
  }

  // Force absolute positioning (for edit/view modes)
  &.force-absolute {
    position: absolute !important; // Force absolute positioning
    transform: none !important; // Override any CDK Drag transforms
    left: var(--node-x, 0px) !important; // Use CSS variables for positioning
    top: var(--node-y, 0px) !important;
  }

  // Build Mode Styling - White pill with gradient icon
  .node-content-build {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: auto; // Changed to auto to accommodate padding
    padding: 4px;
    padding-right: 16px;
    position: relative;
    transition: none; // Remove transition for smooth movement
    min-width: fit-content;

    // Single connection point behind the icon
    &::before {
      content: "";
      position: absolute;
      left: 34px; // Adjusted for 10px padding + 24px icon center
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      background: #9ca3af;
      border-radius: 50%;
      z-index: 0;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    // Gradient icon section
    .node-icon-section {
      width: 40px; // Reduced from 48px to account for padding
      height: 40px; // Reduced from 48px to account for padding
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      z-index: 2;
      position: relative;

      img {
        width: 24px;
        height: 24px;
        object-fit: contain;
        filter: brightness(0) invert(1); // Make asset images white
      }

      // Lucide icons are already white via the color attribute
    }

    // Label section
    .node-label-section {
      padding: 0 0 0 12px; // Removed right padding since we have 10px padding on container
      display: flex;
      align-items: center;
      height: 100%;

      .node-label {
        font-size: 14px;
        font-weight: 500;
        color: #1f2937;
        white-space: nowrap;
        line-height: 1;
        max-width: 140px; // Approximately 20 characters at 14px font size
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transition: box-shadow 0.1s ease; // Only transition hover effects

      &::before {
        opacity: 1;
        transition: opacity 0.1s ease; // Only transition connection point
      }
    }
  }

  // Execute Mode Styling (Circular Icon Only - same as build mode icon section)
  .node-content-execute {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    border: 7px solid white; // White border around the icon
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: none; // Remove transition to prevent flickering

    img {
      width: 20px;
      height: 20px;
      object-fit: contain;
      filter: brightness(0) invert(1);
    }

    lucide-icon {
      color: white;
    }
  }

  // Node type specific gradient colors for build mode icons
  &[data-node-type="prompt"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #8be1eb 0%, #028697 100%);
  }

  &[data-node-type="model"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #fec47e 0%, #db770c 100%);
  }

  &[data-node-type="knowledge"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #f598b7 0%, #d41b5a 100%);
  }

  &[data-node-type="tool"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #81d39f 0%, #179ead 100%);
  }

  &[data-node-type="guardrail"] .node-content-build .node-icon-section {
    background: linear-gradient(180deg, #b9a4de 0%, #5e35a7 100%);
  }

  // Execute mode colors - matching the reference image
  &[data-node-type="prompt"] .node-content-execute {
    background: linear-gradient(180deg, #8be1eb 0%, #028697 100%);
  }

  &[data-node-type="model"] .node-content-execute {
    background: linear-gradient(180deg, #fec47e 0%, #db770c 100%);
  }

  &[data-node-type="knowledge"] .node-content-execute {
    background: linear-gradient(180deg, #f598b7 0%, #d41b5a 100%);
  }

  &[data-node-type="tool"] .node-content-execute {
    background: linear-gradient(180deg, #81d39f 0%, #179ead 100%);
  }

  &[data-node-type="guardrail"] .node-content-execute {
    background: linear-gradient(180deg, #b9a4de 0%, #5e35a7 100%);
  }

  // Selected state styling
  &.selected .node-content-build {
    box-shadow: 0 0 0 2px #3b82f6;
  }

  &.selected .node-content-execute {
    box-shadow: 0 0 0 2px #3b82f6;
  }

  // Delete button styling
  .delete-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 20px;
    height: 20px;
    background: #ef4444;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.2s ease;

    &:hover {
      background: #dc2626;
      transform: scale(1.1);
    }

    svg {
      width: 10px;
      height: 10px;
    }
  }

  &:hover .delete-btn,
  &.selected .delete-btn {
    display: flex;
  }

  // Hide all connection points - we only use the single point behind icon
  .connection-point {
    display: none;
  }

  // Execute mode tooltip styling - Enhanced for professional look
  .execute-tooltip {
    position: absolute;
    right: 100%; // Position to the left of the node
    top: 50%; // Center vertically
    transform: translateY(-50%); // Center vertically

    // Professional semi-transparent background
    background: rgba(
      0,
      0,
      0,
      0.85
    ); // Slightly more transparent for professional look
    backdrop-filter: blur(
      8px
    ); // Add blur effect for modern glass-like appearance
    -webkit-backdrop-filter: blur(8px); // Safari support

    color: white;
    padding: 10px 14px; // Slightly more padding for better spacing
    border-radius: 8px; // Slightly more rounded corners
    font-size: 12px;
    font-weight: 500; // Medium weight for better readability
    z-index: 9999; // Higher z-index to appear above dropdowns
    margin-right: 10px; // Slightly more space between tooltip and node
    text-align: left;

    // Enhanced shadow for depth and professional appearance
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.2),
      0 2px 8px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1); // Subtle inner highlight

    // Add subtle border for definition
    border: 1px solid rgba(255, 255, 255, 0.1);

    // Dynamic sizing based on content
    min-width: 150px; // Increased width
    max-width: 400px; // Increased max width
    width: auto; // Use content width up to max-width

    // Height and overflow management - FIXED FOR SCROLLING
    height: auto;
    max-height: 300px !important; // Increased max height and use !important to override any global styles

    // Text and overflow handling - ENHANCED FOR PROPER SCROLLING
    white-space: pre-line !important; // Allow line breaks and preserve formatting
    overflow-y: auto !important; // Enable vertical scrolling when content exceeds max-height
    overflow-x: hidden !important; // Hide horizontal scrolling
    word-wrap: break-word !important; // Break long words to prevent horizontal overflow

    // IMPORTANT: Remove any text truncation that might be applied globally
    text-overflow: clip !important; // Ensure no ellipsis truncation
    -webkit-line-clamp: unset !important; // Remove any line clamping
    line-clamp: unset !important; // Remove any line clamping (standard property)
    -webkit-box-orient: unset !important; // Remove webkit box orientation
    display: block !important; // Ensure proper display mode for scrolling

    // Ensure content is fully visible
    line-height: 1.4; // Better line spacing for readability

    // Force scrollbar to always be visible when content overflows
    scrollbar-width: thin; // For Firefox
    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1); // For Firefox

    // Enhanced scrollbar styling for better visibility
    &::-webkit-scrollbar {
      width: 6px !important; // Slightly wider for better visibility
      height: 6px !important;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1) !important;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.4) !important; // More visible
      border-radius: 3px;

      &:hover {
        background: rgba(
          255,
          255,
          255,
          0.6
        ) !important; // Even more visible on hover
      }
    }

    // Ensure scrollbar corner is styled
    &::-webkit-scrollbar-corner {
      background: rgba(255, 255, 255, 0.1);
    }

    // Enhanced arrow pointing right (towards the node) with professional styling
    &::after {
      content: "";
      position: absolute;
      left: 100%; // Position at the right edge of tooltip
      top: 50%; // Center vertically
      transform: translateY(-50%);

      // Enhanced arrow styling for professional look
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 8px 0 8px 10px; // Larger, more prominent arrow
      border-color: transparent transparent transparent rgba(0, 0, 0, 0.85); // Semi-transparent black

      // Add subtle shadow for depth
      filter: drop-shadow(1px 0 2px rgba(0, 0, 0, 0.2));

      // Ensure arrow is properly attached to tooltip
      margin-left: -1px; // Slight overlap to ensure seamless connection
    }

    // Optional: Add a subtle border to the arrow for better definition
    &::before {
      content: "";
      position: absolute;
      left: 100%;
      top: 50%;
      transform: translateY(-50%);

      width: 0;
      height: 0;
      border-style: solid;
      border-width: 9px 0 9px 11px; // Slightly larger for border effect
      border-color: transparent transparent transparent rgba(0, 0, 0, 0.3); // Darker border

      z-index: -1; // Behind the main arrow
      margin-left: -2px; // Position behind main arrow
    }
  }
}
