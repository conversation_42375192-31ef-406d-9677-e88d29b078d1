import {
  Component,
  Input,
  Output,
  EventEmitter,
  ElementRef,
  ViewChild,
  AfterViewInit,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { DragDropModule, CdkDragEnd, CdkDrag } from '@angular/cdk/drag-drop';
import { IconComponent } from '@ava/play-comp-library';

export interface BuildAgentNodeData {
  id: string;
  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';
  name: string;
  icon?: string;
  position: { x: number; y: number };
  originalToolData?: any; // Store the original tool data for configuration purposes
}

export interface ExecuteNodeData {
  type: 'prompt' | 'model' | 'knowledge' | 'tool' | 'guardrail';
  nodes: BuildAgentNodeData[]; // All nodes of this type
  position: { x: number; y: number };
}

@Component({
  selector: 'app-build-agent-node',
  standalone: true,
  imports: [CommonModule, DragDropModule, IconComponent],
  templateUrl: './build-agent-node.component.html',
  styleUrls: ['./build-agent-node.component.scss'],
})
export class BuildAgentNodeComponent implements AfterViewInit, OnChanges {
  @ViewChild('nodeElement') nodeElement!: ElementRef;
  @ViewChild('dragRef') cdkDrag!: CdkDrag;

  @Input() node: any;
  @Input() selected: boolean = false;
  @Input() mouseInteractionsEnabled: boolean = true;
  @Input() canvasMode: string = 'build'; // 'build' or 'execute'
  @Input() executeNodeData?: ExecuteNodeData; // For execute mode consolidated nodes
  @Input() forceAbsolutePositioning: boolean = false; // Force absolute positioning (for edit/view modes)

  private hasBeenDragged: boolean = false; // Track if node has been dragged by user
  private dragStartPosition: { x: number; y: number } = { x: 0, y: 0 }; // Store drag start position

  @Output() deleteNode = new EventEmitter<string>();
  @Output() moveNode = new EventEmitter<{
    nodeId: string;
    position: { x: number; y: number };
  }>();
  @Output() nodeSelected = new EventEmitter<string>();
  @Output() nodeDoubleClicked = new EventEmitter<string>();
  // Removed nodePositionChanged to avoid conflicts with moveNode
  @Output() startConnection = new EventEmitter<{
    nodeId: string;
    handleType: 'source' | 'target';
    event: MouseEvent;
  }>();

  isDragging: boolean = false;
  showTooltip: boolean = false;

  private readonly nodeConfig = {
    prompt: { icon: 'FileText', useAsset: false },
    model: { icon: 'Box', useAsset: false },
    knowledge: { icon: 'BookOpen', useAsset: false },
    tool: { icon: 'Wrench', useAsset: false },
    guardrail: { icon: 'Swords', useAsset: false },
  };

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['node']?.currentValue) {
      const currentNode = changes['node'].currentValue;
      const previousNode = changes['node'].previousValue;

      if (this.hasPositionChanged(currentNode, previousNode)) {
        // Only update position if we're not currently dragging
        if (!this.isDragging) {
          // Use CSS variables for positioning in execute mode, otherwise use CDK Drag
          if (this.isExecuteMode) {
            this.setCSSPosition(currentNode.data.position);
          } else {
            // Always update CDK drag position for non-execute modes
            this.updateCdkDragPosition(currentNode.data.position);
          }
        }
      }
    }
  }

  private hasPositionChanged(currentNode: any, previousNode: any): boolean {
    if (!currentNode?.data?.position) return false;
    if (!previousNode?.data?.position) return true;

    const current = currentNode.data.position;
    const previous = previousNode.data.position;
    return current.x !== previous.x || current.y !== previous.y;
  }

  ngAfterViewInit(): void {
    const nodeData = this.currentNodeData;
    if (nodeData) {
      if (this.isExecuteMode) {
        // Set CSS position immediately for execute mode
        this.setCSSPosition(nodeData.position);
      } else {
        // Set CDK drag position for other modes
        setTimeout(() => {
          this.updateCdkDragPosition(nodeData.position);
        }, 0);
      }
    }
  }

  private updateCdkDragPosition(position: { x: number; y: number }): void {
    if (this.isDragging) return;

    console.log('🔧 updateCdkDragPosition called:', {
      nodeId: this.currentNodeData.id,
      position,
      hasCdkDrag: !!this.cdkDrag,
    });

    // Set absolute position only - let CDK handle its own drag state
    try {
      if (this.nodeElement?.nativeElement) {
        const element = this.nodeElement.nativeElement;

        // Set absolute positioning
        element.style.position = 'absolute';
        element.style.left = `${position.x}px`;
        element.style.top = `${position.y}px`;
        element.style.transform = 'none';

        console.log('🔧 Absolute positioning set:', {
          nodeId: this.currentNodeData.id,
          position: position,
          left: element.style.left,
          top: element.style.top,
        });
      }
    } catch (error) {
      console.warn('Error updating position:', error);
    }
  }

  private setCSSPosition(position: { x: number; y: number }): void {
    console.log('🔧 setCSSPosition called:', {
      nodeId: this.currentNodeData.id,
      nodeName: this.currentNodeData.name,
      position,
      hasElement: !!this.nodeElement?.nativeElement,
      canvasMode: this.canvasMode,
      shouldUseAbsolute: this.shouldUseAbsolutePositioning,
    });

    if (this.nodeElement?.nativeElement) {
      const element = this.nodeElement.nativeElement;
      element.style.setProperty('--node-x', `${position.x}px`);
      element.style.setProperty('--node-y', `${position.y}px`);

      console.log('🔧 CSS variables set:', {
        nodeX: element.style.getPropertyValue('--node-x'),
        nodeY: element.style.getPropertyValue('--node-y'),
        computedStyle: window.getComputedStyle(element).position,
      });
    } else {
      console.warn('🔧 setCSSPosition failed - no element available');
    }
  }

  get currentNodeData(): BuildAgentNodeData {
    return this.node?.data || {};
  }

  get isExecuteMode(): boolean {
    return this.canvasMode === 'execute';
  }

  get shouldUseAbsolutePositioning(): boolean {
    // Only use absolute positioning for execute mode or when CDK drag is disabled
    const result = this.isExecuteMode || !this.mouseInteractionsEnabled;
    console.log('🔧 shouldUseAbsolutePositioning check:', {
      nodeId: this.currentNodeData.id,
      isExecuteMode: this.isExecuteMode,
      mouseInteractionsEnabled: this.mouseInteractionsEnabled,
      forceAbsolutePositioning: this.forceAbsolutePositioning,
      hasBeenDragged: this.hasBeenDragged,
      canvasMode: this.canvasMode,
      result,
    });
    return result;
  }

  get executeTooltipText(): string {
    if (!this.isExecuteMode || !this.executeNodeData) return '';

    const nodeNames = this.executeNodeData.nodes.map((node) => node.name);
    if (nodeNames.length === 1) {
      return nodeNames[0];
    } else if (nodeNames.length > 1) {
      const typeLabel =
        this.executeNodeData.type === 'knowledge'
          ? 'knowledge bases'
          : `${this.executeNodeData.type}s`;
      return `${nodeNames.length} ${typeLabel}:\n${nodeNames.join('\n')}`;
    }
    return '';
  }

  get isTooltipMultiLine(): boolean {
    return (
      this.executeTooltipText.includes('\n') ||
      this.executeTooltipText.length > 50
    );
  }

  get nodePosition(): { x: number; y: number } {
    if (this.isExecuteMode && this.executeNodeData) {
      return this.executeNodeData.position || { x: 0, y: 0 };
    }
    return this.currentNodeData.position || { x: 0, y: 0 };
  }

  get isCurrentlySelected(): boolean {
    return this.selected;
  }

  get currentConfig() {
    if (this.isExecuteMode && this.executeNodeData) {
      return (
        this.nodeConfig[this.executeNodeData.type] || this.nodeConfig.prompt
      );
    }
    const nodeData = this.currentNodeData;
    return this.nodeConfig[nodeData.type] || this.nodeConfig.prompt;
  }

  get shouldUseAssetIcon(): boolean {
    const config = this.currentConfig;
    return config.useAsset || false;
  }

  get iconName(): string {
    const config = this.currentConfig;

    if (this.isExecuteMode && this.executeNodeData) {
      // In execute mode, always use Lucide icons
      return config.icon;
    }

    const nodeData = this.currentNodeData;

    // For asset icons (prompts), use the node's icon or config icon
    if (config.useAsset) {
      return nodeData.icon || config.icon;
    }

    // For Lucide icons, always use the config icon (which contains the correct Lucide icon name)
    // Ignore any asset paths that might be in nodeData.icon for non-prompt types
    return config.icon;
  }

  onImageError(event: any): void {
    // Fallback to the default icon if the image fails to load
    const fallbackIcon = 'assets/images/build.png';
    event.target.src = fallbackIcon;
    console.warn(
      `Failed to load icon for ${this.currentNodeData?.type} node, using fallback:`,
      fallbackIcon,
    );
  }

  onNodeClick(): void {
    if (this.isExecuteMode) {
      // In execute mode, don't emit selection events
      return;
    }
    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {
      this.nodeSelected.emit(this.currentNodeData.id);
    }
  }

  onNodeDoubleClick(): void {
    if (this.isExecuteMode) {
      // In execute mode, don't emit double-click events
      return;
    }
    if (this.mouseInteractionsEnabled && this.currentNodeData.id) {
      this.nodeDoubleClicked.emit(this.currentNodeData.id);
    }
  }

  onMouseEnter(): void {
    if (this.isExecuteMode && this.executeTooltipText) {
      this.showTooltip = true;
    }
  }

  onMouseLeave(): void {
    if (this.isExecuteMode) {
      this.showTooltip = false;
    }
  }

  onDeleteClick(event: Event): void {
    event.stopPropagation();
    if (this.currentNodeData.id) {
      this.deleteNode.emit(this.currentNodeData.id);
    }
  }

  onDragStarted(event: any): void {
    this.isDragging = true;
    this.hasBeenDragged = true; // Mark that user has dragged this node

    // Store the starting position for manual drag calculation
    const element = this.nodeElement?.nativeElement;
    if (element) {
      this.dragStartPosition = {
        x: parseInt(element.style.left) || 0,
        y: parseInt(element.style.top) || 0,
      };
    }

    console.log('🔧 Drag started:', {
      nodeId: this.currentNodeData.id,
      hasBeenDragged: this.hasBeenDragged,
      dragStartPosition: this.dragStartPosition,
    });
  }

  onDragEnded(event: CdkDragEnd): void {
    if (!this.mouseInteractionsEnabled) return;

    this.isDragging = false;
    const nodeData = this.currentNodeData;
    if (!nodeData?.id) return;

    // Calculate new position from drag start + CDK transform
    const element = this.nodeElement?.nativeElement;
    if (element) {
      const transform = event.source.getFreeDragPosition();

      const absolutePosition = {
        x: Math.max(0, Math.round(this.dragStartPosition.x + transform.x)),
        y: Math.max(0, Math.round(this.dragStartPosition.y + transform.y)),
      };

      console.log('🔧 Drag ended - calculated position:', {
        nodeId: nodeData.id,
        dragStartPosition: this.dragStartPosition,
        transform: transform,
        newAbsolutePosition: absolutePosition,
      });

      // Emit the new position
      this.moveNode.emit({ nodeId: nodeData.id, position: absolutePosition });
    }
  }

  onNodeMouseDown(event: MouseEvent): void {
    if (this.mouseInteractionsEnabled) {
      // Check for connection starting gesture (Ctrl+click or right-click)
      if (event.ctrlKey || event.button === 2) {
        // Start connection from this node
        event.preventDefault();
        event.stopPropagation();

        // Make sure position is up to date before starting connection (like workflow editor)
        // Position changes handled by moveNode instead

        this.startConnection.emit({
          nodeId: this.currentNodeData.id,
          handleType: 'source',
          event: event,
        });
        return;
      }

      this.isDragging = true;
      this.onNodeClick();
    }
  }
}
