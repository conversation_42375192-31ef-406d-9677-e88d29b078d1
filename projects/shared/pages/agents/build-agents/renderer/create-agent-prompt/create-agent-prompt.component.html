
<div class="create-agent-prompt-container" *ngIf="isIndividualAgent">
 <form [formGroup]="promptForm">
    <div
      class="form-layout"
      [ngClass]="{ 'three-column-layout': isExecuteMode && showChatInterface }"
    >        <!-- Toggle Tabs -->
        <div class="tab-container">
          <h3 class="tab-heading">{{ promptLabels.chooseTheTypeOfPrompt }}</h3>
          <div class="tabs-wrapper">
            <div class="pill-tabs-container">
              <shared-toggle-button
                [options]="promptTabs"
                [selectedValue]="selectedTab"
                [disabled]="isEditMode || isViewMode"
                [selectedBorderColor]="'var(--build-agents-toggle-active-bg) '"
                [selectedTextColor]="'#FFFFFF'"
                [unselectedTextColor]="'#3B3F46'"
                [containerBackgroundColor]="'#FFFFFF'"
                [containerBorderColor]="'#F0F1F2'"
                (selectionChange)="onTabSelected($event)"
              >
              </shared-toggle-button>
            </div>
          </div>
        </div>

        <!-- Left Column -->
      <div class="left-column" [class.collapsed]="isLeftCollapsed">
        <div class="card-content" *ngIf="!isLeftCollapsed">
          <ava-textbox
            [formControl]="getControl('name')"
            label="{{ promptLabels.promptName }}"
            id="promptName"
            name="promptName"
            placeholder="{{ promptLabels.pleaseEnterPromptName }} "
            [error]="getFieldError('name')"
            [required]="true"
          >
          </ava-textbox>

          <ava-textarea
            id="description"
            name="description"
            label="{{ promptLabels.description }}"
            [formControl]="getControl('promptDescription')"
            placeholder="{{ promptLabels.placeholderDescription }}"
            [rows]="2"
            size="sm"
            [error]="getFieldError('promptDescription')"
            [required]="true"
          >
          </ava-textarea>
        </div>
      </div>

      <!-- Middle Column -->
      <div class="middle-column">
        <div class="form-container">
          <!-- Tab Content -->
          <div class="tab-content">
            <!-- Freeform Tab -->
            <ng-container *ngIf="selectedTab === 'freeform'">
              <div class="freeform-content-wrapper">
                <label class="field-label">
                  {{ promptLabels.freeformPrompt }} <span class="required-asterisk">*</span>
                </label>
                <ava-textarea
                  id="freeformPrompt"
                  name="freeformPrompt"
                  [formControl]="getControl('promptTask')"
                  placeholder="Please enter the prompt task"
                  [rows]="6"
                  size="sm"
                  [error]="getFieldError('promptTask')"
                  [fullWidth]="true"
                  [required]="true"
                >
                </ava-textarea>
                <div class="ask-aava-wrapper">
                  <ava-button
                    *ngIf="!isViewMode"
                    label="{{ promptLabels.askAva }}"
                    variant="secondary"
                    size="small"
                    iconName="WandSparkles"
                    (userClick)="toggleAskAvaModal()"
                  >
                  </ava-button>
                </div>

              
              </div>
            </ng-container>

            <!-- Template Tab -->
            <ng-container *ngIf="selectedTab === 'template'">
              <div class="template-form">
                <!-- Role Field - Half Width -->
                <div class="fields-row two-column">
                  <div class="field-col">
                    <ava-textbox
                      formControlName="role"
                      [formControl]="getControl('role')"
                      id="role"
                      name="role"
                      placeholder="Select Role"
                      label="{{ promptLabels.role }}"
                      [disabled]="isViewMode"
                      [error]="getFieldError('role')"
                      [required]="true"
                      class="prompt-type-dropdown-flex role-dropdown-style"
                    >
                    </ava-textbox>
                  </div>
                     <div class="field-col">
                    <ava-dropdown
                      formControlName="promptType"
                      label="Prompt Type"
                      id="promptType"
                      name="promptType"
                      [options]="promptTypeOptions"
                      [selectedValue]="selectedDropdownValue"
                      placeholder="Select Prompt Type"
                      [required]="true"
                      [error]="getFieldError('promptType')"
                      (selectionChange)="onPromptTypeChange($event)"
                      [disabled]="isViewMode"
                      class="prompt-type-dropdown-flex role-dropdown-style"
                    >
                    </ava-dropdown>
                  </div>
                </div>

                <!-- First Row: Goal and Description -->
                <div class="fields-row two-column">
                  <div class="field-col">
                    <ava-textarea
                      [formControl]="getControl('goal')"
                      label="Goal"
                      id="goal"
                      name="{{ promptLabels.goal }}"
                      placeholder="{{ promptLabels.placeholderGoal }}"
                      size="sm"
                      [rows]="4"
                      [error]="getFieldError('goal')"
                      [fullWidth]="true"
                      [required]="true"
                    >
                    </ava-textarea>
                  </div>
                  <div class="field-col">
                    <ava-textarea
                      id="templateDescription"
                      name="templateDescription"
                      label="{{ promptLabels.description }}"
                      [formControl]="getControl('description')"
                      placeholder="{{ promptLabels.placeholderDescription }}"
                      [rows]="4"
                      size="sm"
                      [fullWidth]="true"
                      [error]="getFieldError('description')"
                      [required]="true"
                    >
                    </ava-textarea>
                  </div>
                </div>

                <!-- Second Row: Backstory and Expected Output -->
                <div class="fields-row two-column">
                  <div class="field-col">
                    <ava-textarea
                      id="backstory"
                      name="backstory"
                      label="{{ promptLabels.backstory }}"
                      [formControl]="getControl('backstory')"
                      placeholder="{{ promptLabels.placeholderBackStory }}"
                      [rows]="4"
                      size="sm"
                      [fullWidth]="true"
                      [error]="getFieldError('backstory')"
                      [required]="true"
                    >
                    </ava-textarea>
                  </div>
                  <div class="field-col">
                    <ava-textarea
                      [formControl]="getControl('expectedOutput')"
                      label="{{ promptLabels.expectedOutput }}"
                      id="expectedOutput"
                      name="expectedOutput"
                      placeholder="{{ promptLabels.placeholderExpectedOutput }}"
                      size="sm"
                      [rows]="4"
                      [fullWidth]="true"
                      [error]="getFieldError('expectedOutput')"
                      [required]="true"
                    >
                    </ava-textarea>
                  </div>
                </div>

                <div
                  class="fields-row-2"
                  *ngIf="
                    getControl('promptType').value &&
                    getControl('promptType').value.toLowerCase() !==
                      'zero shot'
                  "
                >
                  <div class="field-col" *ngIf="getControl('promptType').value && getControl('promptType').value.toLowerCase() === 'chain of thought'">
                    <ava-textarea
                      id="intermediateSteps"
                      name="intermediateSteps"
                      label="{{ promptLabels.intermediateSteps }}"
                      [formControl]="getControl('intermediateSteps')"
                      placeholder="{{
                        promptLabels.placeholderIntermediateSteps
                      }}"
                      [rows]="4"
                      size="sm"
                      [fullWidth]="true"
                      [error]="getFieldError('intermediateSteps')"
                      [required]="true"
                    >
                    </ava-textarea>
                  </div>
                      <div class="optional-sections">
                  <div
                    class="examples-accordion-wrapper"
                    *ngIf="
                      !getControl('promptType').value ||
                      getControl('promptType').value.toLowerCase() !==
                        'zero shot'
                    "
                  >
                    <ava-accordion
                      id="examples-accordion"
                      #accordion
                      [expanded]="false"
                      [animation]="true"
                      [controlled]="false"
                      iconClosed="chevron-down"
                      iconOpen="chevron-up"
                      iconPosition="right"
                      type="default"
                    >
                      <div header>
                        {{ promptLabels.addExamples }}
                      </div>
                      <div content>
                        <div #examplesEnd></div>

                        <div class="example">
                          <div class="example-content" formArrayName="examples">
                            <div
                              class="example-group"
                              *ngFor="
                                let example of examples.controls;
                                let i = index
                              "
                              [formGroupName]="i"
                            >
                              <div class="field-col">
                                <ava-textarea
                                  [formControlName]="'input'"
                                  label="Input"
                                  name="{{ promptLabels.input }}"
                                  id="input"
                                  placeholder="{{
                                    promptLabels.placeholderInput
                                  }}"
                                  variant="primary"
                                  [rows]="2"
                                  size="sm"
                                  [fullWidth]="true"
                                >
                                </ava-textarea>
                              </div>
                              <div class="field-col">
                                <ava-textarea
                                  [formControlName]="'output'"
                                  label="Output"
                                  name="{{ promptLabels.output }}"
                                  id="output"
                                  placeholder="{{
                                    promptLabels.placeholderOutput
                                  }}"
                                  variant="primary"
                                  [rows]="2"
                                  size="sm"
                                  [fullWidth]="true"
                                >
                                </ava-textarea>
                              </div>
                              <!-- Remove button down and right-aligned -->
                              <div class="example-remove" *ngIf="i > 0">
                                <ava-button
                                  label="{{ promptLabels.remove }}"
                                  variant="danger"
                                  size="small"
                                  iconName="trash"
                                  iconPosition="left"
                                  (userClick)="removeExample(i)"
                                  [outlined]="true"
                                >
                                </ava-button>
                              </div>
                            </div>
                            <!-- Add button aligned to the left below the example group -->
                            <div class="example-actions">
                              <ava-button
                                label="{{ promptLabels.addExamples }}"
                                variant="secondary"
                                size="small"
                                iconName="plus"
                                iconPosition="right"
                                (userClick)="addExample()"
                              >
                              </ava-button>
                            </div>
                          </div>
                          <div class="add-consideration">
                            <ava-textarea
                              [formControl]="getControl('addConsideration')"
                              label="{{ promptLabels.additionalConsideration }}"
                              id="addConsideration"
                              name="addConsideration"
                              placeholder="{{
                                promptLabels.placeholderAdditionalConsideration
                              }}"
                              variant="primary"
                              [rows]="2"
                              size="sm"
                              [fullWidth]="true"
                            >
                            </ava-textarea>
                          </div>
                        </div>
                      </div>
                    </ava-accordion>
                  </div>
                </div>
                </div>

                  <div class="ask-aava-wrapper">
          <ava-button
            label="Ask AAVA"
            variant="secondary"
            size="small"
            iconName="WandSparkles"
            (userClick)="toggleAskAvaModal()"
            iconPosition="right"
          >
          </ava-button>
        </div>

              </div>

            </ng-container>
          </div>
        </div>
      </div>


      <!-- Right Column (if present) -->
    </div>
                     <div class="action-buttons">
        <ava-button
          label="Cancel"
          variant="secondary"
          size="medium"
          (userClick)="onCancel()"
        >
        </ava-button>
        <ava-button
          label="Save"
          variant="primary"
          size="medium"
          (userClick)="onSave()"
          [disabled]="isSaveDisabled()"
        >
        </ava-button>
      </div>
  </form>
</div>

<!-- Collaborative Agent Layout -->
<div class="create-agent-prompt-collaborative-container" *ngIf="isCollaborativeAgent">
  <form [formGroup]="promptForm">


    <div class="collaborative-content">
      <!-- Form Fields -->
      <div class="collaborative-form-fields">
        <!-- Prompt Name -->
        <div class="field-group full-width">
          <label class="field-label">Prompt Name <span class="required-asterisk">*</span></label>
          <ava-textbox
            [formControl]="getControl('name')"
            id="promptName"
            name="promptName"
            placeholder="Enter text here"
            [error]="getFieldError('name')"
            [required]="true"
          >
          </ava-textbox>
        </div>

        <!-- Prompt Description -->
        <div class="field-group full-width">
          <label class="field-label">Prompt Description <span class="required-asterisk">*</span></label>
          <ava-textarea
            id="description"
            name="description"
            [formControl]="getControl('promptDescription')"
            placeholder="Enter text here"
            [rows]="3"
            size="sm"
            [error]="getFieldError('promptDescription')"
            [required]="true"
            [fullWidth]="true"
          >
          </ava-textarea>
        </div>

        <!-- Role -->
            <div class="two-column-row">
        <div class="field-group">
          <label class="field-label">
            Role <span class="required-asterisk">*</span>
          </label>
          <ava-textbox
            [formControl]="getControl('role')"
            id="role"
            name="role"
            placeholder="Enter text here"
            [error]="getFieldError('role')"
            [required]="true"
          >
          </ava-textbox>
        </div>
           <div class="field-col">
                    <ava-dropdown
                      formControlName="promptType"
                      label="Prompt Type"
                      id="promptType"
                      name="promptType"
                      [options]="promptTypeOptions"
                      [selectedValue]="selectedDropdownValue"
                      placeholder="Select Prompt Type"
                      [required]="true"
                      [error]="getFieldError('promptType')"
                      (selectionChange)="onPromptTypeChange($event)"
                      [disabled]="isViewMode"
                      class="prompt-type-dropdown-flex role-dropdown-style"
                    >
                    </ava-dropdown>
                  </div>
        </div>
        <!-- Two Column Row -->
        <div class="two-column-row">
          <!-- Goal -->
          <div class="field-group">
            <label class="field-label">
              Goal <span class="required-asterisk">*</span>
            </label>
            <ava-textarea
              id="goal"
              name="goal"
              [formControl]="getControl('goal')"
              placeholder="Enter Goal"
              [rows]="4"
              size="sm"
              [error]="getFieldError('goal')"
              [fullWidth]="true"
              [required]="true"
            >
            </ava-textarea>
          </div>

          <!-- Description -->
          <div class="field-group">
            <label class="field-label">
              Description <span class="required-asterisk">*</span>
            </label>
            <ava-textarea
              id="templateDescription"
              name="templateDescription"
              [formControl]="getControl('description')"
              placeholder="Description"
              [rows]="4"
              size="sm"
              [error]="getFieldError('description')"
              [fullWidth]="true"
              [required]="true"
            >
            </ava-textarea>
          </div>
        </div>

        <!-- Second Two Column Row -->
        <div class="two-column-row">
          <!-- Backstory -->
          <div class="field-group">
            <label class="field-label">
              Backstory <span class="required-asterisk">*</span>
            </label>
            <ava-textarea
              id="backstory"
              name="backstory"
              [formControl]="getControl('backstory')"
              placeholder="Enter Backstory"
              [rows]="4"
              size="sm"
              [error]="getFieldError('backstory')"
              [fullWidth]="true"
              [required]="true"
            >
            </ava-textarea>
          </div>

          <!-- Expected Output -->
          <div class="field-group">
            <label class="field-label">
              Expected Output <span class="required-asterisk">*</span>
            </label>
            <ava-textarea
              id="expectedOutput"
              name="expectedOutput"
              [formControl]="getControl('expectedOutput')"
              placeholder="Enter Expected Output"
              [rows]="4"
              size="sm"
              [error]="getFieldError('expectedOutput')"
              [fullWidth]="true"
              [required]="true"
            >
            </ava-textarea>
          </div>
          </div>
            <div
                  class="fields-row-2"
                  *ngIf="
                    getControl('promptType').value &&
                    getControl('promptType').value.toLowerCase() !==
                      'zero shot'
                  "
                >
                  <div class="field-col" *ngIf="getControl('promptType').value && getControl('promptType').value.toLowerCase() === 'chain of thought'">
                    <ava-textarea
                      id="intermediateSteps"
                      name="intermediateSteps"
                      label="{{ promptLabels.intermediateSteps }}"
                      [formControl]="getControl('intermediateSteps')"
                      placeholder="{{
                        promptLabels.placeholderIntermediateSteps
                      }}"
                      [rows]="4"
                      size="sm"
                      [fullWidth]="true"
                      [error]="getFieldError('intermediateSteps')"
                      [required]="true"
                    >
                    </ava-textarea>
                  </div>
                      <div class="optional-sections">
                  <div
                    class="examples-accordion-wrapper"
                    *ngIf="
                      !getControl('promptType').value ||
                      getControl('promptType').value.toLowerCase() !==
                        'zero shot'
                    "
                  >
                    <ava-accordion
                      id="examples-accordion"
                      #accordion
                      [expanded]="false"
                      [animation]="true"
                      [controlled]="false"
                      iconClosed="chevron-down"
                      iconOpen="chevron-up"
                      iconPosition="right"
                      type="default"
                    >
                      <div header>
                        {{ promptLabels.addExamples }}
                      </div>
                      <div content>
                          <div #examplesEnd></div>

                        <div class="example">
                          <div class="example-content" formArrayName="examples">
                            <div
                              class="example-group"
                              *ngFor="
                                let example of examples.controls;
                                let i = index
                              "
                              [formGroupName]="i"
                            >
                              <div class="field-col">
                                <ava-textarea
                                  [formControlName]="'input'"
                                  label="Input"
                                  name="{{ promptLabels.input }}"
                                  id="input"
                                  placeholder="{{
                                    promptLabels.placeholderInput
                                  }}"
                                  variant="primary"
                                  [rows]="2"
                                  size="sm"
                                  [fullWidth]="true"
                                >
                                </ava-textarea>
                              </div>
                              <div class="field-col">
                                <ava-textarea
                                  [formControlName]="'output'"
                                  label="Output"
                                  name="{{ promptLabels.output }}"
                                  id="output"
                                  placeholder="{{
                                    promptLabels.placeholderOutput
                                  }}"
                                  variant="primary"
                                  [rows]="2"
                                  size="sm"
                                  [fullWidth]="true"
                                >
                                </ava-textarea>
                              </div>
                              <!-- Remove button down and right-aligned -->
                              <div class="example-remove" *ngIf="i > 0">
                                <ava-button
                                  label="{{ promptLabels.remove }}"
                                  variant="danger"
                                  size="small"
                                  iconName="trash"
                                  iconPosition="left"
                                  (userClick)="removeExample(i)"
                                  [outlined]="true"
                                >
                                </ava-button>
                              </div>
                            </div>
                            <!-- Add button aligned to the left below the example group -->
                            <div class="example-actions">
                              <ava-button
                                label="{{ promptLabels.addExamples }}"
                                variant="secondary"
                                size="small"
                                iconName="plus"
                                iconPosition="right"
                                (userClick)="addExample()"
                              >
                              </ava-button>
                            </div>
                          </div>
                          <div class="add-consideration">
                            <ava-textarea
                              [formControl]="getControl('addConsideration')"
                              label="{{ promptLabels.additionalConsideration }}"
                              id="addConsideration"
                              name="addConsideration"
                              placeholder="{{
                                promptLabels.placeholderAdditionalConsideration
                              }}"
                              variant="primary"
                              [rows]="2"
                              size="sm"
                              [fullWidth]="true"
                            >
                            </ava-textarea>
                          </div>
                        </div>
                      </div>
                    </ava-accordion>
                  </div>
                </div>
                </div>
        <!-- Ask AAVA Button -->
        <div class="ask-aava-wrapper">
          <ava-button
            label="Ask AAVA"
            variant="secondary"
            size="small"
            iconName="WandSparkles"
            (userClick)="toggleAskAvaModal()"
            iconPosition="right"
          >
          </ava-button>
        </div>
      </div>

      <!-- Action Buttons -->
    </div>
          <div class="action-buttons">
        <ava-button
          label="Cancel"
          variant="secondary"
          size="medium"
          (userClick)="onCancel()"
        >
        </ava-button>
        <ava-button
          label="Save"
          variant="primary"
          size="medium"
          (userClick)="onSave()"
          [disabled]="isSaveDisabled()"
        >
        </ava-button>
      </div>
  </form>
</div>

<!-- Ask Ava Modal (existing) -->
<app-ask-ava-wrapper
  [show]="showAskAvaModal"
  [prompt]="prompt"
  [isLoading]="isLoading"
  [showOutput]="showPromptOutput"
  (oNClickGenerate)="onClickGenerate()"
  (oNClickClosed)="onClose()"
  (oNClickUse)="onUse()"
  (oNClickReset)="onReset()"
  (oNClickCancel)="onCancle()"
>
  <form [formGroup]="promptsOutputForm">
    <!-- Individual Agent - Freeform Tab -->
    <ng-container *ngIf="isIndividualAgent && selectedTab === 'freeform'">
      <ava-textarea
        id="freeformPrompt"
        name="freeformPrompt"
        label="{{ promptLabels.freeformPrompt }}"
        [formControl]="getOutPutControl('promptTask')"
        placeholder="Please enter the prompt task"
        [rows]="8"
        size="md"
        [fullWidth]="true"
        [readonly]="true"
      >
      </ava-textarea>
    </ng-container>

    <!-- Individual Agent - Template Tab OR Collaborative Agent -->
    <ng-container *ngIf="(isIndividualAgent && selectedTab === 'template') || isCollaborativeAgent">
      <div class="collaborative-output-fields">
        <!-- Role -->
        <div class="field-group">
          <ava-textbox
            [formControl]="getOutPutControl('role')"
            label="{{ promptLabels.role }}"
            id="outputRole"
            name="outputRole"
            placeholder="{{ promptLabels.placeholderRole }}"
            variant="primary"
            size="md"
            [readonly]="true"
          >
          </ava-textbox>
        </div>

        <!-- Two Column Row -->
        <div class="two-column-row">
          <!-- Goal -->
          <div class="field-group">
            <ava-textarea
              [formControl]="getOutPutControl('goal')"
              label="Goal"
              id="outputGoal"
              name="outputGoal"
              placeholder="{{ promptLabels.placeholderGoal }}"
              size="sm"
              [rows]="4"
              [fullWidth]="true"
              [readonly]="true"
            >
            </ava-textarea>
          </div>

          <!-- Description -->
          <div class="field-group">
            <ava-textarea
              [formControl]="getOutPutControl('description')"
              label="Description"
              id="outputDescription"
              name="outputDescription"
              placeholder="Description"
              size="sm"
              [rows]="4"
              [fullWidth]="true"
              [readonly]="true"
            >
            </ava-textarea>
          </div>
        </div>

        <!-- Second Two Column Row -->
        <div class="two-column-row">
          <!-- Backstory -->
          <div class="field-group">
            <ava-textarea
              [formControl]="getOutPutControl('backstory')"
              label="Backstory"
              id="outputBackstory"
              name="outputBackstory"
              placeholder="Enter Backstory"
              size="sm"
              [rows]="4"
              [fullWidth]="true"
              [readonly]="true"
            >
            </ava-textarea>
          </div>

          <!-- Expected Output -->
          <div class="field-group">
            <ava-textarea
              [formControl]="getOutPutControl('expectedOutput')"
              label="Expected Output"
              id="outputExpectedOutput"
              name="outputExpectedOutput"
              placeholder="Enter Expected Output"
              size="sm"
              [rows]="4"
              [fullWidth]="true"
              [readonly]="true"
            >
            </ava-textarea>
          </div>
        </div>
      </div>
    </ng-container>
  </form>
</app-ask-ava-wrapper>
