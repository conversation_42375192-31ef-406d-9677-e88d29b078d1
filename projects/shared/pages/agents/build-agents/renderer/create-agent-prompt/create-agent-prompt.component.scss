// Remove default margins and ensure full width
:host {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// Individual agent styles
.create-agent-prompt-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 5px;
  width: min(95vw, 1050px);
  height: 90vh;
  max-height: 90vh;
  margin: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  // sticky header/actions and scrollable middle at high zoom
  .popup-header { position: sticky; top: 0; background: #fff; z-index: 1; padding-bottom: 8px; }
  .action-buttons { position: sticky; bottom: 0; background: #fff; padding-top: 16px; }

}
.create-agent-prompt-container form,
.create-agent-prompt-collaborative-container form {
  display: flex; flex-direction: column; flex: 1 1 auto; min-height: 0;
}

/* Keep footer fixed while body scrolls (Individual) */
.create-agent-prompt-container .form-layout > .action-buttons {
  position: sticky !important;
  bottom: 0;
  z-index: 3;
  background: #fff;
  padding-top: 12px;
  padding-bottom: 12px;
  margin-top: 0 !important;
}
.create-agent-prompt-container .action-buttons {
  position: sticky !important;
  bottom: 0;
  background: #fff;
  z-index: 3;
  margin-top: 0 !important;
}


// Individual layout: make the left and middle columns scroll together
.create-agent-prompt-container {
  /* Make header sticky inside the scroll container */
  .tab-container { position: sticky; top: 0; background: #fff; z-index: 2; padding-bottom: 8px; }

  /* Scroll container for the body (both left and middle columns) */
  .form-layout { display: flex; flex-direction: column; flex: 1 1 auto; min-height: 0; overflow-y: auto; }

  /* Ensure children can shrink to enable scrolling */
  .left-column { flex: 0 0 auto; min-height: 0; }
  .middle-column { display: flex; flex-direction: column; flex: 1 1 auto; min-height: 0; }
  .form-container { display: flex; flex-direction: column; flex: 1 1 auto; min-height: 0; }
  .tab-content { display: flex; flex-direction: column; flex: 1 1 auto; min-height: 0; }

  /* Remove nested scroll; let the whole body scroll */
  .template-form { flex: 1 1 auto; min-height: 0; overflow: visible; }
}


.tab-container{
  display: flex;
  gap: 120px;
  margin-bottom: 20px;
}

::ng-deep .ava-textbox__label{
  text-align: left;
  padding-top: 10px;
}

::ng-deep #description .ava-textarea__label {
  text-align: left;
  padding-top: 10px;
}




// Collaborative agent styles
.create-agent-prompt-collaborative-container {
  background: #ffffff;
  border-radius: 8px;
  padding: 24px;
  width: min(95vw, 1050px);
  height: 90vh;
  max-height: 90vh;
  margin: 0;
  // sticky header/actions and scrollable middle at high zoom
  .popup-header { position: sticky; top: 0; background: #fff; z-index: 1; padding-bottom: 8px; }
  .action-buttons { position: sticky; bottom: 0; background: #fff; padding-top: 16px; }

  box-shadow: none;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  /* establish scroll context */
  min-height: 0;


}

.collaborative-popup-container::-webkit-scrollbar {
  display: none; // Chrome/Safari/Edge
}

// Scrollable content inside popup
.popup-content,
.collaborative-content {
  display: flex;
  flex-direction: column;
  flex: 1 1 auto;
  min-height: 0;
  gap: 8px;
  overflow-y: auto;
  padding-right: 8px;
  padding-bottom: 96px;
}

.form-fields,
.collaborative-form-fields {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1 1 auto;
  min-height: 0;
  overflow: visible;
}

.two-column-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;

  > .field-group,
  > .field-col {
    flex: 1 1 320px;
    min-width: 280px;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
  }
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &.full-width {
    width: 100%;
  }

  &.half-width {
    width: 48%;

    @media (max-width: 768px) {
      width: 100%;
    }
  }
}

.popup-header {
  margin-bottom: 0;
  flex-shrink: 0;
}

.popup-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1d29;
  text-align: left;
  margin: 0;
  line-height: 1.4;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  text-align: left !important;
}

.required-asterisk {
  color: #ef4444;
  margin-left: 2px;
}

.ask-aava-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
  flex-shrink: 0;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: auto;
  padding-top: 24px;
  flex-shrink: 0;
  border-top: 1px solid #f3f4f6;
}

// Override component styles to match Figma design
:host ::ng-deep {
  ava-textbox .ava-textbox__container,
  ava-textarea .ava-textarea__container {
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    width: 100%;

    &:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }

  ava-textbox .ava-textbox__input,
  ava-textarea .ava-textarea__input {
    padding: 12px 16px;
    font-size: 14px;
    color: #374151;

    &::placeholder {
      color: #9ca3af;
    }
  }

  ava-textarea .ava-textarea__input {
    min-height: 60px;
    resize: vertical;
  }

  .collaborative-popup-container {
    ava-textarea .ava-textarea__input {
      min-height: 50px;
    }
  }

  // Button styling
  ava-button {
    .ava-button {
      border-radius: 8px;
      font-weight: 500;
      padding: 12px 24px;

      &.ava-button--primary {
        background-color: #3b82f6;
        border-color: #3b82f6;

        &:hover {
          background-color: #2563eb;
          border-color: #2563eb;
        }
      }

      &.ava-button--secondary {
        background-color: transparent;
        border-color: #d1d5db;
        color: #374151;

        &:hover {
          background-color: #f9fafb;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .prompt-popup-container,
  .collaborative-popup-container {
    padding: 24px;
  }

  .form-fields,
  .collaborative-form-fields {
    gap: 16px;
  }

  .field-group.half-width {
    width: 100%;
  }

  .action-buttons {
    gap: 12px;
  }
}

// Hide default popup message
::ng-deep .popup-container .popup-message.center-align {
  display: none !important;
}

// FIX: enable scroll but hide scrollbars on popup container
::ng-deep .popup-container {
  overflow: hidden; /* inner component handles scrolling */
}

::ng-deep .popup-container::-webkit-scrollbar { display: none; }
::ng-deep .askava-container label {
padding-top: 16px;
margin-left: 5px;
}
 .freeform-content-wrapper {
  .field-label {
    text-align: left !important;
    display: block;
    margin-bottom: 8px;
    padding-top: 10px;
  }
}
/* Freeform tab: shrink popup to content height (up to 90vh) to remove large bottom gap */
.create-agent-prompt-container:has(.freeform-content-wrapper) {
  height: auto;
  max-height: 90vh; /* still cap overall height */
}


/* Individual -> Freeform tab only: fit content and remove extra bottom gap without affecting Template */
.create-agent-prompt-container .middle-column:has(.freeform-content-wrapper) { flex: 0 0 auto; }
.create-agent-prompt-container .form-container:has(> .tab-content > .freeform-content-wrapper) { flex: 0 0 auto; }
.create-agent-prompt-container .tab-content:has(> .freeform-content-wrapper) { flex: 0 0 auto; overflow: visible; }
.create-agent-prompt-container .freeform-content-wrapper { margin-bottom: 0 !important; padding-bottom: 0 !important; }
/* Ensure the popup scrolls if content exceeds available height */
.create-agent-prompt-container .form-layout:has(.freeform-content-wrapper) { overflow-y: auto; }


.template-form {
  .fields-row {
    display: flex;
    gap: 16px;
    margin-bottom: 20px;

    &.two-column {
      .field-col {
        flex: 1;
        width: calc(50% - 8px);
      }
    }

    .field-col {
      &.half-width {
        flex: 0 0 calc(50% - 8px);
        max-width: calc(50% - 8px);
      }
    }
  }
}

::ng-deep .template-form .ava-textarea__label {
 text-align: left;
}

::ng-deep .ava-textbox__error-text{
  text-align: left !important;
}

::ng-deep .ava-textarea__error-text{
  text-align: left !important;
}

::ng-deep ava-textarea .ava-textarea__input #description {
  height: 70px !important;
}

::ng-deep #promptType{

// Spacing between Intermediate Steps and Add Examples in popup
.template-form .optional-sections,
.create-agent-prompt-collaborative-container .optional-sections {
  margin-top: 14px; /* add some breathing room */
}

// Extra safety: if optional-sections is missing, pad the accordion itself
.template-form .examples-accordion-wrapper,
.create-agent-prompt-collaborative-container .examples-accordion-wrapper {
  margin-top: 12px;
}

  padding-top: 9px;
}

/* Force vertical spacing between Intermediate Steps and Add Examples */
.template-form .fields-row-2 > .optional-sections { margin-top: 16px !important; }
.create-agent-prompt-collaborative-container .fields-row-2 > .optional-sections { margin-top: 16px !important; }

/* Fallback: add space directly above the accordion if wrapper is missing */
.template-form .examples-accordion-wrapper,
.create-agent-prompt-collaborative-container .examples-accordion-wrapper { margin-top: 14px !important; }

/* Template tab: Make Add Examples accordion content scrollable if it overflows */
.create-agent-prompt-container .template-form .examples-accordion-wrapper ava-accordion ::ng-deep .accordion-content {
  max-height: 40vh; /* ensure room within popup */
  overflow-y: auto;
}

/* Make sure inner example list doesn't collapse and can grow */
.create-agent-prompt-container .template-form .example-content {
  display: block;
  min-height: 0;
}
/* Apply the same accordion content scrolling in Collaborative popup */
.create-agent-prompt-collaborative-container .examples-accordion-wrapper ava-accordion ::ng-deep .accordion-content {
  max-height: 40vh;
  overflow-y: auto;
}

/* Make Input/Output fields full-width and stacked within Add Examples (Individual + Collaborative) */
.create-agent-prompt-container .template-form .examples-accordion-wrapper .example-group,
.create-agent-prompt-collaborative-container .examples-accordion-wrapper .example-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.create-agent-prompt-container .template-form .examples-accordion-wrapper .example-group .field-col,
.create-agent-prompt-collaborative-container .examples-accordion-wrapper .example-group .field-col {
  flex: 0 0 100% !important;
  width: 100% !important;
  max-width: 100% !important;
}



/* Hide scrollbars (keep scrolling) for popup content areas and Add Examples accordion */
.create-agent-prompt-container .form-layout,
.create-agent-prompt-collaborative-container .popup-content,
.create-agent-prompt-collaborative-container .collaborative-content,
.create-agent-prompt-container .template-form .examples-accordion-wrapper ava-accordion ::ng-deep .accordion-content,
.create-agent-prompt-collaborative-container .examples-accordion-wrapper ava-accordion ::ng-deep .accordion-content {
  -ms-overflow-style: none; /* IE/Edge */
  scrollbar-width: none;    /* Firefox */
}

.create-agent-prompt-container .form-layout::-webkit-scrollbar,
.create-agent-prompt-collaborative-container .popup-content::-webkit-scrollbar,
.create-agent-prompt-collaborative-container .collaborative-content::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Edge (Chromium) */
}

.create-agent-prompt-container .template-form .examples-accordion-wrapper ava-accordion ::ng-deep .accordion-content::-webkit-scrollbar,
.create-agent-prompt-collaborative-container .examples-accordion-wrapper ava-accordion ::ng-deep .accordion-content::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Edge (Chromium) */
}
