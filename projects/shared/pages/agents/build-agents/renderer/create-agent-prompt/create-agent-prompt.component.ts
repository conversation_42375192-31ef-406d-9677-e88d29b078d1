
import { Component, On<PERSON>nit, On<PERSON><PERSON>roy, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, ElementRef } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
  FormArray,
  Validators,
} from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { ChatMessage } from '@shared/components/chat-window/chat-window.component';
import { MOCK_PROMPTS } from '@shared/mock-data/prompt-mock-data';
import { ToolExecutionService } from '@shared/services/tool-execution/tool-execution.service';
import { Subscription } from 'rxjs';
import promptsLabels from '../../../../libraries/prompts/constants/prompts.json';
import {
  AvaTextareaComponent,
  AvaTextboxComponent,
  ButtonComponent,
  AccordionComponent,
  DropdownComponent,
  DialogService,
  CubicalLoadingComponent,
} from '@ava/play-comp-library';
import { ToggleButtonComponent } from '@shared/components/toggle-button/toggle-button.component';
import { PromptsService } from '@shared/services/prompts.service';
import { LucideAngularModule } from 'lucide-angular';
import { AskAvaWrapperComponent } from '@shared/components/ask-ava-wrapper/ask-ava-wrapper.component';
import { PromptEnhanceService } from '@shared/services/prompt-enhance.service';
import { PromptsModes, USE_CASE_IDENTIFIER } from '../../../../libraries/prompts/constants/constants';
import { Inject, Optional } from '@angular/core';
@Component({
  selector: 'app-create-agent-prompt',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    AvaTextareaComponent,
    ButtonComponent,
    ToggleButtonComponent,
    AccordionComponent,
    DropdownComponent,
    LucideAngularModule,
    AskAvaWrapperComponent,
    CubicalLoadingComponent,
  ],
   templateUrl: './create-agent-prompt.component.html',
  styleUrls: ['./create-agent-prompt.component.scss'],
})
export class CreateAgentPromptComponent implements OnInit, OnDestroy {
  @Output() closed = new EventEmitter<any>();
  isViewMode: boolean = false;
  promptTypeOptions: { name: string; value: string }[] = [];
  selectedPromptType: string = '';
  selectedDropdownValue: string = ''; // Add this property to track selected value
  selectedRoleValue: string = '';
  previousAccordionState: boolean = false;
  showSuccessPopup = false;
  submissionSuccess = false;
  popupTitle: string = '';
  popupMessage: string = '';
  formChanged = false;
  private isPatching = false;
  saveLoader = false;
  @Input() isIndividualAgent = false;
  @Input() isCollaborativeAgent = false;


  iconName = 'check-circle';
  @ViewChild('accordion') accordionRef!: AccordionComponent;
  @ViewChild('examplesEnd', { static: false }) examplesEndRef!: ElementRef<HTMLElement>;

  // Labels used across the Prompt UI components (titles)
  public promptLabels = promptsLabels.labels;
  promptTabs = [
    { label: 'Freeform', value: 'freeform' },
    { label: 'Template', value: 'template' },
  ];

  showAskAvaModal = false;
  showPromptOutput = false;
  isLoading = false;
  initLoader = false;

  selectedTab: string = 'freeform';

  onTabSelected(tabValue: string): void {
    this.selectedTab = tabValue;
    const promptTaskControl = this.promptForm.get('promptTask');
    if (tabValue === 'template') {
      promptTaskControl?.clearValidators();
    } else if (tabValue === 'freeform') {
      promptTaskControl?.setValidators([Validators.required]);
    }

    promptTaskControl?.updateValueAndValidity();
  }

  onPromptTypeChange(event: any): void {
    // Handle dropdown selection change
    if (event && event.selectedOptions && event.selectedOptions.length > 0) {
      const selectedValue = event.selectedOptions[0].value;
      this.promptForm
        .get('promptType')
        ?.setValue(selectedValue, { emitEvent: false });
      this.selectedDropdownValue = selectedValue;
    }
  }

  // Mode flags
  categoryId: number | null = null;
  promptId: string | null = null;
  isEditMode: boolean = false;
  isExecuteMode: boolean = false;
  showChatInterface: boolean = false;
  isLeftCollapsed = false;

  toggleLeftPanel() {
    this.isLeftCollapsed = !this.isLeftCollapsed;
  }

  promptForm: FormGroup;
  promptsOutputForm: FormGroup;

  // Chat interface properties
  chatMessages: ChatMessage[] = [];
  isProcessingChat: boolean = false;

  // Subscription
  private executionSubscription: Subscription = new Subscription();
  prompt = new FormControl('');

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private toolExecutionService: ToolExecutionService,
    private promptsService: PromptsService,
    private cdr: ChangeDetectorRef,
    private promptGenerateService: PromptEnhanceService,
    private dialogService: DialogService,
    @Optional() @Inject('dialogData') private dialogData?: any
  ) {
    this.promptForm = this.createPromtForm();
    this.promptsOutputForm = this.createPromtForm();
  }

  promptCache: Record<string, string> = {
    template: '',
    freeform: '',
  };

  private createPromtForm(): FormGroup {
    return this.fb.group({
      // Prompt details
      name: [''],
      promptDescription: [''],
      // Prompt task (for freeform)
      promptTask: ['', Validators.required],
      // Right side (Template configuration fields)
      role: ['', Validators.required],
      backstory: [''],
      description: [''],
      goal: [''],
      expectedOutput: [''],
      // Prompt Type Dropdown
      promptType: [''],
      // Examples (these fields are still in the form group, but no longer rendered in HTML)
      examples: this.fb.array([
        this.fb.group({
          input: [''],
          output: [''],
        }),
      ]),
      addConsideration: [''],
      // Steps (these fields are still in the form group, but no longer rendered in HTML)
      step1Instruction: [''],
      // Add intermediateSteps to the form group
      intermediateSteps: [''],
    });
  }

  ngOnInit(): void {
    // Handle dialog data if provided
    if (this.dialogData) {
      this.isIndividualAgent = this.dialogData.isIndividualAgent || false;
      this.isCollaborativeAgent = this.dialogData.isCollaborativeAgent || false;
    }

    this.initLoader = true;
    this.promptForm.valueChanges.subscribe(() => {
      if (!this.isPatching) {
        this.formChanged = true;
      }
    });
    // First load dropdown options
    this.promptsService.fetchPromptTypeDropdown().subscribe({
      next: (options) => {
        if (options && options.length && options[0].name && options[0].value) {
          this.promptTypeOptions = options;
        } else if (
          options &&
          typeof options === 'object' &&
          !Array.isArray(options)
        ) {
          this.promptTypeOptions = Object.keys(options).map((key) => ({
            name: options[key],
            value: options[key],
          }));
        } else {
          this.promptTypeOptions = [];
        }

        // After dropdown options are loaded, check if we need to load prompt data
        this.promptId = this.route.snapshot.paramMap.get('id');
        const executeParam = this.route.snapshot.queryParamMap.get('execute');
        const viewParam = this.route.snapshot.queryParamMap.get('view');

        this.isEditMode = !!this.promptId;
        this.isExecuteMode = executeParam === 'true';
        this.isViewMode = viewParam === 'true';
        this.showChatInterface = this.isExecuteMode;

        if (this.isEditMode && this.promptId) {
          this.loadPromptById(this.promptId);
        } else {
          // If not in edit mode but we have a stored prompt type, try to set it
          this.trySetStoredPromptType();
        }

        // If form is already patched but dropdown options were loaded later, try to update the dropdown
        this.tryUpdateDropdownAfterFormPatch();
      },
      error: (err) => {
        console.error('Dropdown API error:', err);
        // Even if dropdown fails, still try to load prompt data
        this.promptId = this.route.snapshot.paramMap.get('id');
        const executeParam = this.route.snapshot.queryParamMap.get('execute');
        const viewParam = this.route.snapshot.queryParamMap.get('view');

        setTimeout(() => {
          this.isEditMode = !!this.promptId;
          this.cdr.detectChanges(); // Optional: force update after flag is set
        });

        this.isExecuteMode = executeParam === 'true';
        this.isViewMode = viewParam === 'true';
        this.showChatInterface = this.isExecuteMode;

        if (this.isEditMode && this.promptId) {
          this.loadPromptById(this.promptId);
        }
      },
    });

    // Debug: log value changes for promptType
    this.promptForm.get('promptType')?.valueChanges.subscribe((val) => {
      // Update the selected dropdown value when form control changes
      this.selectedDropdownValue = val || '';
      console.log(
        'Form control value changed:',
        val,
        'selectedDropdownValue:',
        this.selectedDropdownValue,
      );
    });

    // Handle execute mode (this doesn't depend on dropdown options)
    const executeParam = this.route.snapshot.queryParamMap.get('execute');
    if (this.isExecuteMode && this.promptId) {
      this.chatMessages = [
        { from: 'ai', text: 'Hi Akash, this is the prompt testing' },
        { from: 'user', text: 'Test this input' },
        { from: 'ai', text: 'Here is the output' },
      ];

      setTimeout(() => {
        this.toolExecutionService.startExecution(
          this.promptId!,
          this.chatMessages,
        );
        this.executionSubscription = this.toolExecutionService
          .getExecutionState()
          .subscribe((state) => {
            if (state.isExecuting && state.toolId === this.promptId) {
              this.chatMessages = state.chatMessages;
            }
          });
      }, 100);
    }
    setTimeout(() => {
      this.initLoader = false;
    }, 1000);
  }

  // Add a property to store the prompt type for delayed setting
  private storedPromptType: string = '';

  // Method to try setting stored prompt type when dropdown options are loaded
  private trySetStoredPromptType(): void {
    if (this.storedPromptType && this.promptTypeOptions.length > 0) {
      const match = this.findMatchingDropdownOption(this.storedPromptType);
      if (match) {
        // Set the value immediately
        this.promptForm
          .get('promptType')
          ?.setValue(match.value, { emitEvent: false });
        this.selectedDropdownValue = match.value;

        // Also set it after a delay to ensure the dropdown is rendered
        setTimeout(() => {
          this.promptForm
            .get('promptType')
            ?.setValue(match.value, { emitEvent: false });
          this.selectedDropdownValue = match.value;
        }, 200);

        this.storedPromptType = ''; // Clear after setting
      }
    }
  }

  // Method to try updating dropdown after form is already patched
  private tryUpdateDropdownAfterFormPatch(): void {
    const currentValue = this.promptForm.get('promptType')?.value;
    if (currentValue && this.promptTypeOptions.length > 0) {
      // Check if the current value is not in the dropdown options
      const isValueInOptions = this.promptTypeOptions.some(
        (opt) => opt.value === currentValue || opt.name === currentValue,
      );

      if (!isValueInOptions) {
        // Try to find a matching option
        const match = this.findMatchingDropdownOption(currentValue);
        if (match) {
          // Set the value immediately
          this.promptForm
            .get('promptType')
            ?.setValue(match.value, { emitEvent: false });
          this.selectedDropdownValue = match.value;

          // Also set it after a delay to ensure the dropdown is rendered
          setTimeout(() => {
            this.promptForm
              .get('promptType')
              ?.setValue(match.value, { emitEvent: false });
            this.selectedDropdownValue = match.value;
          }, 200);
        }
      }
    }
  }

  // Helper method to find matching dropdown option
  private findMatchingDropdownOption(
    promptType: string,
  ): { name: string; value: string } | null {
    if (!promptType || this.promptTypeOptions.length === 0) {
      return null;
    }

    // Try exact match first (case-insensitive)
    let match = this.promptTypeOptions.find(
      (opt) => opt.value.toLowerCase() === promptType.toLowerCase(),
    );

    // If no exact match, try matching by name
    if (!match) {
      match = this.promptTypeOptions.find(
        (opt) => opt.name.toLowerCase() === promptType.toLowerCase(),
      );
    }

    // If still no match, try partial matching
    if (!match) {
      match = this.promptTypeOptions.find(
        (opt) =>
          opt.value.toLowerCase().includes(promptType.toLowerCase()) ||
          opt.name.toLowerCase().includes(promptType.toLowerCase()) ||
          promptType.toLowerCase().includes(opt.value.toLowerCase()) ||
          promptType.toLowerCase().includes(opt.name.toLowerCase()),
      );
    }

    return match || null;
  }

  private loadPromptById(promptId: string): void {
    this.promptsService.getPromptById(promptId).subscribe({
      next: (prompt) => {
        if (prompt) {
          this.categoryId = prompt.categoryId;
          this.patchPromptForm(prompt);
        } else {
        }
      },
      error: (err) => {
        console.error('Error fetching prompt:', err);
      },
    });
  }

  private patchPromptForm(prompt: any): void {
    this.isPatching = true;
    // After form is fully patched
    setTimeout(() => {
      this.isPatching = false;
      this.formChanged = false; // form hasn't changed yet
    }, 0);

    if (this.isViewMode) {
      setTimeout(() => {
        this.promptForm.disable({ emitEvent: false });
      }, 0);
    }

    // Set selected tab first based on type
    this.selectedTab = prompt.type === 'free form' ? 'freeform' : 'template';

    // Improved dropdown type matching logic
    let dropdownType = '';
    if (prompt.type) {
      if (this.promptTypeOptions.length > 0) {
        // Dropdown options are available, try to find a match
        const match = this.findMatchingDropdownOption(prompt.type);
        if (match) {
          dropdownType = match.value;
        } else {
          // If no match found, use the original type as fallback
          dropdownType = prompt.type;
          console.warn(
            `No matching dropdown option found for prompt type: "${prompt.type}". Available options:`,
            this.promptTypeOptions,
          );
        }
      } else {
        // Dropdown options not loaded yet, store the type for later
        this.storedPromptType = prompt.type;
        dropdownType = prompt.type;
      }
    } else {
      dropdownType = prompt.type || '';
    }

    // Patch only the relevant fields based on the selected tab/type
    const patchObj: any = {
      name: prompt.name || '',
      role: prompt.role || '',
      goal: prompt.goal || '',
      backstory: prompt.backstory || '',
      expectedOutput: prompt.expectedOutput || '',
      intermediateSteps: prompt.intermediateSteps || '',
      promptType: dropdownType,
    };

    if (this.selectedTab === 'freeform') {
      patchObj.promptTask = prompt.prompt || '';
      patchObj.promptDescription = prompt.promptDescription || '';
      // Clear template fields
      patchObj.description = prompt.description;
    } else if (this.selectedTab === 'template') {
      patchObj.description = prompt.description || '';
      patchObj.promptDescription = prompt.promptDescription || '';
      // Clear freeform fields
      patchObj.promptTask = '';
    }

    this.promptForm.patchValue(patchObj);

    // Update the selected dropdown value
    this.selectedDropdownValue = dropdownType;
    console.log(
      'patchPromptForm - dropdownType:',
      dropdownType,
      'selectedDropdownValue:',
      this.selectedDropdownValue,
    );

    // If we're in template mode and have a dropdown type, ensure it's set after a delay
    if (this.selectedTab === 'template' && dropdownType) {
      setTimeout(() => {
        this.promptForm
          .get('promptType')
          ?.setValue(dropdownType, { emitEvent: false });
        this.selectedDropdownValue = dropdownType;
        console.log(
          'After delay - dropdownType:',
          dropdownType,
          'selectedDropdownValue:',
          this.selectedDropdownValue,
        );
      }, 100);
    }

    // Patch examples if available
    if (prompt.examples && Array.isArray(prompt.examples)) {
      const exampleArray = this.promptForm.get('examples') as FormArray;

      // Clear existing examples
      exampleArray.clear();

      // Push each example into the form array
      prompt.examples.forEach((ex: any) => {
        exampleArray.push(
          this.fb.group({
            input: [ex.exampleInput || ''],
            output: [ex.exampleOutput || ''],
          }),
        );
      });
    }
  }

  selectTab(tab: 'freeform' | 'template' | string) {
    this.selectedTab = tab as 'freeform' | 'template';
  }

  ngOnDestroy(): void {
    // Clean up subscription
    if (this.executionSubscription) {
      this.executionSubscription.unsubscribe();
    }
  }

  confirmSave(): void {
    this.dialogService
      .confirmation({
        title: 'Save Prompt?',
        message: 'Are you sure you want to save this prompt?',
        confirmButtonText: 'Save',
        cancelButtonText: 'Cancel',
        confirmButtonVariant: 'primary',
        icon: 'save',
      })
      .then((result) => {
        if (result.confirmed) {
          this.onSave();
        }
      });
  }

  confirmUpdate(): void {
    this.dialogService
      .confirmation({
        title: 'Update Prompt?',
        message: 'Are you sure you want to update this prompt?',
        confirmButtonText: 'Update',
        cancelButtonText: 'Cancel',
        confirmButtonVariant: 'primary',
        icon: 'square-pen',
      })
      .then((result) => {
        if (result.confirmed) {
          this.onSave();
        }
      });
  }


onSave(): void {

  this.formChanged = false;

  if (this.isViewMode) {

    return;

  }

  const nameControl = this.promptForm.get('name');

  const promptTaskControl = this.promptForm.get('promptTask');

  const promptTypeControl = this.promptForm.get('promptType');

  const promptDescControl = this.promptForm.get('promptDescription');

  const expectedOutputControl = this.promptForm.get('expectedOutput');

  const roleControl = this.promptForm.get('role');

  const goalControl = this.promptForm.get('goal');

  const backstoryControl = this.promptForm.get('backstory');

  const stepsControl = this.promptForm.get('intermediateSteps');

  // Clear validators

  stepsControl?.clearValidators();

  nameControl?.clearValidators();

  promptTaskControl?.clearValidators();

  promptTypeControl?.clearValidators();

  promptDescControl?.clearValidators();

  expectedOutputControl?.clearValidators();

  roleControl?.clearValidators();

  goalControl?.clearValidators();

  backstoryControl?.clearValidators();

  // Apply validators based on mode (Collaborative behaves like Template)

  nameControl?.setValidators([Validators.required]);

  promptDescControl?.setValidators([Validators.required]);

  const isTemplateMode = this.isCollaborativeAgent ? true : this.selectedTab === 'template';

  if (isTemplateMode) {

    expectedOutputControl?.setValidators([Validators.required]);

    roleControl?.setValidators([Validators.required]);

    goalControl?.setValidators([Validators.required]);

    backstoryControl?.setValidators([Validators.required]);

    promptTypeControl?.setValidators([Validators.required]);

    promptTaskControl?.clearValidators();

    promptTaskControl?.setValue('');

  } else {

    // Individual Freeform mode
    promptTaskControl?.setValidators([Validators.required]);

    // Only set default type for individual Freeform, not Collaborative
    if (!this.isCollaborativeAgent) {
      this.promptForm.get('promptType')?.setValue('free form', { emitEvent: false });
    }

  }

  // Update value/validity

  nameControl?.updateValueAndValidity();

  promptTaskControl?.updateValueAndValidity();

  promptTypeControl?.updateValueAndValidity();

  promptDescControl?.updateValueAndValidity();

  expectedOutputControl?.updateValueAndValidity();

  roleControl?.updateValueAndValidity();

  goalControl?.updateValueAndValidity();

  backstoryControl?.updateValueAndValidity();

  if (this.promptForm.value.promptType === 'Chain of Thought') {

    stepsControl?.setValidators([Validators.required]);

  }

  stepsControl?.updateValueAndValidity();

  // Build payload

  const formValues = this.promptForm.value;

  const promptType = (formValues.promptType || '').trim();

  // Collaborative behaves like Template for payload mapping
  const isTemplateLike = this.isCollaborativeAgent || this.selectedTab === 'template';

  let createPayload: any = {

    categoryId: this.categoryId ?? 1,

    type: promptType,

    name: (formValues.name || '').trim(),

    role: (formValues.role || '').trim(),

    goal: (formValues.goal || '').trim(),

    backstory: (formValues.backstory || '').trim(),

    description: (formValues.description || '').trim(),

    promptDescription: (formValues.promptDescription || '').trim(),

    expectedOutput: (formValues.expectedOutput || '').trim(),

    prompt: isTemplateLike ? (formValues.description || '').trim() : (formValues.promptTask || '').trim(),

  };

  if (promptType === 'One Shot/Multi Shot' || promptType === 'One Shot' || promptType === 'Multi Shot') {

    createPayload.examples = this.examples.getRawValue().map((e: any) => ({

      exampleInput: e.input,

      exampleOutput: e.output,

    }));

    createPayload.additionalConsideration = formValues.addConsideration;

  } else if (promptType === 'Chain of Thought') {

    createPayload.intermediateSteps = formValues.intermediateSteps;

    createPayload.examples = this.examples.getRawValue().map((e: any) => ({

      exampleInput: e.input,

      exampleOutput: e.output,

    }));

    createPayload.additionalConsideration = formValues.addConsideration;

  } else if (promptType.toLowerCase() === 'free form') {

    if (this.isCollaborativeAgent) {
      // In Collaborative flow, treat Free Form like template: prompt comes from description
      createPayload.prompt = (formValues.description || '').trim();
    } else {
      // Individual Free Form keeps minimal payload
      createPayload = {
        categoryId: this.categoryId ?? 1,
        type: promptType,
        name: (formValues.name || '').trim(),
        prompt: (formValues.promptTask || '').trim(),
        promptDescription: (formValues.promptDescription || '').trim(),
      };
    }

  }

  Object.keys(createPayload).forEach((key) => {

    if (createPayload[key] === undefined) {

      delete createPayload[key];

    }

  });

  // Use local loader flag (do NOT open dialogService.loading here)

  this.saveLoader = true;

  if (this.isEditMode && this.promptId) {

    const editPayload = { ...createPayload, id: this.promptId };

    this.promptsService.editPrompt(editPayload).subscribe({

      next: (info) => {

        this.saveLoader = false;

        // Emit close immediately so DialogService destroys this dialog

        this.closed.emit({ saved: true, data: info });

        // Show success dialog separately (doesn't interfere)

        this.dialogService.success({

          title: 'Success!',

          message: info?.info?.message || info.message || 'Prompt updated successfully!',

        });

        // optional local cleanup

        // this.resetForm();

      },

      error: (error) => {

        this.saveLoader = false;

        this.dialogService.error({

          title: 'Update Failed',

          message: error?.error?.message || error.message || 'Failed to update prompt. Please try again.',

          showRetryButton: true,

          retryButtonText: 'Retry',

        }).then((result) => {

          if (result?.action === 'retry') {

            this.onSave();

          }

        });

      },

    });

  } else {

    this.promptsService.addPrompt(createPayload).subscribe({

      next: (info) => {

        this.saveLoader = false;

        // Emit close immediately so DialogService destroys this dialog

        this.closed.emit({ saved: true, data: info });

        // Show success dialog separately

        this.dialogService.success({

          title: 'Success!',

          message: info?.info?.message || info.message || 'Prompt created successfully!',

        });

        // Reset examples locally if you want

        const examplesArray = this.promptForm.get('examples') as FormArray;

        if (examplesArray) {

          while (examplesArray.length > 0) examplesArray.removeAt(0);

          examplesArray.push(this.fb.group({ input: [''], output: [''] }));

        }

      },

      error: (error) => {

        this.saveLoader = false;

        this.dialogService.error({

          title: 'Creation Failed',

          message: error?.error?.message || error.message || 'Failed to create prompt. Please try again.',

          showRetryButton: true,

          retryButtonText: 'Retry',

        }).then((result) => {

          if (result?.action === 'retry') {

            this.onSave();

          }

        });

      },

    });

  }

}



  onExecute(): void {
    if (!this.promptForm.valid) {
      this.dialogService.warning({
        title: 'Form Incomplete',
        message: 'Please complete the prompt configuration before executing.',
        showProceedButton: false,
      });
      return;
    }

    if (this.promptId) {
      // If we're already in execute mode with chat interface showing
      if (this.isExecuteMode && this.showChatInterface) {
        // Process the execution
      } else {
        // Show loading dialog during execution setup
        this.dialogService.loading({
          title: 'Initializing Execution...',
          message: 'Setting up the prompt execution environment.',
          showProgress: false,
          showCancelButton: false,
        });

        // Set flags to show chat interface
        this.isExecuteMode = true;
        this.showChatInterface = true;

        // Set the initial messages
        this.chatMessages = [
          {
            from: 'ai',
            text: 'Hi Akash, this is the prompt testing',
          },
          {
            from: 'user',
            text: 'Test this input',
          },
          {
            from: 'ai',
            text: 'Here is the output',
          },
        ];

        // Delay starting the execution service
        setTimeout(() => {
          this.dialogService.close(); // Close loading dialog
          this.toolExecutionService.startExecution(
            this.promptId!,
            this.chatMessages,
          );
        }, 1000);
      }
    }
  }

  onExit(): void {
    // If we're in execute mode with chat interface showing
    if (this.isExecuteMode && this.isEditMode) {
      // Return to edit mode without chat interface
      this.isExecuteMode = false;
      this.showChatInterface = false;
      this.toolExecutionService.stopExecution();
    } else {
      // Check if form has unsaved changes
      if (this.promptForm.dirty && !this.isViewMode) {
        this.dialogService
          .warning({
            title: 'Unsaved Changes',
            message:
              'You have unsaved changes that will be lost if you continue. Are you sure you want to proceed?',
            showProceedButton: true,
            proceedButtonText: 'Proceed',
          })
          .then((result) => {
            if (result.action === 'proceed') {
              this.navigateBack();
            }
          });
      } else {
        this.navigateBack();
      }
    }
  }

  private navigateBack(): void {
    // Exit to prompts list at correct page
    this.router.navigate(['/libraries/prompts'], {
    });
  }

  private resetForm(): void {
    // Reset examples array to a single empty group
    const examplesArray = this.promptForm.get('examples') as FormArray;
    while (examplesArray.length > 0) {
      examplesArray.removeAt(0);
    }
    examplesArray.push(this.fb.group({ input: [''], output: [''] }));

    // Reset form to initial state
    this.promptForm.reset();
    this.formChanged = false;

    // Reset component state variables
    this.selectedTab = 'freeform';
    this.selectedPromptType = '';
    this.selectedDropdownValue = '';
    this.selectedRoleValue = '';
    this.showSuccessPopup = false;
    this.submissionSuccess = false;
    this.popupTitle = '';
    this.popupMessage = '';
    this.isPatching = false;
    this.saveLoader = false;

    // Clear prompt cache
    this.promptCache = {
      template: '',
      freeform: '',
    };

    // Reset Ask Ava modal state
    this.showAskAvaModal = false;
    this.showPromptOutput = false;
    this.isLoading = false;
    this.prompt.reset('');
    this.promptsOutputForm.reset();
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.promptForm.get(name) as FormControl;
  }

  getOutPutControl(name: string): FormControl {
    return this.promptsOutputForm.get(name) as FormControl;
  }

  // Handle file attachment for the prompt task
  handleAttachment(): void {
    // Implement file attachment logic here
  }

  // Analyze the prompt task
  analyzePrompt(): void {
    // Implement prompt analysis logic here
  }

  // Load prompt data from mock data
  loadPromptData(promptId: string): void {
    // In a real app, this would use data fetched from a service
    const prompt = MOCK_PROMPTS?.find((p) => p.id === promptId);

    if (prompt) {
      // Set form values based on the prompt data
      this.promptForm.get('name')?.setValue(prompt.title);
      this.promptForm
        .get('description')
        ?.setValue(`This is the ${prompt.title} description.`);

      // Set filter data based on the prompt properties
      this.promptForm.get('organization')?.setValue('Ascendion');
      this.promptForm
        .get('domain')
        ?.setValue(prompt.department || 'AI Research');
      this.promptForm
        .get('project')
        ?.setValue(prompt.project || 'Prompt Engineering');
      this.promptForm.get('team')?.setValue('NLP Team');

      // Set sample values for other fields
      this.promptForm
        .get('promptTask')
        ?.setValue('Generate a conversation about AI safety');
      this.promptForm.get('role')?.setValue('AI Assistant');
      this.promptForm
        .get('goal')
        ?.setValue('Help users understand AI safety concepts');
      this.promptForm
        .get('backstory')
        ?.setValue('You are an expert in AI safety and ethics');
      this.promptForm
        .get('expectedOutput')
        ?.setValue('A clear, informative conversation about AI safety');
    }
  }

  // Handle chat messages
  handleChatMessage(message: string): void {
    if (this.promptId && this.isExecuteMode) {
      this.isProcessingChat = true;

      this.chatMessages.push({
        from: 'user',
        text: message,
      });

      // Reset loading state after a delay that matches the service's response time
      setTimeout(() => {
        this.isProcessingChat = false;
      }, 1000);
    }
  }
  get examples(): FormArray {
    return this.promptForm.get('examples') as FormArray;
  }

  addExample(): void {
    this.examples.push(
      this.fb.group({
        id: [null],
        input: [''],
        output: [''],
      }),
    );

    // Ensure the newly added example is visible when in Template tab
    setTimeout(() => {
      try {
        if (this.selectedTab === 'template' && this.examplesEndRef?.nativeElement) {
          this.examplesEndRef.nativeElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }
      } catch {}
    }); 
  }

  removeExample(index: number): void {
    // Only remove if it's not the first one
    if (index > 0) {
      this.examples.removeAt(index);
    }
  }


  regenerate() {
    const previousPrompt = this.promptCache[this.selectedTab];
    if (previousPrompt) {
      this.prompt.setValue(previousPrompt);
      this.toggleAskAvaModal();
      this.onClickGenerate(previousPrompt);
    }
  }
  ngAfterViewChecked(): void {
    if (!this.accordionRef) return;

    const currentExpanded = this.accordionRef.expanded;

    if (this.previousAccordionState && !currentExpanded) {
      setTimeout(() => {
        this.clearExtraExamples();
      });
    }

    this.previousAccordionState = currentExpanded;
  }

  clearExtraExamples(): void {
    const examplesArray = this.promptForm.get('examples') as FormArray;
    while (examplesArray.length > 1) {
      examplesArray.removeAt(1);
    }
    examplesArray.at(0).get('input')?.reset();
    examplesArray.at(0).get('output')?.reset();
  }

  // Get error message for a specific form field
  getFieldError(fieldName: string): string {
    const field = this.promptForm.get(fieldName);
    // Custom label mapping
    const customLabels: Record<string, string> = {
      name: 'Prompt Name',
      description: 'Description',
      promptTask: 'Freeform Prompt',
      role: 'Role',
      goal: 'Goal',
      promptDescription: 'Description',
      backstory: 'Backstory',
      expectedOutput: 'ExpectedOutput',
      promptType: 'PromptType',
    };

    const formattedFieldName = customLabels[fieldName] || fieldName;

    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${formattedFieldName} must be at least ${field.errors['minlength'].requiredLength} characters long`;
      }
    } else if (fieldName == 'modelType' || fieldName == 'apiVersion') {
      if (field && field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
    }

    return '';
  }

isSaveDisabled(): boolean {
  if (this.isViewMode) {
    return true;
  }

  if (this.isIndividualAgent) {
    if (this.selectedTab === 'freeform') {
      return this.getControl('name').invalid || this.getControl('promptTask').invalid;
    }

    if (this.selectedTab === 'template') {
      return (
        this.getControl('name').invalid ||
        this.getControl('promptDescription').invalid ||
        this.getControl('role').invalid ||
        this.getControl('goal').invalid ||
        this.getControl('description').invalid ||
        this.getControl('backstory').invalid ||
        this.getControl('expectedOutput').invalid||
        this.getControl('promptType').invalid
      );
    }
  }

  if (this.isCollaborativeAgent) {
    return (
      this.getControl('name').invalid ||
      this.getControl('promptDescription').invalid ||
      this.getControl('role').invalid ||
      this.getControl('goal').invalid ||
      this.getControl('description').invalid ||
      this.getControl('backstory').invalid ||
      this.getControl('expectedOutput').invalid||
      this.getControl('promptType').invalid
    );
  }

  return true; // default to disabled if no mode matched
}


  // Handle success popup confirmation
  onSuccessConfirm(): void {
    this.closeSuccessPopup();
  }

  // Hide success popup and navigate if successful
  closeSuccessPopup(): void {
    this.showSuccessPopup = false;
    this.popupTitle = '';
    this.popupMessage = '';
    if (this.submissionSuccess) {
      this.router.navigate(['/libraries/prompts']);
    }
  }

  toggleAskAvaModal(show = true) {
    this.showAskAvaModal = show;
  }

  onClickGenerate(prompt = this.prompt.value) {
    // For collaborative agents, always use template mode
    // For individual agents, use tab-based logic
    const isTabFreeForm = this.isCollaborativeAgent ? false : this.selectedTab === 'freeform';
    const mode = isTabFreeForm
      ? PromptsModes.useCaseCreation
      : PromptsModes.agnetJsonCreation;
    const useCaseIdentifier = mode + USE_CASE_IDENTIFIER;
    this.isLoading = true;
    this.promptGenerateService
      .modelApi(prompt, mode, false, useCaseIdentifier)
      .subscribe({
        next: (res) => {
          if (isTabFreeForm) {
            this.promptsOutputForm.patchValue({
              promptTask: res?.response?.choices[0]?.text,
            });
          } else {
            let generatedPrompt: any = {};
            try {
              generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);
            } catch (error) {
              return;
            }
            const { ROLE, GOAL, BACKSTORY, DESCRIPTION, OUTPUT } =
              generatedPrompt;
            this.promptsOutputForm.patchValue({
              role: ROLE,
              goal: GOAL,
              backstory: BACKSTORY,
              description: DESCRIPTION,
              expectedOutput: OUTPUT,
            });
          }

          this.showPromptOutput = true;
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
        },
      });
  }

  onUse(): void {
    const outputFormValue = this.promptsOutputForm.getRawValue();
    //  Cache prompt value (if used for tabs)
    this.promptCache[this.selectedTab] = this.prompt.value || '';

    //  Save current name and description before patching
    const { name, promptDescription } = this.promptForm.getRawValue();

    //  Patch all fields from output form
    this.promptForm.patchValue({
      ...outputFormValue,
      name, // restore preserved values
      promptDescription,
    });

    // ✅ Reset child form only (does NOT touch parent)
    this.promptsOutputForm.reset();
    this.prompt.reset('');

    this.showPromptOutput = false;
    this.toggleAskAvaModal(false);
  }

  onReset() {
    this.promptsOutputForm.reset();
    this.showPromptOutput = false;
  }

  onCancle() {
    this.prompt.reset('');
    this.onReset();
    this.toggleAskAvaModal(false);
  }

  onClose() {
    this.toggleAskAvaModal(false);
    this.onCancle();
  }
  @Output() onCancelClick = new EventEmitter<void>();
  @Output() onPromptCreated = new EventEmitter<void>();



  onCancel(): void {
    this.closed.emit();
    this.resetForm(); // Add form reset
    // this.onCancelClick.emit();
    // Close dialog if opened via DialogService
    this.dialogService.close();
  }

  get actionButtonLabel(): string {
    return this.isEditMode ? 'Update' : 'Save';
  }
}

