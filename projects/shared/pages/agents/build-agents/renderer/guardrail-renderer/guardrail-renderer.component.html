<div class="guardrail-renderer-container">
  <!-- Header -->
  <div class="modal-header">
    <h2 class="modal-title">Create Guardrail</h2>
  </div>

  <!-- Content -->
  <div class="modal-content">
    <form [formGroup]="guardrailForm" *ngIf="showFormFields">
      <!-- Guardrail Name -->
      <div class="form-field">
        <ava-textbox
          [label]="'Guardrail Name'"
          [placeholder]="labels.gnPlacholder"
          [formControl]="$any(guardrailForm.get('name'))"
          [required]="true">
        </ava-textbox>
      </div>

      <!-- Guardrail Description -->
      <div class="form-field">
        <ava-textarea
          [label]="'Guardrail Description'"
          [placeholder]="labels.gdPlaceholder"
          [formControl]="$any(guardrailForm.get('description'))"
          [required]="true"
          [rows]="2">
        </ava-textarea>
      </div>

      <!-- Guardrail in Colang -->
      <div class="form-field code-field">
        <label class="field-label">Guardrail in Colang</label>
        <div class="code-editor-wrapper">
          <app-code-editor
            #colangEditor
            [title]="''"
            [language]="codeLanguagesMap['Colang']"
            [Control]="getColangControl()"
            [customCssClass]="'tools-monaco-editor'"
            [placeholder]="'Define user interactions, bot responses, and flow logic using Colang syntax...'"
            [height]="'180px'"
            (valueChange)="onColangContentChanged($event)">
          </app-code-editor>
        </div>
      </div>

      <!-- Guardrail in YML (Optional) -->
      <div class="form-field code-field">
        <label class="field-label">Guardrail in YML (Optional)</label>
        <div class="code-editor-wrapper">
          <app-code-editor
            #ymlEditor
            [title]="''"
            [language]="codeLanguagesMap['YML']"
            [Control]="getYmlControl()"
            [customCssClass]="'tools-monaco-editor'"
            [placeholder]="'Configure validation rules, input/output checks, and prompts in YAML format...'"
            [height]="'180px'">
          </app-code-editor>
        </div>
      </div>
    </form>
  </div>

  <!-- Footer -->
  <div class="modal-footer">
    <ava-button
      [label]="'Cancel'"
      variant="secondary"
      size="medium"
      (userClick)="closeModal()">
    </ava-button>
    <ava-button
      [label]="'Save Guardrail'"
      variant="primary"
      size="medium"
      (userClick)="confirmSave()"
      [disabled]="isSubmitDisabled()">
    </ava-button>
  </div>
</div>
