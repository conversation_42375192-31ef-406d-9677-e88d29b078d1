// Responsive breakpoints
$breakpoint-1440: 1440px;
$breakpoint-1920: 1920px;

.guardrail-renderer-container {
  display: flex;
  flex-direction: column;
  height: 80vh;
  max-height: 80vh;
  background: transparent;
  overflow: hidden;

  // 70% width for popups
  width: 70vw;
  max-width: 70vw;
}

.modal-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 0 0 16px 0;
  background: transparent;
  flex-shrink: 0;

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #111827;
    margin: 0;
  }
}

.modal-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  overflow-x: hidden;

  form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 0 16px 16px 0;
  }

  .form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 16px;

    // Ava components handle their own styling

    &.code-field {
      margin-bottom: 24px;

      .field-label {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 10 !important;
        margin-bottom: 8px !important;
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #374151 !important;
      }

      .code-editor-wrapper {
        border: none;
        border-radius: 0;
        overflow: hidden;
        height: 180px;
        position: relative;
        z-index: 1;

        // Remove any internal borders from code editor
        ::ng-deep .tools-monaco-editor {
          border: none;
          height: 180px !important;
          max-height: 180px !important;

          .monaco-editor {
            border: none;
            height: 180px !important;
            max-height: 180px !important;
          }
        }
      }
    }
  }
}

.modal-footer {
  padding: 20px 0 0 0;
  background: transparent;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  flex-shrink: 0;
  border-top: 1px solid #E5E7EB;
  margin-top: auto;
  flex-shrink: 0;


}

// Responsive design
@media (max-width: 768px) {
  .guardrail-renderer-container {
    max-width: 95vw;
    max-height: 90vh;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding-left: 16px;
    padding-right: 16px;
  }
}

// Custom styles for code editors
::ng-deep {
  .tools-monaco-editor {
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    overflow: hidden;

    .monaco-editor {
      border-radius: 8px;
    }
  }
}

// Ask AVA modal overlay styling for guardrails
::ng-deep app-ask-ava-wrapper {
  // Fix overlay border radius to match modal
  ava-popup {
    .popup-overlay {
      border-radius: 12px;
      overflow: hidden;
    }

    .popup-content {
      border-radius: 12px;
      overflow: hidden;
    }

    .popup-container {
      border-radius: 12px;
      overflow: hidden;
    }
  }
}
