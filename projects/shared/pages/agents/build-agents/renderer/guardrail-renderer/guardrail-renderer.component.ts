import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  ButtonComponent,
  DialogService,
  AvaTextboxComponent,
  AvaTextareaComponent,
} from '@ava/play-comp-library';
import {
  CodeEditorComponent,
  CodeLanguage,
} from '@shared/components/code-editor/code-editor.component';
import { GuardrailsService } from '@shared/services/guardrails.service';
import guardrailsLabels from '../../../../libraries/guardrails/constants/guardrails-base.json';

@Component({
  selector: 'app-guardrail-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    CodeEditorComponent,
  ],
  providers:[DialogService],
  templateUrl: './guardrail-renderer.component.html',
  styleUrls: ['./guardrail-renderer.component.scss'],
})
export class GuardrailRendererComponent implements OnInit, OnDestroy {
  @ViewChild('colangEditor') colangEditor!: CodeEditorComponent;
  @ViewChild('ymlEditor') ymlEditor!: CodeEditorComponent;
  closed: EventEmitter<any> = new EventEmitter<any>()
  closeModal(success: boolean = false) {
    this.closed.emit(success);
  }

  guardrailForm: FormGroup;
  labels: any = guardrailsLabels.labels;

  // UI state
  showFormFields = true;
  
  // Code editor state
  codeEditorState = {
    loading: false,
    error: false,
    errorMessage: null as string | null,
    processing: false,
  };

  // Define language mapping
  codeLanguagesMap: Record<string, CodeLanguage> = {
    Colang: 'plaintext',
    YML: 'yaml',
  };

  constructor(
    private fb: FormBuilder,
    private guardrailsService: GuardrailsService,
    private dialogService: DialogService,
  ) {
    this.guardrailForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      codeContent: new FormControl('', Validators.required),
      yamlContent: [''],
      organization: [''],
      domain: [''],
      project: [''],
      team: [''],
    });
  }

  ngOnInit(): void {
    // Initialize form with default values
    this.guardrailForm.patchValue({
      organization: 'Ascendion',
    });
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.guardrailForm.get(name) as FormControl;
  }

  getColangControl(): FormControl {
    return this.guardrailForm.get('codeContent') as FormControl;
  }

  getYmlControl(): FormControl {
    return this.guardrailForm.get('yamlContent') as FormControl;
  }

  getFieldError(fieldName: string): string {
    const field = this.guardrailForm.get(fieldName);
    const formattedFieldName = /^[A-Z]/.test(fieldName)
      ? fieldName
      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
    }
    return '';
  }

  isSubmitDisabled(): boolean {
    return this.guardrailForm.invalid;
  }

  onColangContentChanged(value: string): void {
    const control = this.guardrailForm.get('codeContent');
    control?.setValue(value);
    control?.markAsDirty();
    control?.markAsTouched();
  }

  showCodeEditorError(): boolean {
    const ctrl = this.guardrailForm.get('codeContent');
    const editorValue = this.colangEditor?.getValue() || '';
    const formValue = ctrl?.value || '';

    const isEmpty = !editorValue.trim() && !formValue.trim();
    const hasError = !!(ctrl?.touched && isEmpty);

    this.codeEditorState.error = hasError;
    this.codeEditorState.errorMessage = hasError
      ? 'Colang Code is required.'
      : null;

    return hasError;
  }

  confirmSave(): void {
    this.dialogService
      .confirmation({
        title: 'Save Guardrail?',
        message: 'Are you sure you want to save this guardrail?',
        confirmButtonText: 'Save',
        cancelButtonText: 'Cancel',
        confirmButtonVariant: 'primary',
        icon: 'save',
      })
      .then((result) => {
        if (result.confirmed) {
          this.onSave();
        }
      });
  }

  onSave(): void {
    if (this.guardrailForm.invalid) {
      this.guardrailForm.markAllAsTouched();
      return;
    }

    const name = this.guardrailForm.get('name')?.value;
    const description = this.guardrailForm.get('description')?.value;
    const content = this.guardrailForm.get('codeContent')?.value;
    const organization = this.guardrailForm.get('organization')?.value || 'Ascendion';
    const configKey = this.guardrailForm.get('configKey')?.value;
    const yamlContent = this.guardrailForm.get('yamlContent')?.value;
    const chatBot = this.guardrailForm.get('chatBot')?.value;

    const payload = {
      name,
      description,
      content,
      organization,
      configKey,
      yamlContent,
      chatBot,
    };

    // Show loading dialog
    this.dialogService.loading({
      title: 'Creating Guardrail...',
      message: 'Please wait while we create the guardrail.',
      showProgress: false,
      showCancelButton: false,
    });

    this.guardrailsService.addGuardrail(payload).subscribe({
      next: (response: any) => {
        this.dialogService.close(); // Close loading dialog

        // Check if response contains an error even though status is 200
        if (response?.error || response?.message?.includes('duplicate key value')) {
          const errorMessage =
            response.error?.message ||
            response.message ||
            'Failed to create guardrail. Please try again.';
          this.dialogService
            .error({
              title: 'Create Failed',
              message: errorMessage,
              showRetryButton: true,
              retryButtonText: 'Retry',
            })
            .then((result) => {
              if (result.action === 'retry') {
                this.onSave();
              }
            });
          return;
        }

        // If no error, treat it as success
        this.dialogService
          .success({
            title: 'Guardrail Created',
            message: `Guardrail "${payload.name}" has been successfully created.`,
          })
          .then(() => {
            this.closeModal(true);
          });
      },
      error: (err) => {
        this.dialogService.close(); // Close loading dialog

        const errorMessage =
          err?.error?.message ||
          err?.message ||
          'Failed to create guardrail. Please try again.';
        this.dialogService
          .error({
            title: 'Create Failed',
            message: errorMessage,
            showRetryButton: true,
            retryButtonText: 'Retry',
          })
          .then((result) => {
            if (result.action === 'retry') {
              this.onSave();
            }
          });
      },
    });
  }

  // Ask AVA functionality
  toggleAskAvaModal(): void {
    // Placeholder for Ask AVA functionality
    console.log('Ask AVA clicked for guardrails');
  }
}
