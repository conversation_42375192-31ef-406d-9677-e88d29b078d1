<div class="knowledge__base--container">
  <h3>Knowledge Base Details</h3>

  <div class="form__container">
    <form [formGroup]="knowledgeBaseForm">
      <div class="form-fields">
        <div class="input__field--wrapper">
          <label for="name" class="filter-label required">KnowledgeBase Name</label>
          <ava-textbox
            class="input-field"
            formControlName="name"
            id="name"
            name="name"
            placeholder="Enter Name"
            [fullWidth]="true"
            size="md"
          >
          </ava-textbox>
        </div>
      </div>
  
      <div class="form-fields">
        <div class="input__field--wrapper">
          <label for="description" class="filter-label required">Description</label>
            <ava-textarea
              id="description"
              name="description"
              size="sm"
              [rows]="2"
              placeholder="Enter knowledgeBase description"
              formControlName="description"
            >
            </ava-textarea>
        </div>
      </div>
      <div class="form-fields">
        <div class="input__field--wrapper">
          <label>Select Retriever</label>
          <div class="retriever-options">
            <ava-button
              *ngFor="let retriever of retrieverOptions"
              [label]="retriever"
              size="small"
              [variant]="selectedRetriever === retriever ? 'primary' : 'secondary'"
              (userClick)="selectRetriever(retriever)"
            >
            </ava-button>
          </div>
        </div>
      </div>
  
      <div class="section" *ngIf="selectedRetriever === 'Default'">
        <label class="section-title">Split Size</label>
        <div class="split-size-container">
          <ava-slider
            [value]="splitSize"
            [min]="100"
            [max]="20000"
            [step]="1"
            [showTooltip]="false"
            (valueChange)="splitSizeChange($event)"
            formControlName="splitSize"
          >
          </ava-slider>
          <div class="value-box">
            <ava-textbox
              type="number"
              (change)="onSliderInputChange($event, 'splitSize')"
              formControlName="splitSize"
              [fullWidth]="true"

            ></ava-textbox>
          </div>
        </div>
      </div>
  
      <!-- Parent Doc Split Size Configuration -->
      <div
        class="section"
        *ngIf="selectedRetriever === 'Parent Doc'"
      >
        <div class="split-section">
          <label class="section-title">Parent Split Size</label>
          <div class="split-size-container">
            <ava-slider
              [value]="parentSplitSize"
              [min]="100"
              [max]="20000"
              [step]="1"
              [showTooltip]="false"
              (valueChange)="onParentSplitSizeChange($event)"
              formControlName="parentSplitSize"
            >
            </ava-slider>
            <div class="value-box">
              <ava-textbox
                type="number"
                (change)="onSliderInputChange($event, 'parentSplitSize')"
                formControlName="parentSplitSize"
              ></ava-textbox>
            </div>
          </div>
        </div>
        <div class="split-section">
          <label class="section-title">Child Split Size</label>
          <div class="split-size-container">
            <ava-slider
              [value]="childSplitSize"
              [min]="100"
              [max]="20000"
              [step]="1"
              [showTooltip]="false"
              (valueChange)="onChildSplitSizeChange($event)"
              formControlName="childSplitSize"
            >
            </ava-slider>
            <div class="value-box">
              <ava-textbox
                type="number"
                (change)="onSliderInputChange($event, 'childSplitSize')"
                formControlName="childSplitSize"
              ></ava-textbox>
            </div>
          </div>
          <div
            *ngIf="
              selectedRetriever === 'Parent Doc' &&
              parentSplitSize <= childSplitSize
            "
            class="field-error"
          >
            Parent split size must be greater than child split size.
          </div>
        </div>
      </div>

      <div class="section">
        <div class="input__field--wrapper">
          <label for="embeddingModel" class="filter-label required">Choose Embedding Model</label>
          <ava-dropdown
            class="dropdown-medium"
            dropdownTitle="Select Embedding Model"
            id="embeddingModel"
            [options]="embeddingModelOptions"
            formControlName="embeddingModel"
          ></ava-dropdown>
        </div>
      </div>
  
      <div class="section">
        <div class="input__field--wrapper">
          <label for="uploadType" class="filter-label required">Upload Type</label>
          <ava-dropdown
            class="dropdown-medium"
            id="uploadType"
            formControlName="uploadType"
            [options]="uploadOptions"
            [selectedValue]="uploadPlaceholder"
            (valueChange)="onUploadTypeChange($event)"
            variant="primary"
            size="md"
          >
          </ava-dropdown>
        </div>
      </div>
  
      <ng-container *ngIf="selectedUploadType === 'upload-files'">
        <div class="ava-file-upload-wrapper">
          <ava-file-upload
            (filesListChanged)="filesListChanged($event)"
            [allowedFormats]="allowedFormats"
            [showUploadButton]="showUploadButton"
            [componentTitle]="componentTitle"
            [showDialogCloseIcon]="showDialogCloseIcon"
            [maxFileSize]="maxFileSize"
            uploaderId="light-uploader"
            [preview]="true"
          >
          </ava-file-upload>
        </div>
        <div *ngIf="hasZeroKbFile" class="field-error">
          Cannot upload a file with 0KB size.
        </div>
      </ng-container>
  
      <div
        *ngIf="fileUploadRequired && uploadedFiles.length === 0"
        class="field-error"
      >
        File upload is required
      </div>
  
      <div *ngIf="selectedUploadType" class="upload-fields-grid">
        <!-- Azure Blob -->
        <ng-container *ngIf="selectedUploadType === 'azure-blob'">
          <h3>Config Source : Azure Blob</h3>
          <div class="input__field--wrapper">
            <label for="containerName" class="filter-label required">Container Name</label>
            <ava-textbox
              id="containerName"
              placeholder="Enter container name"
              formControlName="containerName"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="accountName" class="filter-label required">Account Name</label>
            <ava-textbox
              id="accountName"
              placeholder="Enter account name"
              formControlName="accountName"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="accountKey" class="filter-label required">Account Key</label>
            <ava-textbox
              id="containerName"
              placeholder="Enter account key"
              formControlName="accountKey"
            ></ava-textbox>
          </div>   
        </ng-container>
  
        <!-- GitHub -->
        <ng-container *ngIf="selectedUploadType === 'github'">
          <h3>Config Source : GitHub</h3>
          <div class="input__field--wrapper">
            <label for="githubKey" class="filter-label required">GitHub Key</label>
            <ava-textbox
              id="githubKey"
              formControlName="githubKey"
              placeholder="Enter github key"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="githubAccount" class="filter-label required">GitHub Account</label>
            <ava-textbox
              id="githubAccount"
              placeholder="Enter github account"
              formControlName="githubAccount"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="githubRepo" class="filter-label required">GitHub Repository</label>
            <ava-textbox
              id="githubRepo"
              formControlName="githubRepo"
              placeholder="Enter github repository"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="githubBranch" class="filter-label required">GitHub Branch</label>
            <ava-textbox
              id="githubBranch"
              placeholder="Enter github branch"
              formControlName="githubBranch"
            ></ava-textbox>
          </div>
        </ng-container>
  
        <!-- Share Point -->
        <ng-container *ngIf="selectedUploadType === 'share-point'">
          <h3>Config Source : Share Point</h3>
          <div class="input__field--wrapper">
            <label for="clientId" class="filter-label required">Client ID</label>
            <ava-textbox
              id="clientId"
              placeholder="Enter client ID"
              formControlName="clientId"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="clientSecret" class="filter-label required">Client Secret</label>
            <ava-textbox
              id="clientSecret"
              placeholder="Enter client secret"
              formControlName="clientSecret"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="tenantId" class="filter-label required">Tenant ID</label>
            <ava-textbox
              id="tenantId"
              placeholder="Enter tenant ID"
              formControlName="tenantId"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="sharepointSiteName" class="filter-label required">SharePoint Site Name</label>
            <ava-textbox
              id="sharepointSiteName"
              placeholder="Enter sharepoint site name"
              formControlName="sharepointSiteName"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="sharepointFolderPath" class="filter-label required">SharePoint Folder Path</label>
            <ava-textbox
              id="sharepointFolderPath"
              placeholder="Enter sharepoint folder path"
              formControlName="sharepointFolderPath"
            ></ava-textbox>
          </div>
        </ng-container>
  
        <!-- Confluence Wiki -->
        <ng-container *ngIf="selectedUploadType === 'confluence-wiki'">
          <h3>Config Source : Confluence Wiki</h3>
          <div class="input__field--wrapper">
            <label for="email" class="filter-label required">Email ID</label>
            <ava-textbox
              id="email"
              type="email"
              placeholder="Enter email ID"
              formControlName="email"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="clientId" class="filter-label required">Client ID</label>
            <ava-textbox
              id="clientId"
              placeholder="Enter client ID"
              formControlName="clientId"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="clientSecret" class="filter-label required">Client Secret</label>
            <ava-textbox
              id="clientSecret"
              placeholder="Enter client secret"
              formControlName="clientSecret"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="apiToken" class="filter-label required">API Token</label>
            <ava-textbox
              id="apiToken"
              placeholder="Enter API token"
              formControlName="apiToken"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="baseUrl" class="filter-label required">Base URL</label>
            <ava-textbox
              id="baseUrl"
              placeholder="Enter base URL"
              formControlName="baseUrl"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="spaceKey" class="filter-label required">Space Key</label>
            <ava-textbox
              id="spaceKey"
              placeholder="Enter space key"
              formControlName="spaceKey"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="overlap" class="filter-label required">Overlap</label>
            <ava-textbox
              id="overlap"
              type="number"
              placeholder="Enter overlap"
              formControlName="overlap"
            ></ava-textbox>
          </div>
        </ng-container>
  
        <!-- Database -->
        <ng-container *ngIf="selectedUploadType === 'database'">
          <h3>Config Source : Database</h3>
          <div class="input__field--wrapper">
            <label for="scheme" class="filter-label required">Select Scheme</label>
            <div class="schema-dropdown">
              <ava-dropdown
                id="scheme"
                dropdownTitle="Select Scheme"
                [options]="schemeOptions"
                formControlName="scheme"
              >
              </ava-dropdown>
            </div>
          </div>
          <div class="input__field--wrapper">
            <label for="host" class="filter-label required">DB Host</label>
            <ava-textbox
              id="host"
              placeholder="Enter host"
              formControlName="host"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="port" class="filter-label required">DB Port</label>
            <ava-textbox
              id="port"
              placeholder="Enter port"
              formControlName="port"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="user" class="filter-label required">DB User</label>
            <ava-textbox
              id="user"
              placeholder="Enter user"
              formControlName="user"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="password" class="filter-label required">DB Password</label>
            <ava-textbox
              id="password"
              placeholder="Enter password"
              formControlName="password"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="dbname" class="filter-label required">DB Name</label>
            <ava-textbox
              id="dbname"
              placeholder="Enter db name"
              formControlName="dbname"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="query" class="filter-label required">DB Query</label>
            <ava-textbox
              id="query"
              placeholder="Enter query"
              formControlName="query"
            ></ava-textbox>
          </div>
          <div class="input__field--wrapper">
            <label for="overlap" class="filter-label required">Overlap</label>
            <ava-textbox
              id="overlap"
              type="number"
              placeholder="Enter overlap"
              formControlName="overlap"
            ></ava-textbox>
          </div>
        </ng-container>
      </div>
    </form>
  </div>

  <div class="button-container">
    <ava-button
      label="Cancel"
      variant="secondary"
      (userClick)="onCancel()"
      size="small"
      [disabled]="isDisabled"
    >
    </ava-button>
    <ava-button
      [disabled]="isSubmitDisabled() || isDisabled"
      label="Save"
      variant="primary"
      id="save-button"
      (userClick)="onSave()"
      size="small"
    >
    </ava-button>
  </div>
</div>