.knowledge__base--container {
  width: 62vw;  
}

.form__container {
  max-height: 74vh;
  overflow-y: auto;
  overflow-x: clip;
  margin: 0 0 1rem;
  padding-right: 0.5rem;
}

form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin: 1rem 0 0 0;
}

.input__field--wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px;
  color: #374151;

  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 8px;
  }
}

.retriever-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

// Split size slider styling
.split-size-container {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;

  ava-slider {
    flex: 1;
    max-width: 50%;
  }

  .value-box {
    width: 70px;
    height: 49px;
    border: 1px solid #4b507f;
    border-radius: 8px;
    font-size: 16px;
    font-family: Inter, sans-serif;
    text-align: center;
    line-height: 30px;
    color: #4b507f;
    background-color: white;
    padding: 0;

    ::ng-deep {
    input[type="number"]::-webkit-inner-spin-button,
    input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }
  }
}

.dropdown-medium {
  width: 250px;
  display: inline-block;
}

.upload-fields-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 40px;


  h3 {
    grid-column: 1 / -1;
    margin: 0 0 16px;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
  }
}

.field-error {
  color: #e53935;
  font-size: 13px;
  margin-top: 4px;
}

.button-container {
  display: flex;
  justify-content: center;
  gap: 8px;
}