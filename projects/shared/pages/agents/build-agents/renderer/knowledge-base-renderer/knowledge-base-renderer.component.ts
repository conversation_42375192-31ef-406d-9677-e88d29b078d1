import { Component, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent, DialogService, DropdownComponent, FileUploadComponent, SliderComponent, TableComponent } from '@ava/play-comp-library';
import { KnowledgeBaseService } from '@shared/services/knowledge-base.service';

@Component({
  selector: 'app-model-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    DropdownComponent,
    AvaTextareaComponent,
    ButtonComponent,
    SliderComponent,
    FileUploadComponent
  ],
  providers: [DialogService],
  templateUrl: './knowledge-base-renderer.component.html',
  styleUrls: ['./knowledge-base-renderer.component.scss'],
})
export class KnowledgeBaseRendererComponent implements OnInit {
  knowledgeBaseForm!: FormGroup;
  retrieverOptions: string[] = ['Default', 'Parent Doc'];
  selectedRetriever = 'Default';
  uploadOptions: any[] = [];
  hasZeroKbFile = false;
  showUploadButton = true;
  
  // Split size fields
  splitSize: number = 5000;
  parentSplitSize: number = 5000;
  childSplitSize: number = 2000;

  uploadPlaceholder = 'Upload Files';
  selectedUploadType = 'upload-files';
  uploadedFiles: File[] = [];

  private selectedControls: Record<string, string[]> = {
    'upload-files': [],
    'Azure Blob': [ 'containerName', 'accountName', 'accountKey' ],
    'GitHub': ['githubKey', 'githubAccount', 'githubRepo', 'githubBranch' ],
    'Share Point': [ 'clientId', 'clientSecret', 'tenantId', 'sharepointSiteName', 'sharepointFolderPath' ],
    'Confluence Wiki': [ 'email', 'clientId', 'clientSecret', 'apiToken', 'baseUrl', 'spaceKey', 'overlap' ],
    'Database': [ 'scheme', 'host', 'port', 'user', 'password', 'dbname', 'query', 'overlap' ]
  };

  // API path suffix map based on upload source
  private pathSuffix: Record<string, string> = {
    'Azure Blob': '/blob',
    'GitHub': '/github',
    'Share Point': '/sharepoint',
    'Confluence Wiki': '/confluence',
    'Database': '/database',
  };

  allowedFormats: string[] = [ 'pdf', 'txt', 'doc', 'docx', 'ppt', 'pptx', 'html', 'xls', 'xlsx' ];
  componentTitle: string = 'Upload File Here';
  showDialogCloseIcon = false;
  maxFileSize = 15 * 1024 * 1024;
  fileUploadRequired = false;
  embeddingModelOptions: { name: string; value: string }[] = [];
  schemeOptions: { name: string; value: string }[] = [];
  isDisabled: boolean = false;

  closed: EventEmitter<any> = new EventEmitter<any>();

  // Constructor injecting services and initializing form group
  constructor(
    private fb: FormBuilder,
    private knowledgeBaseService: KnowledgeBaseService,
    private dialogService: DialogService,
  ) {
    // Define form controls and validators
    this.knowledgeBaseForm = this.fb.group({
      name: ['', [Validators.required]],
      description: ['', Validators.required],
      retriever: ['Default'],
      splitSize: [5000],
      parentSplitSize: [5000],
      childSplitSize: [2000],
      embeddingModel: [null, Validators.required],
      uploadType: ['', Validators.required],
      containerName: [''],
      accountName: [''],
      accountKey: [''],
      githubKey: [''],
      githubAccount: [''],
      githubRepo: [''],
      githubBranch: [''],
      tenantId: [''],
      sharepointSiteName: [''],
      sharepointFolderPath: [''],
      email: [''],
      clientId: [''],
      clientSecret: [''],
      apiToken: [''],
      baseUrl: [''],
      spaceKey: [''],
      overlap: [''],
      scheme: [''],
      host: [''],
      port: [''],
      user: [''],
      password: [''],
      dbname: [''],
      query: [''],
    });
  }

  ngOnInit(): void {
    this.loadEmbeddingModelOptions();
    this.loadUploadOptionsAndSetDefault();
    this.loadSchemeDropdownOptions();
  }

  // Fetch available embedding models
  loadEmbeddingModelOptions(): void {
    this.knowledgeBaseService
    .getEmbeddingModelOptions()
    .subscribe({
      next: (options) => {
        this.embeddingModelOptions = options.map((opt: any) => ({
          name: opt.modelDeploymentName,
          value: String(opt.id),
        }));
      },
      error: (err) => {
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load embedding models. Please refresh the page.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.loadEmbeddingModelOptions();
          }
        });
      }
    });
  }

  // Load upload options and set first one as default
  loadUploadOptionsAndSetDefault(): void {
    this.knowledgeBaseService.getUploadDropdowns().subscribe({
      next: (res) => {
        if (res?.value) {
          this.uploadOptions = this.getUploadTypeOptions(res.value);

          // Safely assign default value and apply
          const firstOption = this.uploadOptions[0];
          if (firstOption && firstOption.value) {
            const defaultValue = firstOption.value;

            // Set form control
            this.knowledgeBaseForm.get('uploadType')?.setValue(defaultValue);

            // Set component state
            this.selectedUploadType = defaultValue;
          }
        }
      },
      error: (err) => {
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load upload options. Please refresh the page.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.loadUploadOptionsAndSetDefault();
          }
        });
      },
    });
  }

    // Scheme Dropdown values
  loadSchemeDropdownOptions(): void {
    this.knowledgeBaseService.getSchemeDropdowns().subscribe({
      next: (response) => {
        if (response && response.value) {
          try {
          const parsedValue = JSON.parse(response.value); // parse string to object
            this.schemeOptions = Object.keys(parsedValue).map((key) => ({
              name: key,
              value: parsedValue[key]
            }));
          } catch (error) {
            console.error('Failed to parse scheme options:', error);
            this.schemeOptions = [];
          }
        } else {
          this.schemeOptions = [];
        }
      },
      error: (err) => {
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load scheme options. Please refresh the page.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.loadSchemeDropdownOptions();
          }
        });
      }
    });
  }

  // Converts string labels into slug format
  getUploadTypeOptions(rawValue: string): { name: string; value: string }[] {
    const parsed = JSON.parse(rawValue);
    return Object.keys(parsed).map((label) => ({
      name: label,
      value: this.toSlug(label),
    }));
  }

  // Utility for slug formatting
  toSlug(label: string): string {
    return label.toLowerCase().replace(/\s+/g, '-');
  }

  selectRetriever(retriever: string): void {
    this.selectedRetriever = retriever;
    this.knowledgeBaseForm.get('retriever')?.setValue(retriever);
  }

  // splitSize input setter
  splitSizeChange(event: any) {
    this.splitSize = event;
    this.knowledgeBaseForm
      .get('splitSize')
      ?.setValue(this.splitSize);
  }

  onParentSplitSizeChange(event: any): void {
    this.parentSplitSize = event;
    this.knowledgeBaseForm
      .get('parentSplitSize')
      ?.setValue(this.parentSplitSize);
  }

  onChildSplitSizeChange(event: any): void {
    this.childSplitSize = event;
    this.knowledgeBaseForm.get('childSplitSize')?.setValue(this.childSplitSize);
  }

  onSliderInputChange(event: any, controlName: string): void {
    let inputValue: number;

    // Handle both native event and direct value
    if (event?.target) {
      inputValue = (event.target as HTMLInputElement).valueAsNumber;
    } else if (typeof event === 'number') {
      inputValue = event;
    } else {
      inputValue = Number(event);
    }

    // Cap to max value (you can also parametrize this if needed)
    const cappedValue = Math.min(inputValue, 20000);

    // Update local variable based on controlName
    if (controlName === 'parentSplitSize') {
      this.parentSplitSize = cappedValue;
    } else if (controlName === 'childSplitSize') {
      this.childSplitSize = cappedValue;
    } else if (controlName === 'splitSize') {
      this.splitSize = cappedValue;
    }
    // Update the corresponding form control
    this.knowledgeBaseForm.get(controlName)?.setValue(cappedValue);
  }

  // Triggered when upload type changes
  onUploadTypeChange(event: any): void {
    const selected = event?.selectedOptions?.[0]?.value;
    this.selectedUploadType = selected || '';

    if (this.selectedUploadType !== 'upload-files') {
      this.uploadedFiles = [];
    }
    // Clear all old validators AND values
    Object.values(this.selectedControls)
      .flat()
      .forEach((ctrlName) => {
        const ctrl = this.getControl(ctrlName);
        if (ctrl) {
          ctrl.clearValidators();
          ctrl.setValue(null); // or ''
          ctrl.markAsPristine();
          ctrl.markAsUntouched();
        }
      });


    // Apply required validators to new controls
    this.selectedControls[this.selectedUploadType]?.forEach((ctrl) => {
      this.getControl(ctrl)?.setValidators(Validators.required);
    });

    // Reapply validator to uploadType itself
    this.getControl('uploadType')?.setValidators(Validators.required);

    // Mark uploadType as interacted
    this.getControl('uploadType')?.markAsTouched();
    this.getControl('uploadType')?.markAsDirty();

    // Recalculate form status
    this.knowledgeBaseForm.updateValueAndValidity();
  }

   // Get a typed form control
  getControl(name: string): FormControl {
    return this.knowledgeBaseForm.get(name) as FormControl;
  }

  // Update file list after change
  filesListChanged(file: File[]) {
    this.uploadedFiles = [...file];
    this.hasZeroKbFile = this.uploadedFiles.some(file => file.size === 0);
  }

  // Disable submit based on form or file state
  isSubmitDisabled(): boolean {
    const isFormInvalid = this.knowledgeBaseForm.invalid;
    const isUploadTypeFile = this.selectedUploadType === 'upload-files';
    const isFileEmpty = this.uploadedFiles.length === 0;
    const isParentDoc = this.selectedRetriever === 'Parent Doc';

    const splitSize = Number(this.knowledgeBaseForm.get('splitSize')?.value);
    const parentSplitSize = Number(this.knowledgeBaseForm.get('parentSplitSize')?.value);
    const childSplitSize = Number(this.knowledgeBaseForm.get('childSplitSize')?.value);

    //  Normal condition: splitSize must be at least 100
    const isNormalSplitSizeInvalid =
      !isParentDoc && splitSize < 100;

    //  Parent-child condition:
    // 1. parent must be > child
    // 2. both must be >= 100
    const isParentChildInvalid =
      isParentDoc &&
      (parentSplitSize <= childSplitSize ||
        parentSplitSize < 100 ||
        childSplitSize < 100);

    return (
      isFormInvalid ||
      (isUploadTypeFile && isFileEmpty) ||
      isParentChildInvalid ||
      isNormalSplitSizeInvalid ||
      this.hasZeroKbFile
    );
  }

  // Submit form handler
  onSave(): void {
     // Remove popup state resets
  this.fileUploadRequired = false;

  // Basic form validation
  if (this.knowledgeBaseForm.invalid && this.uploadedFiles.length === 0) {
    this.knowledgeBaseForm.markAllAsTouched();
    this.fileUploadRequired = true;
    return;
  }

  const type = this.knowledgeBaseForm.value.uploadType;
  const suffix = this.pathSuffix[type] || '';
  const retrieverType =
    this.selectedRetriever === 'Parent Doc'
      ? 'parent_doc_retriever'
      : 'normal';

  const knowledgeBase = this.getControl('name').value;
  const description = this.getControl('description').value;
  const splitSize = this.getControl('splitSize').value;
  const parentSplitSize = this.getControl('parentSplitSize')?.value;
  const childSplitSize = this.getControl('childSplitSize')?.value;

  // Get selected model ID
  const selectedModelValue = this.knowledgeBaseForm.value['embeddingModel'];
  const selectedModel = this.embeddingModelOptions.find(
    (opt) =>
      opt.name === selectedModelValue || opt.value === selectedModelValue,
  );
  const selectedModelId = selectedModel ? selectedModel.value : null;

  // Construct base payload
  const payload: Record<string, any> = {};

  // Full payload during create
  payload['knowledgeBase'] = knowledgeBase;
  payload['description'] = description;
  payload['model-ref'] = selectedModelId;
  payload['type'] = retrieverType;

  if (retrieverType === 'parent_doc_retriever') {
    payload['parentSplitSize'] = parentSplitSize;
    payload['splitSize'] = childSplitSize;
  } else {
    payload['splitSize'] = splitSize;
  }

  // Add selected controls for non-file uploads to payload
  const isFileUpload = type === 'upload-files' || type === 'Upload Files';
  this.isDisabled = true;
 
  // Show loading dialog
  this.dialogService.loading({
    title: 'Creating Knowledge Base...',
    message: 'Please wait while we process your request.',
    showProgress: false,
    showCancelButton: false
  });

  // Prepare data for submission
  let submissionData: any;
  
  if (isFileUpload) {
    if (!this.uploadedFiles || this.uploadedFiles.length === 0) return;
    
    submissionData = new FormData();
    this.uploadedFiles.forEach((file: File) => {
      submissionData.append('files', file);
    });
  } else {
    submissionData = {};
    this.selectedControls[type]?.forEach((key) => {
      const value = this.getControl(key)?.value;
      submissionData[key] = value;
    });
  }

  // Single API call with unified error handling
  this.knowledgeBaseService
    .submitUpload(submissionData, payload, suffix)
    .subscribe({
      next: (info) => {
        this.dialogService.close(); // Close loading dialog
        this.dialogService.success({
          title: 'Success!',
          message: info?.info?.message || info.message || 'Knowledge Base processed successfully.'
        }).then(() => {
          this.isDisabled = false;
          this.closed.emit(true);
        });
      },
      error: (err) => {
        this.dialogService.close(); // Close loading dialog
        this.dialogService.error({
          title: 'Operation Failed',
          message: err?.error?.data?.code || err?.error?.message || err.message || 'Knowledge Base creation or update failed. Please try again.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          this.isDisabled = false;
          if (result.action === 'retry') {
            this.onSave();
          }
        });
      },
    });
  }

  onCancel() {
    this.knowledgeBaseForm.reset();
    this.closed.emit(false);
  }
} 
