<div class="model__container">
  <h3>Model Desription</h3>
  <div class="form__container">
    <form [formGroup]="modelForm">
      <div class="form-fields">
        <div class="input__field--wrapper">
          <label for="modelDeploymentName" class="filter-label required">Model Name</label>
          <ava-textbox
            class="input-field"
            formControlName="modelDeploymentName"
            id="modelDeploymentName"
            name="modelDeploymentName"
            placeholder="Enter Realm Name"
            [fullWidth]="true"
            size="md"
          >
          </ava-textbox>
        </div>
      </div>  
      <div class="form-fields">
        <div class="input__field--wrapper">
          <label for="modelDescription" class="filter-label required">Model Description</label>
            <ava-textarea
              id="modelDescription"
              name="modelDescription"
              size="sm"
              [rows]="2"
              placeholder="Enter Description"
              formControlName="modelDescription"
            >
            </ava-textarea>
        </div>
      </div>  
      <div class="form-fields">
        <div class="input__field--wrapper">
          <label class="filter-label required">Choose AI Engine</label>
          <ava-dropdown
            [dropdownTitle]="'Select Organization'"
            [options]="aiEngines"
            [selectedValue]="selectedAiEngineId"
            [disabled]="false"
            (selectionChange)="onAiEngineSelected($event)"
            [search]="true"
            [enableSearch]="true"
          ></ava-dropdown>
        </div>
        <div class="input__field--wrapper">
          <label class="filter-label required">Choose AI Engine Type</label>
          <ava-dropdown
            [dropdownTitle]="'Select Organization'"
            [options]="modelTypeList"
            [selectedValue]="selectedModelTypeId"
            [disabled]="false"
            (selectionChange)="onModelTypeSelected($event)"
            [search]="true"
            [enableSearch]="true"
          ></ava-dropdown>
        </div>
        <div class="input__field--wrapper">
          <label class="filter-label required">Choose Model</label>
          <ava-dropdown
            [dropdownTitle]="'Select Organization'"
            [options]="modelNames"
            [selectedValue]="selectedModelId"
            [disabled]="false"
            (selectionChange)="onModelSelected($event)"
            [search]="true"
            [enableSearch]="true"
          ></ava-dropdown>
        </div>
      </div>
    
      <h3 class="config-title">Model Configuration</h3>
  
      <div
        class="parameters-container"
        *ngIf="areSelectionsComplete()"
      >
        <div class="parameter-form">
          <!-- Azure OpenAI Fields -->
          <div
            *ngIf="inputVisibility.AzureOpenAI && selectedModelTypeId"
            class="param-row"
          >
            <div class="param-field">
              <label for="baseurl" class="filter-label required">base URL</label>
              <ava-textbox
                id="baseurl"
                placeholder="Enter base URL"
                formControlName="baseurl"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="llmDeploymentName" class="filter-label required">LLM deployment name</label>
              <ava-textbox
                id="llmDeploymentName"
                placeholder="Enter LLM deployment name"
                [type]="'text'"
                formControlName="llmDeploymentName"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="apiKey" class="filter-label required">API key</label>
              <ava-textbox
                [id]="'apiKey'"
                placeholder="Enter API key"
                formControlName="apiKey"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="apiVersion" class="filter-label required">API Version</label>
              <ava-dropdown
                dropdownTitle="Select API Version"
                [options]="apiVersionOptions"
                formControlName="apiVersion"
                [selectedValue]="modelForm.get('apiVersion')?.value"
              >
              </ava-dropdown>
            </div>
          </div>
          <!-- Amazon Bedrock Fields -->
          <div
            *ngIf="
              inputVisibility.AmazonBedrock && selectedModelTypeId
            "
            class="param-row"
          >
            <div class="param-field">
              <label for="awsAccessKey" class="filter-label required">AWS Access key</label>
              <ava-textbox
                [id]="'awsAccessKey'"
                placeholder="Enter AWS Access key"
                [type]="'text'"
                formControlName="awsAccessKey"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="awsSecretKey" class="filter-label required">AWS Secret Key</label>
              <ava-textbox
                [id]="'awsSecretKey'"
                placeholder="Enter AWS Secret key"
                formControlName="awsSecretKey"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="awsRegion" class="filter-label required">AWS Region</label>
              <ava-textbox
                [id]="'awsRegion'"
                placeholder="Enter AWS region"
                [type]="'text'"
                formControlName="awsRegion"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="bedrockModelId" class="filter-label required">Bedrock Model Id</label>
              <ava-textbox
                [id]="'bedrockModelId'"
                placeholder="Enter Bedrock model ID"
                [type]="'text'"
                formControlName="bedrockModelId"
              >
              </ava-textbox>
            </div>
          </div>
          <!-- Google AI Fields -->
          <div
            *ngIf="inputVisibility.GoogleAI && selectedModelTypeId"
            class="param-row"
          >
            <div class="param-field">
              <label for="bedrockModelId" class="filter-label required">GCP Project Id</label>
              <ava-textbox
                [id]="'gcpProjectId'"
                placeholder="Enter GCP project ID"
                [type]="'text'"
                formControlName="gcpProjectId"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="gcpLocation" class="filter-label required">GCP Location</label>
              <ava-textbox
                [id]="'gcpLocation'"
                placeholder="Enter GCP location"
                [type]="'text'"
                formControlName="gcpLocation"
              >
              </ava-textbox>
            </div>
            <div
              class="param-field"
              *ngIf="
                selectedAiEngineId === 'GoogleAI' &&
                selectedModelTypeId === 'Embedding'
              "
            >
              <label for="vertexAIEndpoint" class="filter-label required">VertexAI Endpoint</label>
              <ava-textbox
                [id]="'vertexAIEndpoint'"
                placeholder="Enter VertexAI endpoint"
                [type]="'url'"
                formControlName="vertexAIEndpoint"
              >
              </ava-textbox>
            </div>
          </div>
          <!-- DaOpenSource AI Fields -->
          <div
            *ngIf="
              inputVisibility.DaOpenSourceAI && selectedModelTypeId
            "
            class="param-row"
          >
            <div class="param-field">
              <label for="serviceUrl" class="filter-label required">Service URL</label>
              <ava-textbox
                [id]="'serviceUrl'"
                placeholder="Enter service URL"
                [type]="'url'"
                formControlName="serviceUrl"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="apiKeyEncoded" class="filter-label required">API Key Encoded</label>
              <ava-textbox
                [id]="'apiKeyEncoded'"
                placeholder="Enter encoded API key"
                formControlName="apiKeyEncoded"
              >
              </ava-textbox>
            </div>
            <div class="param-field">
              <label for="headerName" class="filter-label required">Header Name</label>
              <ava-textbox
                [id]="'headerName'"
                placeholder="Enter Header Name"
                formControlName="headerName"
              >
              </ava-textbox>
            </div>
          </div>
          <!-- BNY Fields -->
          <div
            *ngIf="inputVisibility.BNY && selectedModelTypeId"
            class="param-row"
          >
            <!-- Add BNY specific fields here when needed -->
          </div>
        </div>
      </div>
    </form>
  </div>

  <div class="button-container">
    <ava-button
      label="Cancel"
      variant="secondary"
      (userClick)="onCancel()"
      size="small"
      [disabled]="isDisabled"
    >
    </ava-button>
    <ava-button
      [disabled]="!modelForm.valid || isDisabled"
      label="Save"
      variant="primary"
      id="save-button"
      (userClick)="onSave()"
      size="small"
    >
    </ava-button>
  </div>
</div>