.model__container {
  width: 70vw;
}

.form__container {
  max-height: 74vh;
  overflow-y: auto;
  overflow-x: clip;
  margin: 0 0 1rem;
  padding-right: 0.5rem;
}

.form-fields {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin: 1rem 0 0 0;
}

.input__field--wrapper {
  flex: 1;
}

.required::after {
  content: " *";
  color: red;
  font-weight: bold;
}

.parameter-form {
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
  border-radius: 24px;
}

.param-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
}

.param-field {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 100%;
}

.config-title {
  margin: 2rem 0 0 0;
}

.button-container {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin: 2rem 0 0 0;
}