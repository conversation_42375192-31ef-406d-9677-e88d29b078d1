import {  ChangeDetectorRef, Component, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, ValidationErrors, Validators } from '@angular/forms';
import { AvaTextareaComponent, AvaTextboxComponent, ButtonComponent, DialogService, DropdownComponent, DropdownOption } from '@ava/play-comp-library';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { ModelService } from '@shared/services/model.service';


@Component({
  selector: 'app-model-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextboxComponent,
    DropdownComponent,
    AvaTextareaComponent,
    ButtonComponent
  ],
  providers: [DialogService],
  templateUrl: './model-renderer.component.html',
  styleUrls: ['./model-renderer.component.scss'],
})
export class ModelRendererComponent  {
  modelForm!: FormGroup;
  orgOptions!: any;
  selectedOrgName!: any;
  selectedAiEngineId!: string;
  selectedModelTypeId!: string;
  selectedModelId!: string;
  aiEngines: any[] = [];
  modelTypeList: any[] = [];
  modelNames: any[] = [];
  isDisabled: boolean = false;

  apiVersionOptions: DropdownOption[] = [];

  inputVisibility = {
    AzureOpenAI: false,
    AmazonBedrock: false,
    GoogleAI: false,
    DaOpenSourceAI: false,
    BNY: false,
  };

  dropdownConfigs: {
    label: string;
    targetKey: any;
    mapToListItem?: boolean;
    includeInactive?: boolean;
  }[] = [
    { label: 'AI Engine', targetKey: 'aiEngines', mapToListItem: true },
    { label: 'AzureOpenAI Model', targetKey: 'modelDropdownOptions' },
    { label: 'AmazonBedrock Model', targetKey: 'amazonDropdownOptions' },
    { label: 'GoogleAI Model', targetKey: 'googleDropdownOptions' },
    { label: 'DaOpenSourceAI Model', targetKey: 'daDropdownOptions' },
    { label: 'BNY Model', targetKey: 'bnyDropdownOptions' },

    { label: 'Model Type', targetKey: 'modelTypeList', mapToListItem: true },
    { label: 'Api Version', targetKey: 'apiVersionOptions', includeInactive: true },
  ];
  showModelConfiguration: boolean = true;
  
  closed: EventEmitter<any> = new EventEmitter<any>();

constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private http: HttpClient,
    private modelService: ModelService,
    private cdr: ChangeDetectorRef,
    private dialogService: DialogService,
  ) {
    this.modelForm = this.fb.group({
      modelDeploymentName: ['', [Validators.required, this.noSpacesValidator]],
      modelDescription: ['', [Validators.required]],
      description: ['', this.noSpacesValidator],
      modelType: ['', Validators.required],
      model: [''],
      organization: ['', this.noSpacesValidator],
      domain: ['', this.noSpacesValidator],
      project: ['', this.noSpacesValidator],
      team: ['', this.noSpacesValidator],
      baseurl: ['', [Validators.required, this.noSpacesValidator]],
      llmDeploymentName: ['', this.noSpacesValidator],
      apiKey: ['', this.noSpacesValidator],
      apiVersion: ['', this.noSpacesValidator],
      awsAccessKey: ['', this.noSpacesValidator],
      awsSecretKey: ['', this.noSpacesValidator],
      awsRegion: ['', this.noSpacesValidator],
      bedrockModelId: ['', this.noSpacesValidator],
      gcpProjectId: ['', this.noSpacesValidator],
      gcpLocation: ['', this.noSpacesValidator],
      vertexAIEndpoint: ['', this.noSpacesValidator],
      serviceUrl: ['', this.noSpacesValidator],
      apiKeyEncoded: ['', this.noSpacesValidator],
      headerName: ['', this.noSpacesValidator],
      aiEngine: ['', Validators.required],
    });

  }

  noSpacesValidator = (control: AbstractControl): ValidationErrors | null => {
    if (control.value && control.value.toString().includes(' ')) {
      return { containsSpaces: true };
    }
    return null;
  };

  noLeadingSpaceValidator(control: AbstractControl): ValidationErrors | null {
    const value = control.value;

    if (typeof value === 'string' && value.length > 0 && value.startsWith(' ')) {
      return { leadingSpace: true };
    }
    return null;
  }

  urlValidator(control: AbstractControl): ValidationErrors | null {
    if (control.value && !/^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/.test(control.value)) {
      return { invalidUrl: true };
    }
    return null;
  }

  ngOnInit(): void {
    this.dropdownConfigs.forEach(({ label, targetKey, mapToListItem, includeInactive }) => {
      this.modelService.getDropdownOptions(label, includeInactive).subscribe({
        next: (options: any) => {
          const mapped = mapToListItem
            ? options.map((o: any) => ({ id: o.value, title: o.label, name: o.label, value: o.value }))
            : options.map((o: any) => ({ name: o.label, value: o.value }));
          (this as any)[targetKey]  = mapped;
          this.cdr.detectChanges();
        },
        error: (error: any) => console.error(`Error fetching ${label}:`, error),
      });
    });
  }

  onAiEngineSelected(selectedItem: any) {
    this.selectedAiEngineId = selectedItem.selectedOptions[0].id;
    const selectedEngine = selectedItem.selectedOptions[0].id;
    this.selectedModelId = '';
    this.modelForm.patchValue({
      aiEngine: selectedEngine,
      model: null
    }, { emitEvent: false });
    if (selectedEngine) {
      this.displayInputField(selectedEngine);
      this.updateEngineFieldValidators(selectedEngine);
      this.clearUnneededFields();
      if (this.selectedModelTypeId) {
        this.fetchModelsForTypeAndEngine(this.selectedModelTypeId, selectedEngine);
      } else {
        this.modelNames = [];
      }
    }
  }

  onModelTypeSelected(selectedItem: any) {
    this.selectedModelTypeId = selectedItem.selectedOptions[0].id;
    this.selectedModelId = '';
    this.modelForm.patchValue({
      modelType: this.selectedModelTypeId,
      model: null
    }, { emitEvent: false });
    if (this.selectedAiEngineId) {
      this.displayInputField(this.selectedAiEngineId);
      this.updateEngineFieldValidators(this.selectedAiEngineId);
      this.clearUnneededFields();
      this.fetchModelsForTypeAndEngine(this.selectedModelTypeId, this.selectedAiEngineId);
    } else {
      this.modelNames = [];
    }
  }

  onModelSelected(selectedItem: any) {
    this.selectedModelId = selectedItem.selectedOptions[0].id;
    this.modelForm.patchValue({ model: selectedItem.selectedOptions[0].id }, { emitEvent: false });
  }

  displayInputField(data: string) {
    // Reset visibility first
    Object.keys(this.inputVisibility).forEach((key) => {
      this.inputVisibility[key as keyof typeof this.inputVisibility] = false;
    });
    // Show only the selected engine's fields
    if (this.inputVisibility.hasOwnProperty(data)) {
      this.inputVisibility[data as keyof typeof this.inputVisibility] = true;
    }
    // Reset form fields specific to other engines to avoid cross-engine data leakage
    const getGoogleAIFields = () => {
      if (this.selectedModelTypeId === 'Embedding') {
        return ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'];
      }
      return ['gcpProjectId', 'gcpLocation'];
    };
    
    const engineFieldsMap: Record<string, string[]> = {
      AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],
      AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],
      GoogleAI: getGoogleAIFields(),
      DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],
      BNY: []
    };
    const selectedEngineFields = engineFieldsMap[data] || [];
    const hasFields = selectedEngineFields.length > 0;
  
    // Update a property to indicate whether the model configuration should be shown
    this.showModelConfiguration = hasFields;
    // Clear all engine-specific fields
    Object.values(engineFieldsMap).flat().forEach(field => {
      this.modelForm.get(field)?.reset();
    });
    // Mark required fields as untouched (optional, for cleaner UX)
    this.modelForm.markAsUntouched();
  }

  updateEngineFieldValidators(selectedEngine: string): void {
    const engineFieldMap: Record<string, string[]> = {
      AzureOpenAI: ['baseurl', 'llmDeploymentName', 'apiKey', 'apiVersion'],
      AmazonBedrock: ['awsAccessKey', 'awsSecretKey', 'awsRegion', 'bedrockModelId'],
      GoogleAI: ['gcpProjectId', 'gcpLocation', 'vertexAIEndpoint'],
      DaOpenSourceAI: ['serviceUrl', 'apiKeyEncoded', 'headerName'],
      BNY: []
    };
    const fieldsToUpdate = engineFieldMap[selectedEngine] || [];
    const allEngineFields = Object.values(engineFieldMap).flat();
    // Clear validators for all fields
    allEngineFields.forEach(field => {
      const control = this.modelForm.get(field);
      if (control) {
        control.clearValidators();
        control.updateValueAndValidity();
      }
    });
    // Apply only required validators to selected engine fields
    fieldsToUpdate.forEach(field => {
      const control = this.modelForm.get(field);
      if (control) {
        if (field === 'baseurl' || field === 'serviceUrl') {
          control.setValidators([Validators.required, this.noSpacesValidator]);
          control.updateValueAndValidity();
        }
        else {
          control.setValidators([Validators.required, this.noSpacesValidator]);
          control.updateValueAndValidity();
        }
      }
    });
  }

  private clearUnneededFields(): void {
    // Clear vertexAIEndpoint for GoogleAI Generative models
    if (this.selectedAiEngineId === 'GoogleAI' && this.selectedModelTypeId !== 'Embedding') {
      const vertexControl = this.modelForm.get('vertexAIEndpoint');
      if (vertexControl) {
        vertexControl.setValue('');
        vertexControl.clearValidators();
        vertexControl.updateValueAndValidity();
      }
    }
  }

  // Fetch models using the API refdata endpoint for the selected engine
  fetchModelsForTypeAndEngine(modelType: string, aiEngine: string): void {
    // Map engine to ref_key
    const engineRefKeyMap: { [key: string]: string } = {
      AzureOpenAI: 'AzureOpenAI Model',
      AmazonBedrock: 'AmazonBedrock Model',
      GoogleAI: 'GoogleAI Model',
      DaOpenSourceAI: 'DaOpenSourceAI Model',
      BNY: 'BNY Model',
      DatabricksAI: 'DatabricksAI Model',
    };
    const refKey = engineRefKeyMap[aiEngine];
    if (!refKey) {
      this.modelNames = [];
      return;
    }
    this.modelService.getDropdownOptions(refKey, false, true).subscribe({
      next: (modelGroups: any[]) => {
        const group = modelGroups.find(g => g.type === modelType);
        this.modelNames = group && group.models
          ? group.models.map((m: any) => ({ id: m.id, title: m.name, name: m.name, value: m.id }))
          : [];
        this.cdr.detectChanges();
      },
      error: (error) => {
        this.modelNames = [];
        console.error('Error fetching models:', error);
      }
    });
  }

  areSelectionsComplete(): boolean {
    return !!this.selectedAiEngineId && !!this.selectedModelTypeId && !!this.selectedModelId;
  }

  onCancel() {
    this.modelForm.reset();
    this.closed.emit(false);
  }

  onSave() {
    if (this.modelForm.valid) {
      const formValues = this.modelForm.value;
      const payload = Object.keys(formValues).reduce((acc, key) => {
        const value = formValues[key];
        if (value !== null && value !== undefined && value !== '') {
          acc[key] = value;
        }
        return acc;
      }, {} as any);
      if (!(this.selectedAiEngineId === 'GoogleAI' && this.selectedModelTypeId === 'Embedding')) {
        delete payload.vertexAIEndpoint;
      }

      this.isDisabled = true;
      this.dialogService.loading({
        title: 'Creating Model...',
        message: 'Please wait while we create the model.',
        showProgress: false,
        showCancelButton: false
      });

      this.modelService.saveModel(payload).subscribe({
        next: (info) => {
            this.dialogService.close(); // Close loading dialog
            this.dialogService.success({
            title: 'Success!',
            message: info?.info?.message || info.message || 'Model created successfully!'
          }).then(() => {
            this.isDisabled = false;
            this.closed.emit(true);
          });
        },
        error: (error) => {
            this.dialogService.close(); // Close loading dialog
          this.dialogService.error({
            title: 'Creation Failed',
            message: error?.error?.message || error.message || 'Failed to create model. Please try again.',
            showRetryButton: true,
            retryButtonText: 'Retry'
          }).then(result => {
            this.isDisabled = false;
            if (result.action === 'retry') {
              this.onSave();
            }
          });
        },
      });
    }
  }
} 
