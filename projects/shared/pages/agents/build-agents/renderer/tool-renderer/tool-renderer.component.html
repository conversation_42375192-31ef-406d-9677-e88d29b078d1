<div class="tool-renderer-container" style="display: flex; flex-direction: column; background: transparent; border: none; width: 100%; padding: 0; margin: 0;">
  <!-- Header with Ask AVA button only - shown only on main creation page -->
  <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; padding: 0 0 16px 0; background: transparent; border: none;">
    <h2 class="modal-title">Create Tool</h2>
    <ava-button
      *ngIf="showFormFields"
      [label]="'Ask AVA'"
      variant="secondary"
      size="small"
      iconName="WandSparkles"
      [customStyles]="{
        'border': '2px solid transparent',
        'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
        'background-origin': 'border-box',
        'background-clip': 'padding-box, border-box',
        '--button-effect-color': '33, 90, 214'
      }"
      (userClick)="toggleAskAvaModal()">
    </ava-button>
  </div>

  <!-- Content -->
  <div class="modal-content" style="flex: 1; padding: 0; overflow: visible; background: transparent; border: none;">
    <form [formGroup]="toolForm" *ngIf="showFormFields" style="display: flex; flex-direction: column; gap: 20px; padding: 0; margin: 0;">
      <!-- Tool Name -->
      <div class="form-field">
        <ava-textbox
          [label]="'Tool Name'"
          [placeholder]="labels.tnPlaceholder"
          [formControl]="$any(toolForm.get('name'))"
          [required]="true">
        </ava-textbox>
      </div>

      <!-- Tool Description -->
      <div class="form-field">
        <ava-textarea
          [label]="'Tool Description'"
          [placeholder]="labels.tdPlaceholder"
          [formControl]="$any(toolForm.get('description'))"
          [required]="true"
          [rows]="2">
        </ava-textarea>
      </div>

      <!-- Tool Class -->
      <div class="form-field">
        <ava-textbox
          [label]="'Tool Class'"
          [placeholder]="'e.g., EmailSenderTool, FileProcessorTool, DataAnalyzerTool'"
          [formControl]="$any(toolForm.get('toolClassName'))"
          [required]="true">
        </ava-textbox>
      </div>

      <!-- Tool Class Definition -->
      <div class="form-field code-field">
        <label class="field-label">Tool Class Definition</label>
        <div class="code-editor-wrapper">
          <app-code-editor
            #codeEditor
            [title]="''"
            [language]="codeLanguagesMap['Python']"
            [Control]="getClassDefinitionControl()"
            [customCssClass]="'tools-monaco-editor'"
            [placeholder]="placeholder"
            [height]="'300px'"
            (valueChange)="onClassDefinitionChanged($event)">
          </app-code-editor>
        </div>
      </div>
    </form>

    <!-- Tool Compiler Section (Validation Results) -->
    <div class="validation-section" *ngIf="showOnlyToolCompiler">
      <div class="compiler-header">
        <h3 class="compiler-title">Tool Compiler</h3>
      </div>
      <div class="compiler-content">
        <div class="validation-output" *ngIf="!isValidating">
          <div class="code-editor-wrapper">
            <app-code-editor
              #compilerEditor
              [title]="''"
              [language]="codeLanguagesMap['Python']"
              [value]="validationOutput || 'No validation output available'"
              [customCssClass]="'tools-compiler-editor'"
              [height]="'300px'"
              [readonly]="true">
            </app-code-editor>
          </div>
        </div>
        <div class="validation-loading" *ngIf="isValidating">
          <div class="loader"></div>
          <span>Validating your tool...</span>
        </div>
      </div>
    </div>

  </div>

  <!-- Footer -->
  <div class="modal-footer" style="padding: 20px 0 0 0; background: transparent; display: flex; justify-content: flex-end; gap: 12px; border: none;">
    <!-- Initial state buttons -->
    <ng-container *ngIf="showFormFields">
      <ava-button
        [label]="'Cancel'"
        variant="secondary"
        size="medium"
        (userClick)="closeModal()">
      </ava-button>
      <ava-button
        [label]="isValidating ? 'Validating...' : 'Test'"
        variant="primary"
        size="medium"
        (userClick)="onTest()"
        [disabled]="isSubmitDisabled() || isValidating">
      </ava-button>
    </ng-container>

    <!-- Validation result buttons -->
    <ng-container *ngIf="showOnlyToolCompiler && !isValidating">
      <ava-button
        [label]="'Go Back and Revise'"
        variant="secondary"
        size="medium"
        (userClick)="onGoBackAndRevise()">
      </ava-button>
      <ava-button
        [label]="isSaving ? 'Saving...' : 'Save Anyways'"
        variant="primary"
        size="medium"
        (userClick)="onSaveAnyways()"
        [disabled]="isSaving">
      </ava-button>
    </ng-container>

    <!-- Save success buttons -->
    <ng-container *ngIf="showSaveButton || showSendForApprovalButton">
      <ava-button
        *ngIf="showSaveButton"
        [label]="isSaving ? 'Saving...' : 'Save'"
        variant="secondary"
        size="medium"
        (userClick)="onSave()"
        [disabled]="isSubmitDisabled() || isSaving">
      </ava-button>
      <ava-button
        *ngIf="showSendForApprovalButton"
        [label]="isSendingForApproval ? 'Sending...' : 'Send for Approval'"
        variant="primary"
        size="medium"
        (userClick)="onSendForApproval()"
        [disabled]="!toolId || isSendingForApproval">
      </ava-button>
    </ng-container>
  </div>
</div>

<!-- Ask AVA Wrapper -->
<app-ask-ava-wrapper
  [show]="showAskAvaModal"
  [prompt]="prompt"
  [isLoading]="isLoading"
  [showOutput]="showToolOutput"
  (oNClickGenerate)="onClickGenerate($event)"
  (oNClickClosed)="onClose()"
  (oNClickUse)="onUse()"
  (oNClickReset)="onReset()"
  (oNClickCancel)="onCancle()">
</app-ask-ava-wrapper>
