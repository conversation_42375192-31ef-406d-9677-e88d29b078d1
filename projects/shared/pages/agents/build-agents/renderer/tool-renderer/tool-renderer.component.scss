// Clean tool renderer styles
:host {
  display: block;
  width: 100%;
}

.tool-renderer-container {
  display: flex !important;
  flex-direction: column !important;
  height: 80vh !important;
  max-height: 80vh !important;
  background: transparent !important;
  overflow: hidden !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  width: 100% !important;
  max-width: none !important;
}

.modal-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 0 16px 0 !important;
  background: transparent !important;
  flex-shrink: 0 !important;
  border: none !important;
  border-bottom: none !important;

  .modal-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
  }

  // Ask AVA button styling
  ::ng-deep ava-button {
    .btn {
      height: 32px !important;
      padding: 6px 12px !important;
      font-size: 13px !important;
      font-weight: 500 !important;
    }
  }
}

.modal-content {
  flex: 1 !important;
  padding: 0 !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  border: none !important;
  background: transparent !important;

  form {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  .form-field {
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    margin-bottom: 0 !important;

    .field-label {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #374151 !important;
      margin: 0 !important;
    }

    // Ava components handle their own styling

    &.code-field {
      .code-editor-wrapper {
        border: none;
        border-radius: 0;
        overflow: visible;
        height: 300px;

        // Remove any internal borders from code editor
        ::ng-deep .tools-monaco-editor {
          border: none;

          .monaco-editor {
            border: none;
          }
        }
      }
    }

    .compiler-section {
      margin-top: 20px;

      .compiler-content {
        border: 1px solid #E5E7EB;
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        ::ng-deep .validation-json-editor {
          .monaco-editor {
            border: none;
          }
        }

        .validation-loading {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16px;
          background: rgba(255, 255, 255, 0.95);
          padding: 30px;
          border-radius: 12px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          z-index: 10;

          .cubical-loader {
            display: flex;
            gap: 4px;

            .cube {
              width: 12px;
              height: 12px;
              background: linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%);
              border-radius: 2px;
              animation: cubical-bounce 1.4s infinite ease-in-out both;

              &:nth-child(1) { animation-delay: -0.32s; }
              &:nth-child(2) { animation-delay: -0.16s; }
              &:nth-child(3) { animation-delay: 0s; }
              &:nth-child(4) { animation-delay: 0.16s; }
            }
          }

          span {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            text-align: center;
          }
        }
      }
    }

  .validation-section {
    padding: 24px 0;

    .compiler-header {
      margin-bottom: 16px;

      .compiler-title {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin: 0;
      }
    }

    .compiler-content {
      border: 1px solid #d1d5db;
      border-radius: 8px;
      background: #f8fafc;
      min-height: 200px;
      position: relative;

      .validation-output {
        padding: 16px;

        pre {
          margin: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          line-height: 1.5;
          color: #374151;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }

      .validation-loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .loader {
          width: 24px;
          height: 24px;
          border: 2px solid #e5e7eb;
          border-top: 2px solid #3b82f6;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        span {
          font-size: 14px;
          color: #6b7280;
          font-weight: 500;
        }
      }
    }
  }
}

.modal-footer {
  padding: 20px 0 0 0 !important;
  background: transparent !important;
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
  flex-shrink: 0 !important;
  border-top: 1px solid #E5E7EB !important;
  margin-top: auto !important;
}

// Ask AVA modal grid layout and overlay styling
::ng-deep app-ask-ava-wrapper {
  // Fix overlay border radius to match modal
  ava-popup {
    .popup-overlay {
      border-radius: 12px;
      overflow: hidden;
    }

    .popup-content {
      border-radius: 12px;
      overflow: hidden;
    }

    .popup-container {
      border-radius: 12px;
      overflow: hidden;
    }
  }

  .row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -8px;
    margin-right: -8px;

    &.g-2 > * {
      padding-left: 8px;
      padding-right: 8px;
      margin-bottom: 16px;
    }
  }

  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .col-md-12 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

// Loading animations
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cubical-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #E5E7EB;
  background: #F9FAFB;

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

// Custom styles for the code editor
:host ::ng-deep {
  .tools-monaco-editor {
    .monaco-editor {
      border: 1px solid #D1D5DB;
      border-radius: 8px;
    }

    .monaco-editor.vs-dark {
      border-color: #374151;
    }
  }

  .tools-compiler-editor {
    .monaco-editor {
      border: 1px solid #D1D5DB;
      border-radius: 8px;
      background-color: #f8f9fa;
    }

    .monaco-editor.vs-dark {
      border-color: #374151;
      background-color: #1e1e1e;
    }
  }
}

// Loading states
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// Responsive design
@media (max-width: 768px) {
  .tool-renderer-container {
    width: 95vw;
    max-height: 90vh;
  }

  .modal-content {
    padding: 16px;
  }

  .modal-header,
  .modal-footer {
    padding: 16px;
  }

  .footer-actions {
    flex-direction: column;
    gap: 8px;

    ava-button {
      width: 100%;
    }
  }
}

// Animation for smooth transitions
.tool-renderer-container {
  animation: slideInFromTop 0.3s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Button state styles
ava-button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

// Form validation styles
.form-field {
  .error-message {
    color: #DC2626;
    font-size: 12px;
    margin-top: 4px;
  }
}

// Success/Error state indicators
.validation-status {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;

  &.success {
    background-color: #F0FDF4;
    border: 1px solid #BBF7D0;
    color: #166534;
  }

  &.error {
    background-color: #FEF2F2;
    border: 1px solid #FECACA;
    color: #DC2626;
  }
}
}