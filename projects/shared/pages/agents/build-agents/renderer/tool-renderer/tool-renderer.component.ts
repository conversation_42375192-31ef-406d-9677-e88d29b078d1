import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, ChangeDetectorRef, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  FormControl,
  Validators,
} from '@angular/forms';
import {
  ButtonComponent,
  DialogService,
  AvaTextboxComponent,
  AvaTextareaComponent,
} from '@ava/play-comp-library';
import {
  CodeEditorComponent,
  CodeLanguage,
  CodeEditorTheme,
} from '@shared/components/code-editor/code-editor.component';
import { AskAvaWrapperComponent } from '@shared/components/ask-ava-wrapper/ask-ava-wrapper.component';
import { ToolsService } from '@shared/services/tools.service';
import { PromptEnhanceService } from '@shared/services/prompt-enhance.service';
import {
  TOOL_CREATION_USECASE_IDENTIFIER,
  ToolModes,
} from '../../../../libraries/tools/constants/builtInTools';
import toolsLabels from '../../../../libraries/tools/constants/tools.json';

@Component({
  selector: 'app-tool-renderer',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    ButtonComponent,
    AvaTextboxComponent,
    AvaTextareaComponent,
    CodeEditorComponent,
    AskAvaWrapperComponent,
  ],
  providers:[DialogService],
  templateUrl: './tool-renderer.component.html',
  styles: [`
    :host {
      display: block;
      width: 100%;
    }

    .tool-renderer-container {
      display: flex !important;
      flex-direction: column !important;
      height: fit-content !important;
      max-height: 90vh !important;
      background: transparent !important;
      overflow: visible !important;
      border: none !important;
      border-radius: 0 !important;
      box-shadow: none !important;
      width: 70vw !important;
      max-width: 70vw !important;
    }

    .modal-header {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      padding: 0 0 16px 0 !important;
      background: transparent !important;
      flex-shrink: 0 !important;
      border: none !important;
      border-bottom: none !important;
    }

    .modal-header .modal-title {
      font-size: 18px !important;
      font-weight: 600 !important;
      color: #111827 !important;
      margin: 0 !important;
    }

    .modal-content {
      flex: 1 !important;
      padding: 0 !important;
      overflow: visible !important;
      border: none !important;
      background: transparent !important;
    }

    .modal-content form {
      display: flex !important;
      flex-direction: column !important;
      gap: 20px !important;
      padding: 0 !important;
      margin: 0 !important;
    }

    .form-field {
      display: flex !important;
      flex-direction: column !important;
      gap: 8px !important;
      margin-bottom: 0 !important;
    }

    .field-label {
      font-size: 14px !important;
      font-weight: 500 !important;
      color: #374151 !important;
      margin: 0 !important;
    }

    .field-input, .field-textarea {
      width: 100%;
      padding: 12px 16px;
      border: 1px solid #d1d5db;
      border-radius: 8px;
      font-size: 14px;
      color: #111827;
      background: white;
      transition: all 0.2s;
    }

    .field-input:focus, .field-textarea:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .field-textarea {
      resize: vertical;
      min-height: 60px;
    }

    .modal-footer {
      padding: 20px 0 0 0 !important;
      background: transparent !important;
      display: flex !important;
      justify-content: flex-end !important;
      gap: 12px !important;
      flex-shrink: 0 !important;
      border: none !important;
      border-top: none !important;
    }
  `],
})
export class ToolRendererComponent implements OnInit, OnDestroy {
  @ViewChild('codeEditor') codeEditor!: CodeEditorComponent;
  closed: EventEmitter<any> = new EventEmitter<any>()
  closeModal() {
    this.closed.emit();
  }

  toolForm!: FormGroup;
  outputToolForm!: FormGroup;
  labels: any = toolsLabels.labels;
  placeholder: string = toolsLabels.TOOL_PLACEHOLDER.toolClassDef;

  // Ask AVA related properties
  showAskAvaModal = false;
  showToolOutput = false;
  isLoading = false;
  prompt = new FormControl('');

  // Validation properties
  validationOutput: string = '';
  showValidationOutput: boolean = false;
  validationOutputEditorConfig = {
    title: 'Tool Compiler',
    language: 'json' as CodeLanguage,
    theme: 'light' as CodeEditorTheme,
    readOnly: true,
    height: '250px',
    placeholder: 'Validation results will appear here after testing your tool for security and functionality',
  };

  // Code editor configuration
  codeLanguagesMap: { [key: string]: CodeLanguage } = {
    Python: 'python',
  };

  codeEditorState = {
    error: false,
    errorMessage: null as string | null,
  };

  // UI state
  showTestButton = true;
  showCancelButton = true;
  showSaveButton = false;
  showSendForApprovalButton = false;

  // UI section visibility
  showFormFields = true;
  showOnlyToolCompiler = false;
  isValidating = false;
  isSaving = false;
  isSendingForApproval = false;

  // Validation results
  validationResult: any = null;
  securityIssuesFound = false;

  // Tool data
  toolId: string | null = null;

  constructor(
    private fb: FormBuilder,
    private toolsService: ToolsService,
    private promptGenerateService: PromptEnhanceService,
    private dialogService: DialogService,
    private cdr: ChangeDetectorRef,
  ) {
    this.toolForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      toolClassName: ['', [Validators.required, Validators.pattern(/^[A-Z][a-zA-Z0-9]*$/)]],
      classDefinition: new FormControl('', Validators.required),
    });

    // Initialize output form for Ask AVA functionality
    this.outputToolForm = this.fb.group({
      name: [''],
      description: [''],
      toolClassName: [''],
      classDefinition: [''],
    });
  }

  ngOnInit(): void {
    // Initialize form with default values if needed
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  // Helper method to get form controls easily from the template
  getControl(name: string): FormControl {
    return this.toolForm.get(name) as FormControl;
  }

  getClassDefinitionControl(): FormControl {
    return this.toolForm.get('classDefinition') as FormControl;
  }

  getFieldError(fieldName: string): string {
    const field = this.toolForm.get(fieldName);
    const formattedFieldName = /^[A-Z]/.test(fieldName)
      ? fieldName
      : fieldName.charAt(0).toUpperCase() + fieldName.slice(1);
    
    if (field && field.invalid && (field.touched || field.dirty)) {
      if (field.errors?.['required']) {
        return `${formattedFieldName} is required`;
      }
      if (field.errors?.['pattern']) {
        return `${formattedFieldName} must start with uppercase letter and contain only alphanumeric characters`;
      }
    }
    return '';
  }

  isSubmitDisabled(): boolean {
    return this.toolForm.invalid;
  }

  onClassDefinitionChanged(value: string): void {
    const control = this.toolForm.get('classDefinition');
    control?.setValue(value);
    control?.markAsDirty();
    control?.markAsTouched();
  }

  showCodeEditorError(): boolean {
    const ctrl = this.toolForm.get('classDefinition');
    const editorValue = this.codeEditor?.getValue() || '';
    const formValue = ctrl?.value || '';

    const isEmpty = !editorValue.trim() && !formValue.trim();
    const hasError = !!(ctrl?.touched && isEmpty);

    this.codeEditorState.error = hasError;
    this.codeEditorState.errorMessage = hasError
      ? 'Tool Class Definition is required.'
      : null;

    return hasError;
  }

  // Test button functionality - validates the tool code
  onTest(): void {
    if (this.toolForm.invalid) {
      this.toolForm.markAllAsTouched();
      return;
    }

    this.validateTool();
  }

  // Validate tool using security validation API
  validateTool(): void {
    this.isValidating = true;
    this.showValidationOutput = false;
    this.validationOutput = '';

    // Hide form fields and show only Tool Compiler during validation
    this.showFormFields = false;
    this.showOnlyToolCompiler = true;
    this.showValidationOutput = true; // Show the compiler section immediately
    this.validationOutput = 'Loading validation results...'; // Show loading message

    const useCase = 'VALIDATE_TOOLS';
    const useCaseIdentifier = 'VALIDATE_TOOLS@ASCENDION@PLATFORM_ENGINEERING@AVA@DIGITAL_ASCENDER';
    const toolClassDef = this.toolForm.get('classDefinition')?.value;

    // TODO: Remove this mock validation once backend model is configured
    // Uncomment the line below to use mock validation for testing
    // this.useMockValidation(); return;

    this.promptGenerateService.modelApi(toolClassDef, useCase, false, useCaseIdentifier).subscribe({
      next: (res: any) => {
        this.isValidating = false;

        try {
          let responseText = res?.response?.choices?.[0]?.text;

          if (!responseText) {
            this.validationOutput = 'Unable to validate, please try again.';
            this.showValidationOutput = true;
            this.cdr.detectChanges();
            return;
          }

          // Clean up the response text
          responseText = responseText.replace(/```json\n?/, '').replace(/```\n?$/, '');

          // Try to parse and format the JSON
          try {
            const parsed = JSON.parse(responseText);
            const formatted = JSON.stringify(parsed, null, 2);
            this.validationOutput = formatted;

            // Check for security issues
            const { security_issues_found } = parsed;
            this.securityIssuesFound = security_issues_found === true;
            this.validationResult = parsed;

            // Show validation output
            this.showValidationOutput = true;

            // Reset button states - buttons will be controlled by template conditions
            this.showTestButton = false;
            this.showCancelButton = false;
            this.showSaveButton = false;
            this.showSendForApprovalButton = false;

            this.cdr.detectChanges();
          } catch (e) {
            this.validationOutput = responseText;
            this.showValidationOutput = true;
            this.cdr.detectChanges();
          }
        } catch (error) {
          console.error('Error processing validation response:', error);
          this.validationOutput = 'Error: Unable to process validation response';
          this.showValidationOutput = true;
          this.cdr.detectChanges();
        }
      },
      error: (error) => {
        this.isValidating = false;
        console.error('Validation API error:', error);

        // Handle specific model configuration errors
        const errorMessage = error?.error?.message || error?.message || 'Unknown error';
        if (errorMessage.includes('model id not found')) {
          this.validationOutput = JSON.stringify({
            "validation_status": "warning",
            "message": "Tool validation service is currently unavailable. You can still save the tool, but please ensure your code follows security best practices.",
            "security_issues_found": false,
            "issues": [],
            "recommendations": [
              "Avoid using eval() or exec() functions",
              "Validate all user inputs",
              "Use secure libraries and avoid deprecated functions",
              "Follow Python security guidelines"
            ]
          }, null, 2);
        } else {
          this.validationOutput = 'Error: ' + errorMessage;
        }

        this.showValidationOutput = true;
        this.cdr.detectChanges();
      }
    });
  }

  // TODO: Remove this mock validation method once backend model is configured
  private useMockValidation(): void {
    setTimeout(() => {
      this.isValidating = false;

      // Mock validation response - you can customize this for testing
      const mockValidationResponse = {
        "validation_status": "success",
        "security_issues_found": false,
        "issues": [],
        "recommendations": [
          "Code appears to follow security best practices",
          "No obvious security vulnerabilities detected",
          "Consider adding input validation for production use"
        ],
        "summary": "Tool validation completed successfully. No security issues found."
      };

      this.validationOutput = JSON.stringify(mockValidationResponse, null, 2);
      this.showValidationOutput = true;
      this.securityIssuesFound = mockValidationResponse.security_issues_found;
      this.validationResult = mockValidationResponse;

      // Reset button states - buttons will be controlled by template conditions
      this.showTestButton = false;
      this.showCancelButton = false;
      this.showSaveButton = false;
      this.showSendForApprovalButton = false;

      this.cdr.detectChanges();
    }, 2000); // Simulate 2 second validation delay
  }

  // Go back to revise the tool code
  onGoBackAndRevise(): void {
    // Reset validation state
    this.showValidationOutput = false;
    this.validationOutput = '';
    this.securityIssuesFound = false;
    this.validationResult = null;
    this.isValidating = false;

    // Show form fields again and hide Tool Compiler
    this.showFormFields = true;
    this.showOnlyToolCompiler = false;

    // Reset button states to initial
    this.showTestButton = true;
    this.showCancelButton = true;
    this.showSaveButton = false;
    this.showSendForApprovalButton = false;
  }



  // Save functionality
  onSave(): void {
    if (this.toolForm.invalid) {
      this.toolForm.markAllAsTouched();
      return;
    }

    this.saveTool();
  }

  // Save anyways functionality (after validation)
  onSaveAnyways(): void {
    this.saveTool();
  }

  private saveTool(): void {
    this.isSaving = true;

    const payload = {
      toolName: this.toolForm.get('name')?.value,
      toolDescription: this.toolForm.get('description')?.value,
      toolConfig: {
        image: '',
        tool_class_def: this.toolForm.get('classDefinition')?.value,
        tool_class_name: this.toolForm.get('toolClassName')?.value,
      },
      teamId: 1, // Default team ID, should be dynamic based on user context
    };

    // Show loading dialog
    this.dialogService.loading({
      title: 'Creating Tool...',
      message: 'Please wait while we create your tool.',
      showProgress: false,
      showCancelButton: false,
    });

    this.toolsService.addNewUserTool(payload).subscribe({
      next: (response) => {
        this.isSaving = false;
        this.dialogService.close(); // Close loading dialog
        this.toolId = response?.toolId || response?.id || response?.data?.id || null;
        this.showSaveSuccessDialog();
      },
      error: (error) => {
        this.isSaving = false;
        this.dialogService.close(); // Close loading dialog
        console.error('Error saving tool:', error);
        this.dialogService.error({
          title: 'Save Error',
          message: error?.error?.message || 'Failed to save the tool. Please try again.',
        });
      }
    });
  }

  private showSaveSuccessDialog(): void {
    this.dialogService.confirmation({
      title: 'Tool Saved Successfully',
      message: `Your tool has been saved successfully${this.toolId ? ` (ID: ${this.toolId})` : ''}. Would you like to send it for approval?`,
      confirmButtonText: 'Send for Approval',
      cancelButtonText: 'Close',
      confirmButtonVariant: 'primary',
      icon: 'circle-check'
    }).then((result) => {
      if (result.confirmed) {
        this.onSendForApproval();
      } else {
        this.closed.emit(true); // Return true for success
      }
    });
  }

  // Send for approval functionality
  onSendForApproval(): void {
    if (!this.toolId) {
      this.dialogService.error({
        title: 'Error',
        message: 'Tool ID not found. Please save the tool first.',
      });
      return;
    }

    this.sendForApproval();
  }

  private sendForApproval(): void {
    this.isSendingForApproval = true;

    // Show loading dialog
    this.dialogService.loading({
      title: 'Sending for Approval...',
      message: 'Please wait while we send your tool for approval.',
      showProgress: false,
      showCancelButton: false,
    });

    this.toolsService.sendForApproval(this.toolId!.toString()).subscribe({
      next: () => {
        this.isSendingForApproval = false;
        this.dialogService.close(); // Close loading dialog

        this.dialogService.success({
          title: 'Approval Sent',
          message: 'Tool has been sent for approval successfully.',
        }).then(() => {
          this.closeModal();
        });
      },
      error: (error) => {
        console.error('Send for approval API error:', error);
        this.isSendingForApproval = false;
        this.dialogService.close(); // Close loading dialog
        this.dialogService.error({
          title: 'Approval Error',
          message: error?.error?.message || 'Failed to send tool for approval. Please try again.',
        });
      }
    });
  }

  // Ask AVA functionality methods
  toggleAskAvaModal(show = true): void {
    this.showAskAvaModal = show;
  }

  onClickGenerate(prompt: string): void {
    this.isLoading = true;

    this.promptGenerateService
      .modelApi(prompt, ToolModes.toolCreation, false, TOOL_CREATION_USECASE_IDENTIFIER)
      .subscribe({
        next: (res: any) => {
          let generatedPrompt: any = {};
          try {
            generatedPrompt = JSON.parse(res?.response?.choices[0]?.text);
          } catch (error) {
            this.isLoading = false;
            this.dialogService.error({
              title: 'Generation Error',
              message: 'Unable to generate tool definition. Please try again.',
            });
            return;
          }

          const {
            tool_name,
            tool_class,
            tool_description,
            tool_class_defination,
          } = generatedPrompt;

          this.outputToolForm.patchValue({
            name: tool_name,
            toolClassName: tool_class,
            description: tool_description,
            classDefinition: tool_class_defination?.replace(/```\n?$/, ''),
          });

          this.showToolOutput = true;
          this.isLoading = false;
        },
        error: () => {
          this.isLoading = false;
          this.dialogService.error({
            title: 'Generation Error',
            message: 'Failed to generate tool definition. Please try again.',
          });
        },
      });
  }

  onUse(): void {
    const formValue = this.outputToolForm.getRawValue();
    this.toolForm.patchValue(formValue);
    if (this.codeEditor) {
      this.codeEditor.setValue(formValue.classDefinition);
    }
    this.outputToolForm.reset();
    this.showToolOutput = false;
    this.prompt.reset('');
    this.toggleAskAvaModal(false);
  }

  onReset(): void {
    this.outputToolForm.reset();
    this.showToolOutput = false;
  }

  onCancle(): void {
    this.prompt.reset('');
    this.onReset();
    this.toggleAskAvaModal(false);
    this.closed.emit(false); 
  }

  onClose(): void {
    this.toggleAskAvaModal(false);
    this.onCancle();
  }

  getOutputControl(name: string): FormControl {
    return this.outputToolForm.get(name) as FormControl;
  }
}
