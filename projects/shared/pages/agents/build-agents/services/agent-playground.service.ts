import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, map, catchError, of, throwError } from 'rxjs';
import { EnvironmentService } from '../../../../services/environment.service';
import { TokenStorageService } from '../../../../auth/services/token-storage.service';

@Injectable({
  providedIn: 'root',
})
export class AgentPlaygroundService {

  constructor(
    private http: HttpClient,
    private environmentService: EnvironmentService,
    private tokenStorage: TokenStorageService,
  ) {}

  private get baseUrl(): string {
    return this.environmentService.consoleInstructionApi;
  }

  private get baseUrlOg(): string {
    return this.environmentService.baseUrl;
  }

  private getUserSignature() {
    const userSignature =
      this.tokenStorage.getDaUsername() || '<EMAIL>';
    return userSignature;
  }

  /**
   * Generate prompt using individual agent API
   * @param data - The prompt data or conversation data
   * @param mode - The mode for execution (agent mode)
   * @param isConvChecked - Whether to use conversation mode
   * @param isTemplateChecked - Whether to use template mode
   * @param attachment - Array of attachments
   * @param useCaseIdentifier - Use case identifier
   * @param fileContents - Optional file contents to include in message
   * @param levelId - Level ID for agent path (org level mapping)
   * @returns Observable with API response
   */
  public generatePrompt(
    data: any,
    mode: string = '',
    isConvChecked: boolean = false,
    isTemplateChecked: boolean = false,
    attachment: string[] = [],
    useCaseIdentifier: string = '',
    fileContents: string = '',
    levelId?: number,
  ): Observable<any> {
    let payload: any = {
      mode: mode,
      useCaseIdentifier: useCaseIdentifier,
      userSignature: this.getUserSignature(),
    };

    // Add levelId to payload if provided
    if (levelId) {
      payload.levelId = levelId;
    }

    let queryString = '';

    if (isConvChecked) {
      // Conversational mode - send conversations array
      let conversationData = Array.isArray(data)
        ? data
        : [{ content: data, role: 'user' }];

      // If file contents exist, append to the last user message
      if (fileContents && conversationData.length > 0) {
        const lastMessage = conversationData[conversationData.length - 1];
        if (lastMessage.role === 'user') {
          lastMessage.content = `${lastMessage.content}\n\nFile Contents:\n${fileContents}`;
        }
      }

      payload.conversations = conversationData;
      queryString = '/instructions/core/execute/chat';
    } else {
      // Non-conversational mode - send prompt with promptOverride
      let promptText = typeof data === 'string' ? data : data.content || data;

      // If file contents exist, append to the prompt
      if (fileContents) {
        promptText = `${promptText}\n\nFile Contents:\n${fileContents}`;
      }

      payload.prompt = promptText;
      payload.promptOverride = true; // Always true for non-conversational mode

      // Add attachments if present
      if (attachment?.length) {
        payload['image'] = attachment;
      }
      queryString = '/instructions/core/execute';
    }

    const url = `${this.baseUrlOg}${queryString}`;

    return this.http.post(url, payload).pipe(
      map((response: any) => {
        return response;
      }),
    );
  }

  /**
   * Parse file content and extract text
   * @param formData - FormData containing files to parse
   * @returns Observable with parsed file content
   */
  public getFileToContent(formData: FormData): Observable<any> {
    const url = `${this.baseUrl}/ava/force/contents`;

    return this.http.post(url, formData).pipe(
      map((response: any) => {
        return response;
      }),
    );
  }

  /**
   * Upload files for processing
   * @param files - Array of files to upload
   * @returns Observable with upload response
   */
  public uploadFiles(files: File[]): Observable<any> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append('files', file);
    });

    const url = `${this.baseUrl}/ava/force/files/upload`;

    return this.http.post(url, formData).pipe(
      map((response: any) => {
        return response;
      }),
    );
  }

  /**
   * Submit agent execute
   * @param payload - The answer payload including agentId, executionId, userInputs, and user email
   * @returns Observable with response
   */
  public submitAgentExecute(payload: {
    agentId: number;
    executionId: string;
    userInputs: { [key: string]: string };
    user: string;
  }): Observable<any> {
    const url = `${this.baseUrlOg}/agents/execute`;

    return this.http.post(url, payload);
  }

  /**
   * Submit agent execution with a file upload.
   *
   * Sends a multipart/form-data request containing the agent execution payload
   * and an uploaded file to the server.
   *
   * @param payload - Object containing agentId, executionId, userInputs, and user email.
   * @param uploadedFileWrapper - Wrapper object containing the uploaded File instance.
   * @returns Observable<any> - The server response wrapped in an Observable.
   */
  public submitAgentExecuteWithFile(
    payload: {
      agentId: number;
      executionId: string;
      userInputs: { [key: string]: string };
      user: string;
    },
    uploadedFileWrapper: { file: File },
  ): Observable<any> {
    const url = `${this.baseUrlOg}/agents/execute/files`;

    const formData = new FormData();
    formData.append('files', uploadedFileWrapper.file);
    formData.append('agentId', payload.agentId.toString());
    formData.append('executionId', payload.executionId);
    formData.append('user', payload.user);
    formData.append('userInputs', JSON.stringify(payload.userInputs));

    return this.http.post(url, formData);
  }
}
