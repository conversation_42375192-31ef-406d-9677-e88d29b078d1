<div class="create-guardrails-container">
  <!-- Three Panel Layout -->
  <div class="three-panel-layout">

    <!-- Left Panel: Guardrail Description -->
    <div class="panel left-panel" [class.collapsed]="isLeftCollapsed">
      <div class="panel-header">
        <div class="header-left">
          <ava-icon iconName="arrow-left" [iconColor]="'#1A46A7'" class="back-arrow" (click)="onExit()"></ava-icon>
        </div>
        <h3 class="panel-title">Guardrail Description</h3>
        <div class="header-right">
          <ava-icon iconName="panel-left" [iconColor]="'#1A46A7'" class="collapse-icon"
            (click)="toggleLeftPanel()"></ava-icon>
        </div>
      </div>
      <div class="panel-content">
        <form [formGroup]="guardrailForm">
          <div class="form-field">
            <ava-textbox [formControl]="getControl('name')" [label]="labels.guardrailName" id="guardrailName"
              name="GuardrailName" [placeholder]="labels.gnPlacholder" variant="primary" size="md" [fullWidth]="true"
              [required]="true" [error]="getFieldError('name')"></ava-textbox>
          </div>

          <div class="form-field">
            <ava-textarea id="description" name="description" [label]="labels.description"
              [formControl]="getControl('description')" [placeholder]="labels.gdPlaceholder" [rows]="6" size="md"
              [fullWidth]="true" [required]="true" [error]="getFieldError('description')"></ava-textarea>
          </div>
        </form>
      </div>
    </div>

    <!-- Center Panel: Guardrail Configuration -->
    <div class="center-panel-container">
      <div class="panel center-panel">
        <div class="header-pad">
          <div class="panel-header">
            <div class="header-left">
              <h3 class="panel-title">Guardrail Configuration</h3>
            </div>
            <div class="panel-actions">
              <ava-button [label]="isEditMode ? 'Update' : 'Save'" size="small" variant="primary"
                (userClick)="isEditMode ? confirmUpdate() : confirmSave()" [disabled]="isSubmitDisabled()"
                [customStyles]="{
                background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                '--button-effect-color': '33, 90, 214'
              }">
              </ava-button>
            </div>
          </div>
        </div>

        <div class="panel-content">
          <!-- Split Editors -->
          <div class="split-editors">
            <div class="colang-panel">
              <div class="code-editor-container">
                <app-code-editor #colangEditor [title]="'Colang Editor'" [titleRequired]="true"
                  [language]="codeLanguagesMap['Colang']" [Control]="getColangControl()"
                  [customCssClass]="'tools-monaco-editor'" [placeholder]="'Write your Colang code here...'"
                  (valueChange)="onColangContentChanged($event)"></app-code-editor>
                <!-- Show error if touched and empty -->
                <div *ngIf="showCodeEditorError()" class="error-text">
                  <ava-icon iconName="info" iconColor="red" iconSize="14"></ava-icon>
                  {{ codeEditorState.errorMessage }}
                </div>
              </div>
            </div>

            <div class="yml-panel">
              <div class="code-editor-container">
                <app-code-editor #ymlEditor [title]="'YML Editor'" [language]="codeLanguagesMap['YML']"
                  [Control]="getYmlControl()" [customCssClass]="'tools-monaco-editor'"
                  [placeholder]="'Write your YML code here...'"></app-code-editor>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Panel: Guardrail Playground -->
    <div class="panel right-panel">
      <div class="panel-header">
        <div class="header-left">
          <h3 class="panel-title">Guardrail Playground</h3>
        </div>
      </div>
      <div class="panel-content">
        <div class="playground-container">
          <app-playground playgroundTitle="Guardrail Playground" [promptOptions]="promptOptions"
            [messages]="chatMessages" [isLoading]="isProcessingChat" [showChatInteractionToggles]="false"
            [showAiPrincipleToggle]="true" (promptChange)="onPromptChanged($event)"
            (messageSent)="handleChatMessage($event)" [showApprovalButton]="false" [isMinimalView]="true"
            [isDisabled]="isPlaygroundDisabled"></app-playground>
        </div>
      </div>
    </div>

  </div>
</div>