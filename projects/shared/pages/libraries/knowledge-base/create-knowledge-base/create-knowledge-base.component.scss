// Main container styling
:host {
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.05) !important;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

.create-knowledge-base-container {
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.page-title {
  font-weight: 600;
  font-size: 20px;
  margin-bottom: 20px;
  color: #374151;
}

// Two panel layout
.two-panel-layout {
  display: flex;
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);
  gap: 20px;
  flex: 1;
  transition: all 0.3s ease;

  // Ensure proper flex behavior for all panels
  .left-panel {
    flex-shrink: 0;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
    transition: all 0.3s ease;
  }

  .right-panel {
    flex: 1;
    min-width: 0;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
    transition: all 0.3s ease;
  }
}

// Common panel styles with glassy finish
.panel {
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.6) 100%
    ),
    linear-gradient(
      114deg,
      rgba(0, 150, 214, 0.04) 1.5%,
      rgba(255, 255, 255, 0.08) 25.71%,
      rgba(255, 255, 255, 0.2) 50.17%,
      rgba(255, 255, 255, 0.08) 73.42%,
      rgba(0, 150, 214, 0.04) 98.86%
    );
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    var(--Elevation-03-set-1-X, 0) 2px 2px
      var(--Elevation-03-set-1-Spread, -3px) var(--Brand-Neutral-n-50, #f0f1f2),
    var(--Elevation-03-set-2-X, 0) var(--Elevation-03-set-2-X, 0)
      var(--Elevation-03-set-2-Blur, 6px) var(--Elevation-03-set-2-Spread, -2px)
      var(--Elevation-03-set-2-Color, #d1d3d8);
  overflow: hidden;

  .panel-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .back-arrow {
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .panel-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #374151;
    }

    .header-right {
      display: flex;
      align-items: center;

      .collapse-icon {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .panel-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }

  .panel-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    background: transparent;

    // Hide scrollbar for webkit browsers
    &::-webkit-scrollbar {
      display: none;
    }

    // Hide scrollbar for Firefox
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
}

// Left Panel: Knowledge Base Description (30% width)
.left-panel {
  width: 30%;
  min-width: 350px;
  max-width: 500px;
  transition: all 0.3s ease;
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);

  &.collapsed {
    width: 60px;
    min-width: 60px;
    max-width: 60px;

    .panel-content {
      opacity: 0;
      pointer-events: none;
    }

    .panel-title {
      opacity: 0;
    }

    .header-left {
      opacity: 0;
    }

    .header-right {
      opacity: 1;
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
    }

    .collapse-icon {
      transform: rotate(180deg);
    }
  }

  .form-field {
    margin-bottom: 20px;

    &:last-child {
      flex: 1;
      display: flex;
      flex-direction: column;

      ava-textarea {
        flex: 1;

        ::ng-deep .textarea-container {
          height: 100%;

          textarea {
            height: 100% !important;
            min-height: 200px;
            resize: vertical;
            border-radius: 8px;
            border: 1px solid #d1d5db;

            &:focus {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }
        }
      }
    }
  }
}

// Right Panel: Knowledge Base Configuration (70% width)
.right-panel {
  width: 70%;
  min-width: 500px;
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);

  .panel-content {
    padding: 0;
  }

  .configuration-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow-y: auto;
  }
}

// Loading overlay
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
}

// Section styling
.section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 12px;
  color: #374151;

  @media (max-width: 576px) {
    font-size: 14px;
    margin-bottom: 8px;
  }
}

.split-section {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.retriever-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

// Split size slider styling
.split-size-container {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding-bottom: 10px;

  ava-slider {
    flex: 1;
    min-width: 200px;
  }

  .value-box {
    width: 70px;
    height: 49px;
    border: 1px solid #4b507f;
    border-radius: 8px;
    font-size: 16px;
    font-family: Inter, sans-serif;
    text-align: center;
    line-height: 30px;
    color: #4b507f;
    background-color: white;
    padding: 0;
  }
}

.field-error {
  color: #e53935;
  font-size: 13px;
  margin-top: 4px;
}

.upload-fields-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 40px;


  h3 {
    grid-column: 1 / -1;
    margin: 0 0 16px;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
  }
}

.schema-dropdown {
  margin-top: 10px;
}

.dropdown-medium {
  width: 250px;
  display: inline-block;
}

// Global styles for form elements
::ng-deep {
  // Textbox styling
  ava-textbox {
    .textbox-container {
      width: 100%;

      .textbox-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .textbox-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        color: #374151;

        &:focus {
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
          outline: none;
        }

        &[readonly] {
          background: rgba(255, 255, 255, 0.03);
          opacity: 1;
        }
      }
    }
  }

  // Textarea styling
  ava-textarea {
    .textarea-container {
      width: 100%;

      .textarea-label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .textarea-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        font-size: 14px;
        font-family: inherit;
        resize: vertical;
        transition: all 0.2s ease;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        color: #374151;

        &:focus {
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
          outline: none;
        }

        &[readonly] {
          background: rgba(255, 255, 255, 0.03);
          opacity: 1;
        }
      }
    }
  }

  // Button styling
  ava-button {
    .btn {
      font-size: 14px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.2s ease-in-out;
      padding: 8px 16px;

      &.btn-sm {
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }

  // File upload styling
  ava-file-upload {
    .upload-container {
      .file-actions,
      .ava-icon-container {
        display: none;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 1600px) {
  .left-panel {
    width: 30%;
    min-width: 320px;
  }

  .right-panel {
    width: 70%;
    min-width: 450px;
  }
}

@media (max-width: 1400px) {
  .left-panel {
    width: 30%;
    min-width: 300px;
  }

  .right-panel {
    width: 70%;
    min-width: 400px;
  }

  .two-panel-layout {
    gap: 16px;
  }

  .panel .panel-content {
    padding: 16px;
  }
}

@media (max-width: 1200px) {
  :host {
    padding: 16px;
  }

  .left-panel {
    width: 30%;
    min-width: 280px;
  }

  .right-panel {
    width: 70%;
    min-width: 350px;
  }

  .two-panel-layout {
    gap: 12px;
  }

  .panel .panel-content {
    padding: 14px;
  }

  .panel-header {
    padding: 14px 16px;
  }

  .panel-title {
    font-size: 15px;
  }

  .panel-actions {
    gap: 6px;
  }

  .panel-actions ava-button {
    font-size: 12px;
  }
}

@media (max-width: 992px) {
  :host {
    padding: 12px;
  }

  .two-panel-layout {
    flex-direction: column;
    height: auto;
    gap: 16px;
  }

  .left-panel,
  .right-panel {
    width: 100%;
    min-width: unset;
    max-width: unset;
  }

  .left-panel.collapsed {
    width: 100%;
    min-width: unset;
    max-width: unset;

    .panel-content {
      opacity: 1;
      pointer-events: auto;
    }

    .panel-title {
      opacity: 1;
    }

    .header-right {
      opacity: 1;
    }
  }

  .panel .panel-content {
    padding: 16px;
  }

  .panel-header {
    padding: 16px 20px;
  }

  .panel-title {
    font-size: 16px;
  }

  .panel-actions {
    gap: 6px;
  }

  .panel-actions ava-button {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media (max-width: 768px) {
  :host {
    padding: 8px;
  }

  .two-panel-layout {
    gap: 12px;
  }

  .panel .panel-content {
    padding: 12px;
  }

  .panel-header {
    padding: 12px 16px;
  }

  .panel-title {
    font-size: 14px;
  }

  .panel-actions {
    gap: 4px;
  }

  .panel-actions ava-button {
    font-size: 11px;
    padding: 4px 8px;
  }

  .header-left,
  .header-right {
    i {
      font-size: 14px;
    }
  }

  // Form fields responsive
  ::ng-deep {
    ava-textbox .textbox-input,
    ava-textarea .textarea-input {
      padding: 10px 12px;
      font-size: 13px;
    }

    ava-textbox .textbox-label,
    ava-textarea .textarea-label {
      font-size: 13px;
    }
  }
}

@media (max-width: 576px) {
  :host {
    padding: 6px;
  }

  .two-panel-layout {
    gap: 8px;
  }

  .panel .panel-content {
    padding: 10px;
  }

  .panel-header {
    padding: 10px 12px;
  }

  .panel-title {
    font-size: 13px;
  }

  .panel-actions {
    gap: 3px;
  }

  .panel-actions ava-button {
    font-size: 10px;
    padding: 3px 6px;
  }

  .header-left,
  .header-right {
    i {
      font-size: 12px;
    }
  }

  // Form fields responsive
  ::ng-deep {
    ava-textbox .textbox-input,
    ava-textarea .textarea-input {
      padding: 8px 10px;
      font-size: 12px;
    }

    ava-textbox .textbox-label,
    ava-textarea .textarea-label {
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  :host {
    padding: 4px;
  }

  .two-panel-layout {
    gap: 6px;
  }

  .panel .panel-content {
    padding: 8px;
  }

  .panel-header {
    padding: 8px 10px;
  }

  .panel-title {
    font-size: 12px;
  }

  .panel-actions {
    gap: 2px;
  }

  .panel-actions ava-button {
    font-size: 9px;
    padding: 2px 4px;
  }

  .header-left,
  .header-right {
    i {
      font-size: 11px;
    }
  }

  // Form fields responsive
  ::ng-deep {
    ava-textbox .textbox-input,
    ava-textarea .textarea-input {
      padding: 6px 8px;
      font-size: 11px;
    }

    ava-textbox .textbox-label,
    ava-textarea .textarea-label {
      font-size: 11px;
    }
  }
}

ava-textarea ::ng-deep .ava-textarea__container {
  background: white;
}

.right-header {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}

.ava-file-upload-wrapper {
  margin-bottom: 20px;
}