import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { KnowledgeBaseComponent } from './knowledge-base.component';
import { KnowledgeBaseService } from '../../../shared/services/knowledge-base.service';
import { CardData } from '../../../shared/models/card.model';
import { formatToDisplayDate } from '../../../shared/utils/date-utils';

describe('KnowledgeBaseComponent', () => {
  let component: KnowledgeBaseComponent;
  let fixture: ComponentFixture<KnowledgeBaseComponent>;
  let mockKnowledgeBaseService: jasmine.SpyObj<KnowledgeBaseService>;

  const mockCardData: CardData[] = [
    {
      collectionName: 'Test KB',
      createdDate: '2025-06-01T00:00:00Z',
    } as unknown as CardData
  ];

  beforeEach(async () => {
    mockKnowledgeBaseService = jasmine.createSpyObj('KnowledgeBaseService', ['fetchAllKnowledge']);

    await TestBed.configureTestingModule({
      declarations: [KnowledgeBaseComponent],
      providers: [
        { provide: KnowledgeBaseService, useValue: mockKnowledgeBaseService }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(KnowledgeBaseComponent);
    component = fixture.componentInstance;
  });

  afterEach(() => {
    component.ngOnDestroy(); // Properly trigger destroy$ cleanup
  });

  it('should fetch and transform knowledge base data', () => {
    mockKnowledgeBaseService.fetchAllKnowledge.and.returnValue(of(mockCardData));

    component.ngOnInit(); // triggers the observable logic
    fixture.detectChanges();

    expect(component.allKnowledgeBase.length).toBe(1);
    expect(component.filteredKnowledgeBase.length).toBe(1);

    const item = component.allKnowledgeBase[0];
    expect(item.title).toBe('Test KB');
    expect(item.createdDate).toBe(formatToDisplayDate('2025-06-01T00:00:00Z'));
    expect(item.actions).toEqual([
      { icon: 'execute', action: 'execute', tooltip: 'Access Knowledge Base' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Knowledge Base' }
    ]);
    expect(item.actions?.length).toBe(2);
  });

  it('should handle fetch errors and return empty array', () => {
    mockKnowledgeBaseService.fetchAllKnowledge.and.returnValue(
      throwError(() => new Error('API failed'))
    );

    component.ngOnInit();
    fixture.detectChanges();

    expect(component.allKnowledgeBase.length).toBe(0);
    expect(component.filteredKnowledgeBase.length).toBe(0);
    expect(component.isLoading).toBeFalse();
  });
});
