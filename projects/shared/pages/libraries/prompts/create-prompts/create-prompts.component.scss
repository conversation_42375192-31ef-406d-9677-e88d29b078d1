// Layout for role and dropdown side by side
.role-dropdown-row {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  margin-top: 8px;
}

// Match textbox and dropdown style
.role-dropdown-style {
  flex: 1 1 0;
}

.loading-overlay {
  // position: absolute;
  // top: 0;
  // left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.create-prompts-container {
  width: 100%;
  height: calc(100vh - 80px); /* Account for any header/nav */
  min-height: 600px;
  display: flex;
  flex-direction: column;
  background-color: transparent;

  form {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
}

::ng-deep #save-button .ava-button.primary.ava-button--glass-10 {
  background: var(--build-agents-toggle-active-bg) !important;
}

::ng-deep #regenerate-button .ava-button.primary.ava-button--glass-10 {
  background-image:
    linear-gradient(#ffffff, #ffffff),
    linear-gradient(
      103.35deg,
      var(--color-brand-primary) 31.33%,
      var(--global-color-aqua-500) 100%
    );
  background-origin: border-box;
  background-clip: padding-box, border-box;
  --button-effect-color: 33, 90, 214 !important;
}

.page-title {
  font-weight: 700;
  font-size: 24px;
  font-weight: bold;
}

.form-layout {
  display: flex;
  flex-direction: row;
  gap: 24px;
  padding: 24px;
  flex: 1 1 0;
  min-height: 0;
  overflow: hidden;
  height: 100%;
  min-height: calc(100vh - 120px); /* Ensure minimum height */
  // border: 1px solid #e1e4e8;
  // background: #ffffff;

  /* Responsive adjustments */
  @media (max-width: 768px) {
    gap: 16px;
    padding: 16px;
    min-height: calc(100vh - 100px);
  }

  @media (max-width: 576px) {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
    min-height: calc(100vh - 80px);
  }
}

.left-column,
.middle-column,
.chat-column {
  //padding: 20px;
  box-sizing: border-box;
}

.left-column {
  width: 340px;
  min-width: 60px;
  max-width: 340px;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 100%;
  min-height: calc(100vh - 180px); /* Ensure full height */
  overflow: hidden;
  position: relative;
  background:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.6) 100%
    ),
    linear-gradient(
      114deg,
      rgba(0, 150, 214, 0.04) 1.5%,
      rgba(255, 255, 255, 0.08) 25.71%,
      rgba(255, 255, 255, 0.2) 50.17%,
      rgba(255, 255, 255, 0.08) 73.42%,
      rgba(0, 150, 214, 0.04) 98.86%
    );
  box-shadow:
    var(--Elevation-03-set-1-X, 0) 2px 2px
      var(--Elevation-03-set-1-Spread, -3px) var(--Brand-Neutral-n-50, #f0f1f2),
    var(--Elevation-03-set-2-X, 0) var(--Elevation-03-set-2-X, 0)
      var(--Elevation-03-set-2-Blur, 6px) var(--Elevation-03-set-2-Spread, -2px)
      var(--Elevation-03-set-2-Color, #d1d3d8);
  backdrop-filter: blur(8px);
  border: 1px solid #e1e4e8;
  border-radius: 12px;
  display: flex;
  flex-direction: column;

  /* Responsive adjustments */
  @media (max-width: 768px) {
    width: 300px;
    max-width: 300px;
    min-height: calc(100vh - 160px);
  }

  @media (max-width: 576px) {
    width: 100%;
    min-width: 100%;
    max-width: 100%;
    height: auto;
    min-height: 300px;
  }
}
.left-column.collapsed {
  width: 48px;
  min-width: 48px;
  max-width: 48px;
}
.back-icon {
  width: 24px;
  height: 24px;
  color: var(--color-brand-primary);
  cursor: pointer;
  transition: color 0.2s ease;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: var(--color-brand-primary-hover);
  }
}

.collapse-icon {
  position: absolute;
  top: 20px;
  right: 8px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  font-size: 18px;
}
.left-column.collapsed .collapse-icon {
  right: 12px;
}

.left-column.collapsed .back-icon {
  display: none;
}

.left-column.collapsed .left-title {
  display: none;
}

.left-column.collapsed .left-header {
  justify-content: center;
  padding: 0 8px;
}
.left-column .card-content {
  flex: 1 1 0;
  min-height: 0;
  overflow-y: auto;
  padding: 1rem;
}

.middle-column {
  flex: 1 1 0;
  min-width: 0;
  min-height: calc(100vh - 180px); /* Ensure full height */
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 1.5rem;
  background:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.6) 100%
    ),
    linear-gradient(
      114deg,
      rgba(0, 150, 214, 0.04) 1.5%,
      rgba(255, 255, 255, 0.08) 25.71%,
      rgba(255, 255, 255, 0.2) 50.17%,
      rgba(255, 255, 255, 0.08) 73.42%,
      rgba(0, 150, 214, 0.04) 98.86%
    );
  box-shadow:
    var(--Elevation-03-set-1-X, 0) 2px 2px
      var(--Elevation-03-set-1-Spread, -3px) var(--Brand-Neutral-n-50, #f0f1f2),
    var(--Elevation-03-set-2-X, 0) var(--Elevation-03-set-2-X, 0)
      var(--Elevation-03-set-2-Blur, 6px) var(--Elevation-03-set-2-Spread, -2px)
      var(--Elevation-03-set-2-Color, #d1d3d8);
  backdrop-filter: blur(8px);
  border: 1px solid #e1e4e8;
  border-radius: 12px;
  overflow: auto; /* Allow scrolling if content exceeds height */

  /* Responsive adjustments */
  @media (max-width: 768px) {
    padding: 1rem;
    min-height: calc(100vh - 160px);
  }

  @media (max-width: 576px) {
    padding: 0.75rem;
    width: 100%;
    min-height: 400px;
  }
}

.chat-column {
  width: 400px;
  min-width: 400px;
  background-color: #f8f8f8;
  border-left: 1px solid #ddd;
  display: flex;
  flex-direction: column;
}

.section-title {
  color: var(--color-brand-primary);
}

.prompt_header {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.freeform-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1;
  height: 100%;
}

.freeform-title {
  margin-top: 4rem;
  margin-bottom: 20px;
  color: black;
  text-align: center;
}

::ng-deep ava-textarea.input-box {
  width: 100%;
  min-height: 400px;
  height: 100%;
  flex: 1;
  resize: vertical;
}

:host ::ng-deep .ava-textarea--primary .ava-textarea__container {
  border-color: #e1e4e8 !important; // Your override color
}

/* Make textarea and textbox background white in left panel */
:host ::ng-deep #description .ava-textarea__container {
  background-color: #ffffff !important;
}

:host ::ng-deep #description .ava-textarea__input {
  background-color: #ffffff !important;
}

// :host ::ng-deep #promptName .ava-textbox__container {
//   background-color: #ffffff !important;
// }

// :host ::ng-deep #promptName .ava-textbox__input {
//   background-color: #ffffff !important;
// }

/* Freeform textarea specific styling */
// :host ::ng-deep #freeformPrompt .ava-textarea__container {
//   min-height: 400px !important;
//   height: 100% !important;
//   flex: 1 !important;
// }

#freeformPrompt {
  margin-bottom: 2rem;
}

:host ::ng-deep #freeformPrompt .ava-textarea__container {
  border: 2px solid rgba(var(--rgb-brand-primary), 0.8);
  background: #ffffff;
  border-radius: 16px;
}

:host ::ng-deep #promptType .label-container {
  margin-bottom: 12px;
}

/* Template tab text areas - white background and no shadows */
:host ::ng-deep .template-form .ava-textarea__container {
  background: #ffffff !important;
  box-shadow: none !important;
  // border: 1px solid #e1e4e8 !important;
}

:host ::ng-deep .template-form .ava-textarea__input {
  background: #ffffff !important;
  box-shadow: none !important;
}

/* Template tab specific textareas by ID */
:host ::ng-deep #goal .ava-textarea__container,
:host ::ng-deep #templateDescription .ava-textarea__container,
:host ::ng-deep #backstory .ava-textarea__container,
:host ::ng-deep #expectedOutput .ava-textarea__container,
:host ::ng-deep #intermediateSteps .ava-textarea__container {
  background-color: #ffffff !important;
  box-shadow: none !important;
  // border: 1px solid #e1e4e8 !important;
}

:host ::ng-deep #goal .ava-textarea__input,
:host ::ng-deep #templateDescription .ava-textarea__input,
:host ::ng-deep #backstory .ava-textarea__input,
:host ::ng-deep #expectedOutput .ava-textarea__input,
:host ::ng-deep #intermediateSteps .ava-textarea__input {
  background-color: #ffffff !important;
  box-shadow: none !important;
}

.example {
  margin-top: 1rem;
}

/* Template tab accordion text areas */
:host ::ng-deep #examples-accordion .ava-textarea__container {
  background-color: #ffffff !important;
  box-shadow: none !important;
  border: 1px solid #e1e4e8 !important;
}

:host ::ng-deep #examples-accordion .accordion-container {
  background: #ffffff !important;
  backdrop-filter: none !important;
}

:host ::ng-deep #examples-accordion .ava-textarea__input {
  background-color: #ffffff !important;
  box-shadow: none !important;
}

/* Accordion input and output textareas */
:host ::ng-deep #input .ava-textarea__container,
:host ::ng-deep #output .ava-textarea__container,
:host ::ng-deep #addConsideration .ava-textarea__container {
  background-color: #ffffff !important;
  box-shadow: none !important;
  border: 1px solid #e1e4e8 !important;
}

:host ::ng-deep #input .ava-textarea__input,
:host ::ng-deep #output .ava-textarea__input,
:host ::ng-deep #addConsideration .ava-textarea__input {
  background-color: #ffffff !important;
  box-shadow: none !important;
}

@media (max-width: 600px) {
  ::ng-deep ava-textarea.input-box {
    min-height: 150px;
  }
}

@media (min-width: 1200px) {
  ::ng-deep ava-textarea.input-box {
    min-height: 250px;
  }
}

.regenerate-button-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
  margin-bottom: 0;
}

// .regenerate-button {
//   display: inline-flex;
//   align-items: center;
//   gap: 0px;
//   border-radius: 6px;
//   border: none;
//   cursor: pointer;
// }

.role-textbox {
  width: 40%;
  flex-shrink: 0;
}

.template-form {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  gap: 10px;
}

.regenerate-button-wrapper {
  display: flex;
  justify-content: flex-end;
  margin: 24px 0;
  padding: 0;
}

.optional-sections {
  margin-top: 24px;

  .examples-accordion-wrapper {
    margin-bottom: 16px;
  }
}

.fields-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.fields-row1 {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
  height: 10rem;
  border-bottom: 1px solid #e1e4e8;
}

.add-consideration {
  margin-top: 20px;
  height: 10rem;
  padding-top: 20px;
  border-top: 1px solid #e1e4e8;
}

.field-col {
  flex: 1 1 45%;
  min-width: 400px;
}

.optional-sections {
  margin-top: 0;

  ava-accordion ::ng-deep .accordion-container {
    box-shadow: none !important;
  }

  ava-accordion ::ng-deep .accordion-container .accordion-body {
    padding: 0%;
  }

  ava-accordion ::ng-deep .accordion-container .accordion-header {
    box-shadow: none !important;
  }

  // ava-accordion ::ng-deep .accordion-container .accordion-content {
  //   //padding-top: 26px;
  // }
}

.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden; /* Let tab-content handle the scrolling */
}

.accordion-section {
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  margin-bottom: 20px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}

.playground-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.chat-column-content {
  flex: 1 1 0;
  min-height: 0;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}
.chat-column-content::-webkit-scrollbar {
  display: none;
}

.tabs-wrapper {
  display: flex;
  justify-content: center;
}

.tab-heading {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  text-align: center;
  color: black;
}

.tab-container {
  margin-bottom: 20px;
  margin-top: 20px;
}

/* ✅ Custom Pill Tabs Styles */
.pill-tabs-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  margin: 0 auto;
}

/* Old pill-tab-button styles removed - now using toggle-button component */

.example-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;

  .example-group {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 1rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e1e4e8;
    position: relative;

    .field-col {
      flex: 1 1 45%;
    }

    .example-remove {
      margin-left: auto;
      margin-top: auto;
    }
  }

  .example-actions {
    display: flex;
    justify-content: flex-start; // Add Example left aligned
    margin-top: 1rem;
  }
}

.examples-accordion-wrapper {
  margin-top: 1rem;
}

.examples-accordion-wrapper ::ng-deep .accordion-header {
  min-width: 100% !important;
}

:host ::ng-deep ava-accordion .accordion-container {
  max-width: none !important;
  width: 100% !important; // reinforces full width
}

:host ::ng-deep ava-accordion .accordion-container .accordion-content {
  max-height: 100%;
  overflow-y: auto;
}

::ng-deep #description .ava-textarea__label {
  margin-top: 16px;
}

.left-header {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 30px 16px;
  // background: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-subtle);
  // border-radius: 12px 12px 0 0;
  z-index: 2;
  position: relative;
  gap: 8px;
}

.left-title {
  font-weight: 600;
  font-size: 16px;
  color: var(--color-brand-primary);
  flex: 1;
  display: flex;
  align-items: center;
}

.right-header {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  background: #fff;
  border-bottom: 1px solid #e1e4e8;
  z-index: 2;
}

.tab-content {
  flex: 1 1 0;
  min-height: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding: 8px;
}

.tab-content::-webkit-scrollbar {
  display: none;
}
