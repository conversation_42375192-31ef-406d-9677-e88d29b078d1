import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError, Subject } from 'rxjs';
import { formatDate } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

import { PromptsComponent } from './prompts.component';
import { PromptsService } from '../../../shared/services/prompts.service';
import { CardData } from '../../../shared/models/card.model';

describe('PromptsComponent', () => {
  let component: PromptsComponent;
  let fixture: ComponentFixture<PromptsComponent>;
  let mockPromptsService: jasmine.SpyObj<PromptsService>;
  let destroy$ = new Subject<void>();

  // Sample prompt data as flat array
  const mockPromptData: CardData[] = [
    {
      name: 'Test Prompt',
      updatedAt: '2025-06-01T00:00:00Z',
      categoryName: 'Finance',
      domainName: 'Banking',
      tags: [{ label: 'AI', type: 'custom' }],
    } as unknown as CardData
  ];


  beforeEach(async () => {
    mockPromptsService = jasmine.createSpyObj('PromptsService', ['fetchAllPrompts']);

    await TestBed.configureTestingModule({
      declarations: [PromptsComponent],
      providers: [
        { provide: PromptsService, useValue: mockPromptsService },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: {
              queryParamMap: {
                get: (key: string) => (key === 'page' ? '3' : null)
              }
            }
          }
        }
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PromptsComponent);
    component = fixture.componentInstance;
    // Workaround to access protected destroy$ in tests
    (component as any).destroy$ = destroy$;
  });

  afterEach(() => {
    destroy$.next();
    destroy$.complete();
  });

  it('should fetch and transform prompt data (flat array response)', () => {
    mockPromptsService.fetchAllPrompts.and.returnValue(
      of(mockPromptData) as any
    );

    component.ngOnInit();
    fixture.detectChanges();

    expect(component.allPrompts.length).toBe(1);
    expect(component.filteredPrompts.length).toBe(1);

    const item = component.allPrompts[0];
    expect(item.title).toBe('Test Prompt');
    expect(item.createdDate).toBe(formatDate('2025-06-01T00:00:00Z', 'M/d/yyyy', 'en-US'));

    // Tags: original + categoryName + domainName
    expect(item.tags.length).toBe(3);
    expect(item.tags).toContain(jasmine.objectContaining({ label: 'Finance', type: 'primary' }));
    expect(item.tags).toContain(jasmine.objectContaining({ label: 'Banking', type: 'secondary' }));
    expect(item.tags).toContain(jasmine.objectContaining({ label: 'AI', type: 'custom' }));

    // Actions check
    expect(item.actions).toEqual([
      { icon: 'execute', action: 'execute', tooltip: 'Execute Prompt' },
      { icon: 'delete', action: 'delete', tooltip: 'Delete Prompt' }
    ]);

    expect(component.currentPage).toBe(3);
  });


  it('should fetch and transform prompt data (wrapped object response)', () => {
    mockPromptsService.fetchAllPrompts.and.returnValue(
      of(mockPromptData) as any
    );

    component.ngOnInit();
    fixture.detectChanges();

    expect(component.allPrompts.length).toBe(1);
    expect(component.filteredPrompts.length).toBe(1);

    const item = component.allPrompts[0];
    expect(item.title).toBe('Test Prompt');

    expect(item.tags).toContain(jasmine.objectContaining({ label: 'Finance', type: 'primary' }));
    expect(item.tags).toContain(jasmine.objectContaining({ label: 'Banking', type: 'secondary' }));
  });

  it('should handle missing categoryName and domainName gracefully', () => {
    const modified = [{ ...mockPromptData[0], categoryName: null, domainName: null }];
    mockPromptsService.fetchAllPrompts.and.returnValue(
      of(mockPromptData) as any
    );

    component.ngOnInit();
    fixture.detectChanges();

    const tags = component.allPrompts[0].tags;
    // Only the original 'AI' tag remains
    expect(tags.length).toBe(1);
    expect(tags).toContain(jasmine.objectContaining({ label: 'AI', type: 'custom' }));
  });

  it('should handle missing tags array by defaulting to empty', () => {
    const modified = [{ ...mockPromptData[0], tags: undefined }];
    mockPromptsService.fetchAllPrompts.and.returnValue(
      of(mockPromptData) as any
    );

    component.ngOnInit();
    fixture.detectChanges();

    const tags = component.allPrompts[0].tags;
    // Only category & domain tags added, total 2
    expect(tags.length).toBe(2);
    expect(tags).toContain(jasmine.objectContaining({ label: 'Finance', type: 'primary' }));
    expect(tags).toContain(jasmine.objectContaining({ label: 'Banking', type: 'secondary' }));
  });

  it('should not crash if name or updatedAt is missing', () => {
    const incomplete = [{ categoryName: 'A', domainName: 'B' }];
    mockPromptsService.fetchAllPrompts.and.returnValue(
      of(mockPromptData) as any
    );

    component.ngOnInit();
    fixture.detectChanges();

    expect(component.allPrompts.length).toBe(1);
    expect(component.allPrompts[0].title).toBeUndefined();
    expect(component.allPrompts[0].createdDate).toBeDefined();
  });

  it('should handle fetch errors and return empty list', () => {
    mockPromptsService.fetchAllPrompts.and.returnValue(
      throwError(() => new Error('API failure'))
    );

    component.ngOnInit();
    fixture.detectChanges();

    expect(component.allPrompts.length).toBe(0);
    expect(component.filteredPrompts.length).toBe(0);
    expect(component.isLoading).toBeFalse();
  });
});
