<div class="create-tools-container">
  <!-- Three Panel Layout -->
  <div class="three-panel-layout">

    <!-- Left Panel: Tool Description -->
    <div class="panel left-panel" [class.collapsed]="isLeftPanelCollapsed">
      <div class="panel-header">
        <div class="header-left">
          <ava-icon iconName="arrow-left" [iconColor]="'#1A46A7'" class="back-arrow" (click)="onExit()"></ava-icon>
        </div>
        <h3 class="panel-title">Tool Description</h3>
        <div class="header-right">
          <ava-icon iconName="panel-left" [iconColor]="'#1A46A7'" class="collapse-icon"
            (click)="toggleLeftPanel()"></ava-icon>
        </div>
      </div>
      <div class="panel-content">
        <div class="form-field">
          <ava-textbox [formControl]="getControl('name')" [label]="labels.toolName" id="toolName" name="toolName"
            placeholder="Enter tool name" variant="primary" size="md" [fullWidth]="true" [required]="true"
            [readonly]="isFieldsDisabled">
          </ava-textbox>
        </div>

        <div class="form-field">
          <ava-textbox [formControl]="getControl('toolClassName')" [label]="labels.toolClassName" id="toolClassName"
            name="toolClassName" placeholder="Enter tool class" variant="primary" size="md" [fullWidth]="true"
            [required]="true" [readonly]="isFieldsDisabled">
          </ava-textbox>
        </div>

        <div class="form-field">
          <ava-textarea id="description" name="description" [label]="labels.description"
            [formControl]="getControl('description')" placeholder="Enter description" [rows]="6" size="md"
            [fullWidth]="true" [required]="true" [readonly]="isFieldsDisabled">
          </ava-textarea>
        </div>
      </div>
    </div>

    <!-- Center Panel: Tool Class Definition -->
    <div class="center-panel-container">
      <div class="panel center-panel">
      <div class="header-pad">
        <div class="panel-header">
          <div class="header-left">
            <h3 class="panel-title">Tool Class Definition</h3>
          </div>
          <div class="panel-actions">
            <ava-button label="{{ labels.askAva}}" variant="secondary" size="small" [customStyles]="{
            'border': '2px solid transparent',
            'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
            'background-origin': 'border-box',
            'background-clip': 'padding-box, border-box',
            '--button-effect-color': '33, 90, 214'
          }" iconName="WandSparkles" (userClick)="toggleAskAvaModal()">
            </ava-button>
            <!-- <ava-button label="{{ labels.exit }}" variant="secondary" size="small" [customStyles]="{
            'border': '2px solid transparent',
            'background-image': 'linear-gradient(#ffffff, #ffffff), linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
            'background-origin': 'border-box',
            'background-clip': 'padding-box, border-box',
            '--button-effect-color': '33, 90, 214'
          }" (userClick)="onExit()"></ava-button> -->

            <ng-container *ngIf="!isEditMode && !isExecuteMode">
              <ava-button label="Save" variant="primary" size="small" [disabled]="!toolForm.valid" [customStyles]="{
              background: toolForm.valid ? 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)' : '#cccccc',
              '--button-effect-color': toolForm.valid ? '33, 90, 214' : '204, 204, 204',
              cursor: toolForm.valid ? 'pointer' : 'not-allowed',
              opacity: toolForm.valid ? '1' : '0.7'
            }" (userClick)="confirmSave()"></ava-button>
            </ng-container>

            <ng-container *ngIf="isEditMode && !isExecuteMode">
              <ava-button label="Update" variant="primary" size="small" [customStyles]="{
              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214'
            }" (userClick)="confirmUpdate()"></ava-button>
            </ng-container>
          </div>
        </div>
        </div>

        <div class="panel-content">
          <div class="code-editor-section">
            <app-code-editor #codeEditor [title]="labels.toolClassDefinition" placeholder="{{ placeholder }}"
              language="python" [Control]="getControl('classDefinition')" customCssClass="tools-monaco-editor"
              (primaryButtonSelected)="validateCode()" footerText="{{ labels.note }}" [actionButtons]="editorActions"
              (actionButtonClicked)="onEditorAction($event)" [readonly]="isFieldsDisabled">
            </app-code-editor>
          </div>

          <!-- Tool Compiler Section -->
          <div class="compiler-section">

            <div class="compiler-content">
              <app-code-editor [title]="validationOutputEditorConfig.title"
                [language]="validationOutputEditorConfig.language" [theme]="validationOutputEditorConfig.theme"
                [readonly]="validationOutputEditorConfig.readOnly" [height]="validationOutputEditorConfig.height"
                [value]="showValidationOutput ? validationOutput : ''"
                [placeholder]="validationOutputEditorConfig.placeholder" customCssClass="validation-json-editor">
              </app-code-editor>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Right Panel: Tool Playground -->
    <div class="panel right-panel">
      <div class="panel-header">
        <div class="header-left">
          <h3 class="panel-title">Tool Playground</h3>
        </div>
        <ng-container>
          <ava-button 
            label="Send for Approval" 
            variant="primary" 
            size="small" 
            [disabled]="!isEditMode && !isExecuteMode"
            [customStyles]="{
              background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
              '--button-effect-color': '33, 90, 214',
              opacity: (!isEditMode && !isExecuteMode) ? '0.5' : '1',
              cursor: (!isEditMode && !isExecuteMode) ? 'not-allowed' : 'pointer'
            }" 
            (userClick)="handleApproval()">
          </ava-button>
        </ng-container>
      </div>

      <div class="panel-content">
        <div class="playground-container">
          <app-playground [promptOptions]="promptOptions" [messages]="chatMessages" [isLoading]="isProcessingChat"
            [showChatInteractionToggles]="false" [showAiPrincipleToggle]="false" [showDropdown]="false"
            [displayedAgentName]="getToolDisplayName()" [playgroundTitle]="''" [showApprovalButton]="true"
            (promptChange)="onPromptChanged($event)" (messageSent)="handleChatMessage($event)"
            (approvalRequested)="handleApproval()"></app-playground>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Ask AVA Wrapper -->
<app-ask-ava-wrapper [show]="showAskAvaModal" [prompt]="prompt" [isLoading]="isLoading" [showOutput]="showToolOutput"
  (oNClickGenerate)="onClickGenerate($event)" (oNClickClosed)="onClose()" (oNClickUse)="onUse()"
  (oNClickReset)="onReset()" (oNClickCancel)="onCancle()">
  <form [formGroup]="outputToolForm">
    <div class="row g-2">
      <div class="col-md-6">
        <ava-textbox [formControl]="getOutputControl('name')" [label]="labels.toolName" id="toolName" name="toolName"
          placeholder="Enter tool name" variant="primary" size="md" [readonly]="true">
        </ava-textbox>
      </div>
      <div class="col-md-6">
        <ava-textbox [formControl]="getOutputControl('toolClassName')" [label]="labels.toolClassName" id="toolClassName"
          name="toolClassName" placeholder="Enter tool class" variant="primary" size="md" [readonly]="true">
        </ava-textbox>
      </div>
      <div class="col-md-12">
        <ava-textarea id="description" name="description" [label]="labels.description" variant="primary"
          [formControl]="getOutputControl('description')" placeholder="Enter description" [rows]="4" size="sm"
          [fullWidth]="true" [readonly]="true">
        </ava-textarea>
      </div>
      <div class="col-md-12">
        <app-code-editor id="outputEditor" #codeEditor title="{{ labels.toolClassDefinition }}" language="python"
          [value]="getOutputControl('classDefinition').value" customCssClass="tools-monaco-editor"
          footerText="{{ labels.note }}" [readonly]="true">
        </app-code-editor>
      </div>
    </div>
  </form>
</app-ask-ava-wrapper>