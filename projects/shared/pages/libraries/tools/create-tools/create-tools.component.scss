// Main container styling
:host {
  height: 100vh;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.05) !important;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
}

.create-tools-container {
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

// Three panel layout
.three-panel-layout {
  display: flex;
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);
  gap: 20px;
  flex: 1;
  transition: all 0.3s ease;

  // When left panel is collapsed, adjust center panel container
  .left-panel.collapsed + .center-panel-container {
    flex: 1 1 auto;
  }

  // Ensure proper flex behavior for all panels
  .left-panel {
    flex-shrink: 0;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
    transition: all 0.3s ease;
  }

  .center-panel-container {
    flex: 1;
    min-width: 0;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
    transition: all 0.3s ease;
  }

  .right-panel {
    flex-shrink: 0;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
    transition: all 0.3s ease;
  }
}

// Common panel styles with glassy finish
.panel {
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.6) 100%
    ),
    linear-gradient(
      114deg,
      rgba(0, 150, 214, 0.04) 1.5%,
      rgba(255, 255, 255, 0.08) 25.71%,
      rgba(255, 255, 255, 0.2) 50.17%,
      rgba(255, 255, 255, 0.08) 73.42%,
      rgba(0, 150, 214, 0.04) 98.86%
    );
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    var(--Elevation-03-set-1-X, 0) 2px 2px
      var(--Elevation-03-set-1-Spread, -3px) var(--Brand-Neutral-n-50, #f0f1f2),
    var(--Elevation-03-set-2-X, 0) var(--Elevation-03-set-2-X, 0)
      var(--Elevation-03-set-2-Blur, 6px) var(--Elevation-03-set-2-Spread, -2px)
      var(--Elevation-03-set-2-Color, #d1d3d8);
  overflow: hidden;

  .panel-header {
    // background: rgba(255, 255, 255, 0.05);
    // backdrop-filter: blur(10px);
    // -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;

      .back-arrow {
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .panel-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--color-brand-primary);
    }

    .header-right {
      display: flex;
      align-items: center;

      .collapse-icon {
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: scale(1.1);
        }
      }
    }

    .panel-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }
  }

  .panel-content {
    flex: 1;
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: transparent;
  }
}

// Left Panel: Prompt Description (30% width)
.left-panel {
  width: 30%;
  min-width: 280px;
  max-width: 450px;
  transition: all 0.3s ease;
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);

  &.collapsed {
    width: 60px;
    min-width: 60px;
    max-width: 60px;

    .panel-content {
      opacity: 0;
      pointer-events: none;
    }

    .panel-title {
      opacity: 0;
    }

    .header-left {
      opacity: 0;
    }

    .header-right {
      opacity: 1;
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
    }

    .collapse-icon {
      transform: rotate(180deg);
    }
  }

  .form-field {
    margin-bottom: 20px;

    &:last-child {
      flex: 1;
      display: flex;
      flex-direction: column;

      ava-textarea {
        flex: 1;

        ::ng-deep .textarea-container {
          height: 100%;

          textarea {
            height: 100% !important;
            min-height: 200px;
            resize: vertical;
            border-radius: 8px;
            border: 1px solid #d1d5db;

            &:focus {
              border-color: #3b82f6;
              box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }
          }
        }
      }
    }
  }
}

// Center Panel: Tool Class Definition (40% width)
.center-panel-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  padding: 16px;
  background:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.6) 100%
    ),
    linear-gradient(
      114deg,
      rgba(0, 150, 214, 0.04) 1.5%,
      rgba(255, 255, 255, 0.08) 25.71%,
      rgba(255, 255, 255, 0.2) 50.17%,
      rgba(255, 255, 255, 0.08) 73.42%,
      rgba(0, 150, 214, 0.04) 98.86%
    );
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    var(--Elevation-03-set-1-X, 0) 2px 2px
      var(--Elevation-03-set-1-Spread, -3px) var(--Brand-Neutral-n-50, #f0f1f2),
    var(--Elevation-03-set-2-X, 0) var(--Elevation-03-set-2-X, 0)
      var(--Elevation-03-set-2-Blur, 6px) var(--Elevation-03-set-2-Spread, -2px)
      var(--Elevation-03-set-2-Color, #d1d3d8);
}

.center-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #0084ff14;
  border-radius: 8px;
  overflow: hidden;

  .header-pad {
    flex-shrink: 0;
    padding: 16px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .panel-header {
    background:
      linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.6) 0%,
        rgba(255, 255, 255, 0.6) 100%
      ),
      linear-gradient(
        114deg,
        rgba(0, 150, 214, 0.04) 1.5%,
        rgba(255, 255, 255, 0.08) 25.71%,
        rgba(255, 255, 255, 0.2) 50.17%,
        rgba(255, 255, 255, 0.08) 73.42%,
        rgba(0, 150, 214, 0.04) 98.86%
      );
    box-shadow:
      var(--Elevation-03-set-1-X, 0) 2px 2px
        var(--Elevation-03-set-1-Spread, -3px)
        var(--Brand-Neutral-n-50, #f0f1f2),
      var(--Elevation-03-set-2-X, 0) var(--Elevation-03-set-2-X, 0)
        var(--Elevation-03-set-2-Blur, 6px)
        var(--Elevation-03-set-2-Spread, -2px)
        var(--Elevation-03-set-2-Color, #d1d3d8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  .panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    overflow-y: auto;
    overflow-x: hidden;

    // Hide scrollbar for webkit browsers
    &::-webkit-scrollbar {
      display: none;
    }

    // Hide scrollbar for Firefox
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .code-editor-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 20px;
    margin-bottom: 16px;
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    min-height: 300px;

    app-code-editor {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 400px;
      background: rgba(255, 255, 255, 0.05);

      ::ng-deep {
        .code-editor-container {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .editor-header {
          background: transparent !important;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
          color: #ffffff !important;
        }

        .editor-wrapper {
          flex: 1;
          display: flex;
          flex-direction: column;
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor-container {
          flex: 1;
          min-height: 400px;
          background: transparent !important;
          padding: 16px;
          border: none !important;
        }

        .monaco-editor {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor .monaco-editor-background {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor .margin {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor .monaco-scrollable-element {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor .view-lines {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor .view-overlays {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .monaco-editor .decorationsOverviewRuler {
          background: rgba(255, 255, 255, 0.05) !important;
        }

        .editor-footer {
          flex-shrink: 0;
          padding: 7px 16px;
          background: rgba(255, 255, 255, 0.03);
          backdrop-filter: blur(8px);
          -webkit-backdrop-filter: blur(8px);
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .compiler-section {
    flex: 0 0 auto;
    margin: 0 20px 20px 20px;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    overflow: hidden;
    min-height: 150px;
    max-height: 500px;
    height: auto;
    transition: all 0.3s ease;

    &.has-content {
      min-height: 200px;
    }

    &.large-content {
      min-height: 300px;
    }

    &.extra-large-content {
      min-height: 400px;
      max-height: 500px;
    }

    .compiler-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: rgba(255, 255, 255, 0.03);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      .compiler-title {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
      }
    }

    .compiler-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-height: 0;

      app-code-editor {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: rgba(255, 255, 255, 0.05);

        ::ng-deep {
          .code-editor-container {
            background: rgba(255, 255, 255, 0.05) !important;
            flex: 1;
            display: flex;
            flex-direction: column;
          }

          .editor-header {
            background: transparent !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: #ffffff !important;
          }

          .editor-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
          }

          .monaco-editor-container {
            flex: 1;
            border: none !important;
            border-radius: 0;
            min-height: 170px;
            max-height: 400px;
            background: transparent !important;
            padding: 16px;
            overflow-y: auto;

            // Hide scrollbar for webkit browsers
            &::-webkit-scrollbar {
              display: none;
            }

            // Hide scrollbar for Firefox
            scrollbar-width: none;
            -ms-overflow-style: none;
          }

          .monaco-editor {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          .monaco-editor .monaco-editor-background {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          .monaco-editor .margin {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          .monaco-editor .monaco-scrollable-element {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          .monaco-editor .view-lines {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          .monaco-editor .view-overlays {
            background: rgba(255, 255, 255, 0.05) !important;
          }

          .monaco-editor .decorationsOverviewRuler {
            background: rgba(255, 255, 255, 0.05) !important;
          }
        }
      }
    }
  }
}

// Right Panel: Tool Playground (30% width)
.right-panel {
  width: 30%;
  min-width: 280px;
  max-width: 450px;
  height: calc(100vh - 40px);
  min-height: calc(100vh - 40px);

  .panel-content {
    padding: 0;
  }

  .playground-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    app-playground {
      flex: 1;
      height: 100%;

      ::ng-deep {
        .playground-wrapper {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .chat-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          min-height: 0;
        }

        .chat-messages {
          flex: 1;
          overflow-y: auto;
          padding: 16px;
        }

        .chat-input-container {
          flex-shrink: 0;
          padding: 16px;
          border-top: 1px solid #e1e5e9;
          background-color: #ffffff;
        }
      }
    }
  }
}

// Global styles for form elements
::ng-deep {
  // Textbox styling
  ava-textbox {
    .textbox-container {
      width: 100%;

      .textbox-label {
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .textbox-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        color: #ffffff;

        &:focus {
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
          outline: none;
        }

        &[readonly] {
          background: rgba(255, 255, 255, 0.03);
          opacity: 1;
        }
      }
    }
  }

  // Textarea styling
  ava-textarea {
    .textarea-container {
      width: 100%;

      .textarea-label {
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
        font-size: 14px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .textarea-input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        font-size: 14px;
        font-family: inherit;
        resize: vertical;
        transition: all 0.2s ease;
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        color: #ffffff;

        &:focus {
          border-color: rgba(255, 255, 255, 0.4);
          box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
          outline: none;
        }

        &[readonly] {
          background: rgba(255, 255, 255, 0.03);
          opacity: 1;
        }
      }
    }
  }

  // Button styling
  ava-button {
    .btn {
      font-size: 14px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.2s ease-in-out;
      padding: 8px 16px;

      &.btn-sm {
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 1600px) {
  .left-panel {
    width: 30%;
    min-width: 300px;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .right-panel {
    width: 30%;
    min-width: 300px;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .center-panel-container {
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .three-panel-layout {
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }
}

@media (max-width: 1400px) {
  .left-panel {
    width: 30%;
    min-width: 280px;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .right-panel {
    width: 30%;
    min-width: 280px;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .center-panel-container {
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .three-panel-layout {
    gap: 16px;
    height: calc(100vh - 40px);
    min-height: calc(100vh - 40px);
  }

  .panel .panel-content {
    padding: 16px;
  }

  .center-panel .code-editor-section {
    margin: 16px;
  }

  .center-panel .compiler-section {
    margin: 0 16px 16px 16px;
  }
}

@media (max-width: 1200px) {
  :host {
    padding: 16px;
  }

  .create-tools-container {
    height: calc(100vh - 32px);
    min-height: calc(100vh - 32px);
  }

  .left-panel {
    width: 30%;
    min-width: 250px;
    height: calc(100vh - 32px);
    min-height: calc(100vh - 32px);
  }

  .right-panel {
    width: 30%;
    min-width: 250px;
    height: calc(100vh - 32px);
    min-height: calc(100vh - 32px);
  }

  .center-panel-container {
    height: calc(100vh - 32px);
    min-height: calc(100vh - 32px);
  }

  .three-panel-layout {
    gap: 12px;
    height: calc(100vh - 32px);
    min-height: calc(100vh - 32px);
  }

  .panel .panel-content {
    padding: 14px;
  }

  .center-panel .code-editor-section {
    margin: 14px;
  }

  .center-panel .compiler-section {
    margin: 0 14px 14px 14px;
  }

  .panel-header {
    padding: 14px 16px;
  }

  .panel-title {
    font-size: 15px;
  }
}

@media (max-width: 992px) {
  :host {
    padding: 12px;
  }

  .three-panel-layout {
    flex-direction: column;
    height: auto;
    gap: 16px;
  }

  .left-panel,
  .right-panel {
    width: 100%;
    min-width: unset;
    max-width: unset;
  }

  .center-panel-container {
    width: 100%;
    flex: none;
  }

  .left-panel.collapsed {
    width: 100%;
    min-width: unset;

    .panel-content {
      opacity: 1;
      pointer-events: auto;
    }

    .panel-title {
      opacity: 1;
    }

    .header-right {
      opacity: 1;
    }
  }

  .panel .panel-content {
    padding: 16px;
  }

  .center-panel .code-editor-section {
    margin: 16px;
    min-height: 250px;
  }

  .center-panel .compiler-section {
    margin: 0 16px 16px 16px;
  }

  .panel-header {
    padding: 16px 20px;
  }

  .panel-title {
    font-size: 16px;
  }

  .panel-actions {
    gap: 6px;
  }

  .panel-actions ava-button {
    font-size: 12px;
    padding: 6px 10px;
  }
}

@media (max-width: 768px) {
  :host {
    padding: 8px;
  }

  .three-panel-layout {
    gap: 12px;
  }

  .panel .panel-content {
    padding: 12px;
  }

  .center-panel .code-editor-section {
    margin: 12px;
    min-height: 200px;
  }

  .center-panel .compiler-section {
    margin: 0 12px 12px 12px;
  }

  .panel-header {
    padding: 12px 16px;
  }

  .panel-title {
    font-size: 14px;
  }

  .panel-actions {
    gap: 4px;
  }

  .panel-actions ava-button {
    font-size: 11px;
    padding: 4px 8px;
  }

  .header-left,
  .header-right {
    i {
      font-size: 14px;
    }
  }

  // Adjust panel widths for smaller screens
  .left-panel,
  .right-panel {
    width: 100%;
    min-width: unset;
    max-width: unset;
  }

  .center-panel-container {
    width: 100%;
    flex: none;
  }

  // Form fields responsive
  ::ng-deep {
    ava-textbox .textbox-input,
    ava-textarea .textarea-input {
      padding: 10px 12px;
      font-size: 13px;
    }

    ava-textbox .textbox-label,
    ava-textarea .textarea-label {
      font-size: 13px;
    }
  }
}

@media (max-width: 576px) {
  :host {
    padding: 6px;
  }

  .three-panel-layout {
    gap: 8px;
  }

  .panel .panel-content {
    padding: 10px;
  }

  .center-panel .code-editor-section {
    margin: 10px;
    min-height: 180px;
  }

  .center-panel .compiler-section {
    margin: 0 10px 10px 10px;
  }

  .panel-header {
    padding: 10px 12px;
  }

  .panel-title {
    font-size: 13px;
  }

  .panel-actions {
    gap: 3px;
  }

  .panel-actions ava-button {
    font-size: 10px;
    padding: 3px 6px;
  }

  .header-left,
  .header-right {
    i {
      font-size: 12px;
    }
  }

  // Ensure full width on very small screens
  .left-panel,
  .right-panel {
    width: 100%;
    min-width: unset;
    max-width: unset;
  }

  .center-panel-container {
    width: 100%;
    flex: none;
  }

  // Form fields responsive
  ::ng-deep {
    ava-textbox .textbox-input,
    ava-textarea .textarea-input {
      padding: 8px 10px;
      font-size: 12px;
    }

    ava-textbox .textbox-label,
    ava-textarea .textarea-label {
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  :host {
    padding: 4px;
  }

  .three-panel-layout {
    gap: 6px;
  }

  .panel .panel-content {
    padding: 8px;
  }

  .center-panel .code-editor-section {
    margin: 8px;
    min-height: 150px;
  }

  .center-panel .compiler-section {
    margin: 0 8px 8px 8px;
  }

  .panel-header {
    padding: 8px 10px;
  }

  .panel-title {
    font-size: 12px;
  }

  .panel-actions {
    gap: 2px;
  }

  .panel-actions ava-button {
    font-size: 9px;
    padding: 2px 4px;
  }

  .header-left,
  .header-right {
    i {
      font-size: 11px;
    }
  }

  // Form fields responsive
  ::ng-deep {
    ava-textbox .textbox-input,
    ava-textarea .textarea-input {
      padding: 6px 8px;
      font-size: 11px;
    }

    ava-textbox .textbox-label,
    ava-textarea .textarea-label {
      font-size: 11px;
    }
  }
}

// Utility classes
.askava-container {
  width: 100%;

  &::ng-deep {
    label {
      text-align: start !important;
    }
  }
}

.generated-output__body {
  display: flex;
  flex-direction: column;
  row-gap: 1rem;
}

// Monaco editor inside pop was causing code to be placed on center
// so fixing issue by overriding CSS
#outputEditor::ng-deep {
  .monaco-editor .view-lines {
    justify-content: flex-start !important;
    text-align: left !important;
  }
  .monaco-editor .sticky-scroll {
    justify-content: flex-start !important;
    text-align: left !important;
    left: 0 !important;
  }
  .monaco-editor .sticky-widget-lines-scrollable {
    left: 85px !important;
    text-align: start;
  }
}
ava-textarea ::ng-deep .ava-textarea__container {
  background: white;
}

// Global monaco editor transparency override
::ng-deep {
  .monaco-editor,
  .monaco-editor-container,
  .monaco-editor .monaco-editor-background,
  .monaco-editor .margin,
  .monaco-editor .monaco-scrollable-element {
    border: none !important;
    outline: none !important;
  }
}
