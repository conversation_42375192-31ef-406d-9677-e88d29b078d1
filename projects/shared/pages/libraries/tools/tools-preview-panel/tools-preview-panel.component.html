<div class="preview-panel">
  <div class="backdrop" (click)="closePreview()"></div>
  <app-preview-panel class="panel-container" (click)="$event.stopPropagation()">
  <div panel-header class="preview-header">
    <span class="panel-title">Tool Information</span>
      <ava-icon iconName="x" iconColor="black" class="close-btn" (click)="closePreview()"></ava-icon>
  </div>
    
    <div panel-content class="preview-content" *ngIf="selectedTool">
      <h3>{{ selectedTool.modal?.title || selectedTool.title }}</h3>
      <div class="section-title">Tool Description</div>
      <div class="section-content">{{ selectedTool.modal?.appDescription || selectedTool.description }}</div>
      <div class="section-title" *ngIf="selectedTool.modal?.areaOfScope">Area of Scope</div>
      <div class="section-content" *ngIf="selectedTool.modal?.areaOfScope">{{ selectedTool.modal.areaOfScope }}</div>
      <div class="meta-row">
        <div>
          <div class="meta-label">Created by</div>
          <div class="meta-value">AAVA</div>
        </div>
        <div>
          <div class="meta-label">Created on</div>
          <div class="meta-value">12 May 2025</div>
        </div>
      </div>
    </div>
    
    <div panel-footer>
      <ava-button label="TRY NOW" variant="info" width="100%" (userClick)="onButtonClick($event)"></ava-button>
    </div>
  </app-preview-panel>
</div>