.preview-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden !important;
}


h3 {
  margin-top: 0;
  margin-bottom: 12px;
  color: #144692;
}

strong {
  color: #333;
  font-size: 14px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #161515;
  margin-top: 15px;
  margin-bottom: 8px;
}

.section-content {
  font-size: 0.85rem;
  color: #1b1b1b;
  margin-bottom: 18px;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  gap: 32px;

  .meta-label,
  .meta-value {
    font-size: 1rem;
    font-weight: 500;
    color: #111;
  }
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 56px;
  padding: 5px;
}

.panel-title {
  font-size: 2rem;
  font-weight: 700;
  color: #111;
  text-align: left;
  margin: 0;
  line-height: 1.2;
  flex: 1 1 auto;
}

.close-btn {
  font-size: 2rem;
  cursor: pointer;
  color: #111;
  background: none;
  border: none;
  line-height: 1;
  transition: color 0.2s;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 0 0 auto;
}

.close-btn ava-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 9999;
  cursor: pointer;
  overflow: hidden !important;
}

.panel-container {
  position: fixed;
  top: 0;
  right: 0;
  width: 500px;      
  height: 100vh;
  background-color: white;
  z-index: 10000;
  overflow: hidden !important;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.3);
}

.preview-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(90vh - 140px);  
  padding-top:40px;
  border-top: 1px solid #e5e7eb;
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }
}