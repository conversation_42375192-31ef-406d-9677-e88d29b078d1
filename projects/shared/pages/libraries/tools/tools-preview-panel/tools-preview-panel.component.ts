import { Component, Input, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TOOLS_DATA } from '../constants/builtInTools';
import { ButtonComponent, IconComponent } from '@ava/play-comp-library';
import { CommonModule } from '@angular/common';
import { PreviewPanelComponent } from '@shared/components/preview-panel/preview-panel.component';

@Component({
  selector: 'app-tools-preview-panel',
  imports: [
    ButtonComponent,
    IconComponent,
    CommonModule,
    PreviewPanelComponent,
  ],
  templateUrl: './tools-preview-panel.component.html',
  styleUrl: './tools-preview-panel.component.scss',
})
export class ToolsPreviewPanelComponent implements OnInit, OnDestroy {
  @Input() selectedTool: any;
  constructor(
    private router: Router,
    private route: ActivatedRoute,
  ) {}
  @Input() closePreview!: () => void;
  toolsData = TOOLS_DATA;

  ngOnInit(): void {
    this.disableBodyScroll();
  }

  ngOnDestroy(): void {
    this.enableBodyScroll();
  }

  private disableBodyScroll(): void {
    document.body.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
  }

  private enableBodyScroll(): void {
    document.body.style.overflow = '';
    document.body.style.position = '';
    document.body.style.width = '';
  }

  onButtonClick(event: any): void {
    this.onClose();
    this.router.navigate(['/build/agents', 'collaborative']);
  }

  onClose(): void {
    this.enableBodyScroll();
    this.closePreview();
  }
}
