import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, ViewContainerRef } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { PageFooterComponent } from '@shared/components/page-footer/page-footer.component';
import { PaginationService } from '@shared/services/pagination.service';
import { ToolsService } from '@shared/services/tools.service';
import {
  AvaTextboxComponent,
  DropdownOption,
  TextCardComponent,
  ButtonComponent,
  IconComponent,
  PopupComponent,
  DialogService,
} from '@ava/play-comp-library';
import { LucideAngularModule } from 'lucide-angular';
import toolsText from './constants/tools.json';
import { TOOLS_DATA } from './constants/builtInTools';
import { DrawerService } from '@shared/services/drawer/drawer.service';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { startWith, debounceTime, distinctUntilChanged, map } from 'rxjs';
import { ToolsPreviewPanelComponent } from './tools-preview-panel/tools-preview-panel.component';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';
import { ConsoleCardAction, ConsoleCardComponent } from "@shared/components/console-card/console-card.component";
import { TimeAgoPipe } from '@shared/pipes/time-ago.pipe';
import { DebouncedSearchService } from '@shared/services/debounced-search.service';

@Component({
  selector: 'app-tools',
  standalone: true,
  imports: [
    CommonModule,
    PageFooterComponent,
    TextCardComponent,
    AvaTextboxComponent,
    LucideAngularModule,
    ButtonComponent,
    IconComponent,
    ReactiveFormsModule,
    ConsoleCardComponent,
    TimeAgoPipe,
  ],
  providers: [DatePipe],
  templateUrl: './tools.component.html',
  styleUrl: './tools.component.scss',
})
export class ToolsComponent implements OnInit, OnDestroy {
  @ViewChild('drawerContainer', { read: ViewContainerRef })
  drawerContainer!: ViewContainerRef;

  allTools: any[] = [];
  builtInTools: any[] = [];
  userDefinedTools: any[] = [];
  filteredTools: any[] = [];
  displayedTools: any[] = [];
  isLoading: boolean = false;
  error: string | null = null;
  currentPage: number = 1;
  itemsPerPage: number = 12;
  totalUserDefinedTools: number = 0; // Total count from API for pagination

  // Delete popup properties
  showDeletePopup: boolean = false;
  toolToDelete: any = null;
  currentUserSignature: string = '';
  totalPages: number = 1;
  public labels: any = toolsText.labels;
  toolsOptions: DropdownOption[] = [
    { name: 'All', value: 'all' },
    { name: 'Type A', value: 'typeA' },
    { name: 'Type B', value: 'typeB' },
  ];
  selectedData: any = null;
  // Actions for user-defined tools (all actions)
  userDefinedActions: ConsoleCardAction[] = [
    {
      id: 'duplicate',
      icon: 'copy',
      label: 'Duplicate',
      tooltip: 'Duplicate',
    },
    {
      id: 'edit',
      icon: 'edit',
      label: 'Edit item',
      tooltip: 'Edit',
    },
    {
      id: 'delete',
      icon: 'trash',
      label: 'Delete item',
      tooltip: 'Delete',
    },
    {
      id: 'run',
      icon: 'play',
      label: 'Run application',
      tooltip: 'Run',
      isPrimary: true,
    },
  ];

  // Actions for built-in tools (only run action)
  builtInActions: ConsoleCardAction[] = [
    {
      id: 'run',
      icon: 'play',
      label: 'Run application',
      tooltip: 'Run',
      isPrimary: true,
    },
  ];
  searchForm!: FormGroup;
  search: any;
  playIconList = [{ name: 'play', iconName: 'play', cursor: true }];

  // Tool filter states
  currentFilter: 'builtin' | 'userdefined' = 'builtin';
  selectedToolFilter: 'built-in' | 'user-defined' = 'built-in';
  // Remove all preview panel related logic and state from this file
  cardSkeletonPlaceholders = Array(11);

  constructor(
    private paginationService: PaginationService,
    private router: Router,
    private route: ActivatedRoute,
    private toolsService: ToolsService,
    private datePipe: DatePipe,
    private drawerService: DrawerService,
    private fb: FormBuilder,
    private tokenStorage: TokenStorageService,
    private debouncedSearch: DebouncedSearchService,
    private dialogService: DialogService,
  ) {
    this.searchForm = this.fb.group({
      search: [''],
    });
  }

  private getUserSignature(): string {
    const userSignature = this.tokenStorage.getDaUsername() || '<EMAIL>';
    return userSignature;
  }

  ngOnInit(): void {
    this.loadBuiltInTools();
    this.getAllTools();
    // Initialize total count to ensure pagination shows up
    this.totalUserDefinedTools = 50; // Initial estimate, will be updated by API
    this.searchList();

    // Handle route parameter changes
    this.route.params.subscribe(params => {
      const section = params['section'];
      if (section === 'built-in') {
        this.showBuiltInToolsInternal();
      } else if (section === 'user-defined') {
        this.showUserDefinedToolsInternal();
      } else {
        // Default to built-in tools if no section specified
        this.router.navigate(['/libraries/tools/built-in']);
      }
    });
  }

  ngAfterViewInit(): void {
    // Register the drawer container with the drawer service
    this.drawerService.registerViewContainer(this.drawerContainer);
  }

  loadBuiltInTools(): void {
    // Load built-in tools from constant file
    this.builtInTools = TOOLS_DATA.map((tool: any) => ({
      ...tool,
      id: `builtin-${tool.id}`,
      title: tool.title,
      name: tool.title,
      description: tool.description,
      isBuiltIn: true,
      toolType: 'Built-in Tool',
      owner: 'AAVA',
      createdDate: '05/12/2025',
    }));
  }

  getAllTools(page: number = 1): void {
    // Prevent multiple simultaneous API calls
    if (this.isLoading) {
      return;
    }
    this.isLoading = true;
    this.toolsService.getUserToolsList(page).subscribe({
      next: (response: any) => {
        this.userDefinedTools = (response.userToolDetails || []).map((tool: any) => ({
          ...tool,
          id: tool.id.toString(),
          title: tool.name,
          name: tool.name || 'AAVA',
          description: tool.description,
          createdDate:
            this.datePipe.transform(tool.createdAt, 'MM/dd/yyyy') || '',
          isBuiltIn: false,
          toolType: 'User-defined Tool',
          isApproved: true, // Assuming all returned tools are approved since isDeleted=false in API
        }));
        // Store total count for pagination
        if (response.totalCount || response.total) {
          this.totalUserDefinedTools = response.totalCount || response.total;
        } else {
          // Estimate total count: if we got 11 items, there might be more pages
          const currentPageItems = this.userDefinedTools.length;
          if (currentPageItems === 11) {
            this.totalUserDefinedTools = Math.max(this.totalUserDefinedTools, page * 11 + 11);
          } else {
            this.totalUserDefinedTools = (page - 1) * 11 + currentPageItems;
          }
        }
        this.allTools = [...this.builtInTools, ...this.userDefinedTools];
        switch (this.currentFilter) {
          case 'builtin':
            this.filteredTools = [...this.builtInTools];
            break;
          case 'userdefined':
            this.filteredTools = [...this.userDefinedTools];
            break;
        }
        this.updateDisplayedTools();
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading tools:', error);
        this.isLoading = false;
        this.dialogService.error({
          title: 'Loading Failed',
          message: 'Failed to load tools. Please try again.',
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.getAllTools(page);
          }
        });
      }
    });
    const pageParam = this.route.snapshot.queryParamMap.get('page');
    if (pageParam) {
      this.currentPage = parseInt(pageParam, 10);
    }
  }

  searchList(): void {

    this.searchForm.get('search')?.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged()
    ).subscribe((searchText: string) => {
      const trimmed = searchText?.trim() || '';
      const currentFilter = this.currentFilter;

      if (currentFilter === 'userdefined') {
        if (trimmed) {
          this.isLoading = true;

          this.debouncedSearch.triggerSearch(trimmed, 'tools');
        } else {
          // If search cleared, re-fetch all user-defined tools
          this.filteredTools = [];
          this.displayedTools = [];
          this.currentPage = 1;
          this.getAllTools(); // custom API fetch
        }
        return;
      }

      // For builtin tools
      const toolsToSearch = [...this.builtInTools];

      if (!trimmed) {
        // If search cleared, reset list
        this.filteredTools = [...toolsToSearch];
        this.currentPage = 1;
        this.totalPages = Math.ceil(this.filteredTools.length / this.itemsPerPage);
        this.updateDisplayedTools();
      } else {
        this.filteredTools = toolsToSearch.filter(tool =>
          tool.name?.toLowerCase().includes(trimmed.toLowerCase()) ||
          tool.description?.toLowerCase().includes(trimmed.toLowerCase())
        );
        this.currentPage = 1;
        this.totalPages = Math.ceil(this.filteredTools.length / this.itemsPerPage);
        this.updateDisplayedTools();

      }
    });

    // API result subscription for userdefined
    this.debouncedSearch.searchResults$.subscribe((results: any) => {
      const toolsArray = Array.isArray(results?.userToolDetails) ? results.userToolDetails : [];

      this.filteredTools = toolsArray.map((tool: any) => ({
        id: tool.id?.toString() || '',
        title: tool.name || 'Untitled',
        name: tool.name || 'AAVA',
        description: tool.description || 'No description',
        createdDate: this.datePipe.transform(tool.createdAt, 'MM/dd/yyyy') || 'N/A',
        isBuiltIn: tool.isBuiltIn || false,
        toolType: tool.isBuiltIn ? 'Built-in Tool' : 'User-defined Tool',
        owner: tool.createdBy || 'AAVA',
      }));
      this.updateDisplayedTools();
      this.isLoading = false;

    });
  }

  updateDisplayedTools(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;

    if (this.currentFilter === 'builtin') {
      this.displayedTools = this.filteredTools.slice(startIndex, endIndex);
      this.totalPages = Math.ceil(this.filteredTools.length / this.itemsPerPage);

    } else if (this.currentFilter === 'userdefined') {
      // If search term is present, apply pagination locally
      const searchTerm = this.searchForm.get('search')?.value?.trim();
      if (searchTerm) {
        this.displayedTools = this.filteredTools.slice(startIndex, endIndex);
        this.totalPages = Math.ceil(this.filteredTools.length / this.itemsPerPage);
      } else {
        // No search, so use server-paginated data directly
        this.displayedTools = this.filteredTools;
        this.totalPages = Math.ceil(this.totalUserDefinedTools / this.itemsPerPage);
      }

    } else if (this.currentFilter === 'all') {
      const searchTerm = this.searchForm.get('search')?.value?.trim();
      let combinedTools = [];

      if (searchTerm) {
        const builtInMatches = this.builtInTools.filter(tool =>
          tool.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        combinedTools = [...builtInMatches, ...this.filteredTools];
      } else {
        combinedTools = [...this.builtInTools, ...this.filteredTools];
      }

      this.displayedTools = combinedTools.slice(startIndex, endIndex);
      this.totalPages = Math.ceil(combinedTools.length / this.itemsPerPage);
    }
  }

  onCreateTool(): void {
    this.router.navigate(['/libraries/tools/create']);
  }

  onCardClicked(toolId: string): void {
    const tool = this.filteredTools.find((t) => t.id === toolId);
    if (tool && tool.isBuiltIn) {
      // For built-in tools, open the drawer (preview panel) instead of navigating
      const builtInTool = TOOLS_DATA.find(
        (t) => t.id === tool.id.replace('builtin-', ''),
      );
      this.drawerService.open(ToolsPreviewPanelComponent, {
        selectedTool: builtInTool,
        closePreview: () => this.drawerService.clear(),
      });
      return;
    } else if (tool) {
      this.router.navigate(['/libraries/tools/edit', tool.id], {
        queryParams: { returnPage: this.currentPage }
      });
    }
  }

  // Add a method for viewing a tool, similar to edit
  onViewTool(toolId: string): void {
    const tool = this.filteredTools.find((t) => t.id === toolId);
    if (tool) {
      this.router.navigate(['/libraries/tools/view', tool.id], {
        queryParams: { returnPage: this.currentPage }
      });
    }
  }

  getHeaderIcons(tool: any): { iconName: string; title: string }[] {
    // Always return at least one icon for all tool types
    const icons = [
      { iconName: 'wrench', title: tool.toolType || 'Tool' },
      {
        iconName: 'users',
        title: tool.userCount ? String(tool.userCount) : '120',
      },
    ];
    return icons;
  }

  getFooterIcons(tool: any): { iconName: string; title: string }[] {
    // Always return at least one icon for all tool types
    const icons = [
      { iconName: 'user', title: tool.owner || 'AAVA' },
      {
        iconName: 'calendar-days',
        title: tool.createdDate || '05/12/2025',
      },
    ];
    return icons;
  }

  onActionClick(
    event: { actionId: string; action: ConsoleCardAction },
    toolId: string,
  ): void {
    let tool = this.filteredTools.find((t) => t.id === toolId);
    if ((event.actionId) === 'run') {
      if (tool && tool.isBuiltIn) {
        // Only open drawer for built-in tools
        const builtInTool = TOOLS_DATA.find(
          (t) => t.id === tool.id.replace('builtin-', ''),
        );
        this.drawerService.open(ToolsPreviewPanelComponent, {
          selectedTool: builtInTool,
          closePreview: () => {
            this.drawerService.clear();
          },
        });
      } else {
        // For user-defined tools, navigate to execute page
        this.executeTool(toolId);
      }
      return;
    }
    
    if (!tool || tool.isBuiltIn) return;
    
    switch (event.actionId) {
      case 'delete':
        this.confirmDeleteTool(toolId);
        break;
      case 'run':
        this.executeTool(toolId);
        break;
      case 'duplicate':
        this.cloneTool(toolId);
        break;
      case 'edit':
        this.edittool(toolId);
        break;
      default:
        break;
    }
  }

  handleBuiltinToolsCardClick(toolId: string): void {
    const builtInTool = TOOLS_DATA.find(
      (t) => t.id === toolId
      .replace('builtin-', ''),
    );
    this.drawerService.open(ToolsPreviewPanelComponent, {
      selectedTool: builtInTool,
      closePreview: () => {
        this.drawerService.clear();
      },
    });
  }

  edittool(toolId: string): void {
    this.router.navigate(['/libraries/tools/edit', toolId], {
      queryParams: { returnPage: this.currentPage },
    });
  }

  confirmDeleteTool(toolId: string): void {
    const tool = this.allTools.find(item => item.id === toolId);
    if (!tool || tool.isBuiltIn) return;

    this.dialogService.confirmation({
      title: 'Delete Tool',
      message: `Are you sure you want to delete "${tool.title}"? This action cannot be undone.`,
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel',
      confirmButtonVariant: 'danger',
      icon:'trash'
    }).then(result => {
      if (result.confirmed) {
        this.deleteTool(toolId);
      }
    });
  }

  deleteTool(toolId: string): void {
    const currentUserSignature = this.getUserSignature();
    
    // Show loading dialog
    this.dialogService.loading({
      title: 'Deleting Tool...',
      message: 'Please wait while we delete the tool.',
      showProgress: false,
      showCancelButton: false
    });

    this.toolsService.deleteTool(parseInt(toolId), currentUserSignature).subscribe({
      next: (response) => {
        this.dialogService.close(); // Close loading dialog
        
        this.dialogService.success({
          title: 'Tool Deleted',
          message: 'Tool has been deleted successfully.'
        });

        // Remove the tool from local arrays
        this.userDefinedTools = this.userDefinedTools.filter(tool => tool.id !== toolId);
        this.allTools = this.allTools.filter(tool => tool.id !== toolId);
        this.filteredTools = this.filteredTools.filter(tool => tool.id !== toolId);
        this.updateDisplayedTools();
      },
      error: (err) => {
        this.dialogService.close(); // Close loading dialog
        
        const errorMessage = err?.error?.message || err?.message || 'Failed to delete tool. Please try again.';
        this.dialogService.error({
          title: 'Delete Failed',
          message: errorMessage,
          showRetryButton: true,
          retryButtonText: 'Retry'
        }).then(result => {
          if (result.action === 'retry') {
            this.deleteTool(toolId);
          }
        });
      },
    });
  }

  // closeDeletePopup(): void {
  //   this.showDeletePopup = false;
  //   this.toolToDelete = null;
  // }

  // duplicateTool(toolId: string): void {
  //   // Implement duplicate logic
  // }

  executeTool(toolId: string): void {
    this.router.navigate(['/libraries/tools/execute', toolId], {
      queryParams: { returnPage: this.currentPage },
    });
  }

  cloneTool(toolId: string): void {
    this.router.navigate(['/libraries/tools/clone', toolId]);
  }

  onSelectionChange(data: any) {
    this.selectedData = data;
    // Implement filter logic if needed
  }

  onPageChange(page: number): void {
    this.currentPage = page;

    const searchTerm = this.searchForm.get('search')?.value?.trim();

    if (searchTerm && this.currentFilter === 'userdefined') {
      //  Dynamic search: apply client-side pagination on filtered results
      this.paginateFilteredTools();
    } else if (this.currentFilter === 'userdefined') {
      //  Normal API fetch (even though pagination isn't supported, reload everything if needed)
      this.getAllTools(page);
    } else {
      //  Built-in tools: use local pagination
      this.updateDisplayedTools();
    }
  }

  paginateFilteredTools(): void {
    const start = (this.currentPage - 1) * this.itemsPerPage;
    const end = start + this.itemsPerPage;
    this.displayedTools = this.filteredTools.slice(start, end);
  }

  get showCreateCard(): boolean {
    // Only show create card for user-defined tools or all tools on first page
    return (
      this.currentPage === 1 &&
      !this.isLoading &&
      !this.error &&
      (this.currentFilter === 'userdefined')
    );
  }

  getTotalItemsForPagination(): number {
    const searchTerm = this.searchForm.get('search')?.value?.trim();
    let totalItems: number;

    if (searchTerm && this.currentFilter == 'userdefined') {
      //  When dynamic search is active, count only filtered search results
      totalItems = this.filteredTools.length + 1; // +1 for create card
    } else {
      switch (this.currentFilter) {
        case 'builtin':
          totalItems = this.builtInTools.length; // No create card for built-in tools
          break;
        case 'userdefined':
          if (this.userDefinedTools.length === 11) {
            totalItems = Math.max(this.totalUserDefinedTools, this.currentPage * 11 + 11) + 1;
          } else {
            totalItems = this.totalUserDefinedTools + 1;
          }
          break;
        default:
          totalItems = this.filteredTools.length + 1;
      }
    }

    return totalItems;
  }

  showBuiltInTools(): void {
    // Navigate to built-in tools path using parameterized route
    this.router.navigate(['/libraries/tools', 'built-in']);
  }
  
  private showBuiltInToolsInternal(): void {
    this.selectedToolFilter = 'built-in';
    this.currentFilter = 'builtin';
    this.currentPage = 1; // Reset to first page
    this.filteredTools = [...this.builtInTools];
    this.updateDisplayedTools();
  }

  showUserDefinedTools(): void {
    // Navigate to user-defined tools path using parameterized route
    this.router.navigate(['/libraries/tools', 'user-defined']);
  }
  
  private showUserDefinedToolsInternal(): void {
    this.selectedToolFilter = 'user-defined';
    this.currentFilter = 'userdefined';
    this.currentPage = 1; // Reset to first page
    this.filteredTools = [...this.userDefinedTools];
    this.updateDisplayedTools();
  }

  // Legacy method for backward compatibility
  showbuiltinTools(): void {
    this.showBuiltInTools();
  }

  onButtonClick(event: any) {
    //Implement your logic here
  }

  ngOnDestroy(): void {
    this.searchForm.get('search')?.setValue('', { emitEvent: false });
    // Reset filtered tools
    this.filteredTools = [];

    // Reset displayed tools
    this.displayedTools = [];

    // Reset pagination state
    this.currentPage = 1;
  }
}
