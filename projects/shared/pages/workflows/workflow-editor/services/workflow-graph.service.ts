import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

// Node and Edge interfaces for the workflow graph
export interface WorkflowNode {
  id: string;
  type: string;
  data: {
    label: string;
    agentId?: string;
    agentName?: string;
    agentDescription?: string;
    width?: number;
    [key: string]: any;
  };
  position: {
    x: number;
    y: number;
  };
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
  animated?: boolean;
  label?: string;
}

@Injectable({
  providedIn: 'root'
})
export class WorkflowGraphService {
  private nodesSubject = new BehaviorSubject<WorkflowNode[]>([]);
  private edgesSubject = new BehaviorSubject<WorkflowEdge[]>([]);

  nodes$: Observable<WorkflowNode[]> = this.nodesSubject.asObservable();
  edges$: Observable<WorkflowEdge[]> = this.edgesSubject.asObservable();

  constructor() { }

  getAllNodes(){
    return this.nodesSubject.value
  }

  // Add a new node to the workflow
  addNode(node: WorkflowNode): void {
    const currentNodes = this.nodesSubject.getValue();
    this.nodesSubject.next([...currentNodes, node]);
  }

  // Remove a node from the workflow
  removeNode(nodeId: string): void {
    const currentNodes = this.nodesSubject.getValue();
    const currentEdges = this.edgesSubject.getValue();

    // Remove the node
    const updatedNodes = currentNodes.filter(node => node.id !== nodeId);

    // Remove any edges connected to this node
    const updatedEdges = currentEdges.filter(
      edge => edge.source !== nodeId && edge.target !== nodeId
    );

    this.nodesSubject.next(updatedNodes);
    this.edgesSubject.next(updatedEdges);
  }

  // Add a new edge connecting two nodes
  addEdge(edge: WorkflowEdge): void {
    const currentEdges = this.edgesSubject.getValue();
    this.edgesSubject.next([...currentEdges, edge]);
  }

  // Remove an edge from the workflow
  removeEdge(edgeId: string): void {
    const currentEdges = this.edgesSubject.getValue();
    const updatedEdges = currentEdges.filter(edge => edge.id !== edgeId);
    this.edgesSubject.next(updatedEdges);
  }

  // Update node positions after drag
  updateNodePositions(updatedNodes: WorkflowNode[]): void {
    this.nodesSubject.next(updatedNodes);
  }

  // Clear the entire workflow
  clearWorkflow(): void {
    this.nodesSubject.next([]);
    this.edgesSubject.next([]);
  }

  // Load a saved workflow
  loadWorkflow(nodes: WorkflowNode[], edges: WorkflowEdge[]): void {
    this.nodesSubject.next(nodes);
    this.edgesSubject.next(edges);
  }

  // Get the current state of the workflow
  getWorkflowState(): { nodes: WorkflowNode[], edges: WorkflowEdge[] } {
    return {
      nodes: this.nodesSubject.getValue(),
      edges: this.edgesSubject.getValue()
    };
  }

  // Generate a unique node ID
  generateNodeId(): string {
    return `node_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  // Generate a unique edge ID
  generateEdgeId(source: string, target: string): string {
    return `edge_${source}_${target}_${Math.floor(Math.random() * 1000)}`;
  }

  // Set nodes (for undo/redo functionality)
  setNodes(nodes: WorkflowNode[]): void {
    this.nodesSubject.next([...nodes]);
  }

  // Set edges (for undo/redo functionality)
  setEdges(edges: WorkflowEdge[]): void {
    this.edgesSubject.next([...edges]);
  }

  getAllEdges(){
    return this.edgesSubject.value
  }
  
}
