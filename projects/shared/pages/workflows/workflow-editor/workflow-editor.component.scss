// Utility mixin for hiding scrollbars while keeping scroll functionality
@mixin hide-scrollbar {
  // Hide scrollbar for webkit browsers (Chrome, Safari, Edge)
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }

  // Hide scrollbar for Firefox
  scrollbar-width: none;

  // For Internet Explorer and Edge legacy
  -ms-overflow-style: none;
}

.workflow-editor-container {
  height: calc(100vh - 90px);
  border: 1px solid var(--build-agents-nav-border);
  margin: 0 1rem;
  display: flex;
  flex-direction: column;

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .nav-item {
        font-size: 14px;
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: var(--text-primary);
        }

        &.active {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .separator {
        color: var(--text-tertiary);
        font-size: 14px;
      }

      .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        border-radius: 8px;
        margin-left: 16px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--hover-bg);
          color: var(--text-primary);
        }
      }
    }

    .header-actions {
      .action-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-btn {
          background: none;
          border: 1px solid var(--border-color);
          color: var(--text-secondary);
          cursor: pointer;
          padding: 8px;
          border-radius: 8px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: var(--hover-bg);
            border-color: var(--border-hover);
            color: var(--text-primary);
          }
        }

        .run-btn {
          background: var(--dashboard-primary);
          border: 1px solid var(--dashboard-primary);
          color: white;
          cursor: pointer;
          padding: 8px 16px;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--dashboard-primary-hover);
            border-color: var(--dashboard-primary-hover);
          }
        }
      }
    }
  }

  .delete-btn {
    position: absolute;
    top: 40px;
    right: 25px;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--agents-preview-required);
    border: none;
    border-radius: 50%;
    padding: 0;
    color: white;
    cursor: pointer;
    z-index: 999;
    transition: all 0.2s ease;
  }

  .delete-btn:hover {
    background-color: var(--agents-preview-required);
  }

  .llm-settings {
    margin-top: 24px;
    overflow-y: auto;
    max-height: 348px;
    box-shadow: 0px 4px 12px 0px #00000040;
    background-color: white;
    padding: 16px 15px;
    border-radius: 8px;

    .setting-item {
      margin: 10px 0 24px 0;

      h3 {
        margin-top: 0;
        margin-bottom: 12px;
        font-size: 16px;
        font-weight: 500;
        color: var(--text-color);
      }

      .slider-with-value {
        display: flex;
        align-items: center;
        gap: 12px;

        .setting-slider {
          flex-grow: 1;
          height: 6px;
          -webkit-appearance: none;
          appearance: none;
          background: var(--agent-slider-bg);
          border-radius: 3px;
          outline: none;

          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--agent-slider-thumb-bg);
            cursor: pointer;
            box-shadow: var(--agent-slider-thumb-shadow);
          }

          &::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--agent-slider-thumb-bg);
            cursor: pointer;
            border: none;
            box-shadow: var(--agent-slider-thumb-shadow);
          }
        }

        .value-display {
          min-width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          color: var(--form-input-color);
          background: var(--form-input-bg);
          border-radius: 6px;
          padding: 0 10px;
          border: 1px solid var(--form-input-border);
        }
      }

      .setting-input {
        width: 100%;
        padding: 10px;
        border: 1px solid var(--form-input-border);
        border-radius: 6px;
        font-size: 14px;
        background-color: var(--form-input-bg);
        color: var(--form-input-color);
      }

      .token-container {
        position: relative;

        .tokens-used {
          position: absolute;
          right: 0;
          top: -20px;
          font-size: 12px;
          color: var(--text-secondary);
        }
      }
    }
  }

  .llm-toggle {
    width: 100%;
  }

  .main-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;

    .canvas-area {
      flex: 1;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;

      // Agent Library Floating Panel - Positioned on canvas area
      .agent-library-panel {
        position: absolute;
        width: 380px;
        height: calc(100vh - 90px);
        background: var(--build-agents-nav-bg);
        border: 1px solid var(--build-agents-panel-border);
        border-top: none;
        box-shadow: var(--build-agents-panel-shadow);
        z-index: 20;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        display: flex;
        flex-direction: column;
        &.collapsed {
          width: auto;
          min-width: 200px;
          height: auto;

          .panel-header {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: none;

            h3 {
              margin: 0;
              font-size: 14px;
              font-weight: 600;
              color: var(--text-primary);
            }

            ava-icon {
              transition: transform 0.3s ease;
            }
          }

          .panel-content {
            display: none;
          }
        }

        .panel-header {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          transition: all 0.3s ease;
          flex-shrink: 0;

          h3 {
            margin: 0;
            color: var(--build-agents-toggle-btn-text);
            font-size: 24px;
            font-weight: 700;
          }

          ava-icon {
            transition: transform 0.3s ease;
          }
        }

        .panel-content {
          margin-top: 1rem;
          flex: 1;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          flex-direction: column;

          &.hidden {
            max-height: 0;
            opacity: 0;
          }

          .search-section {
            form {
              width: 100%;
            }

            // Search input styling
            ::ng-deep ava-textbox {
              .textbox-container {
                border-radius: 8px;
                border: 1px solid var(----build-agents-floater-border);
                background-color: var(--build-agents-floater-header-hover);

                &:focus-within {
                  border-color: var(--build-agents-tab-active-bg);
                  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }

                input {
                  background-color: transparent;
                  color: var(--build-agents-tab-active-label);
                  font-size: 14px;

                  &::placeholder {
                    color: var(--build-agents-search-placeholder);
                  }
                }
              }
            }
          }

          .agents-list {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            @include hide-scrollbar;
            margin-bottom: 16px;

            .agent-item {
              border: 1px solid var(--build-agents-tool-border);
              background-color: var(--build-agents-tool-bg);
              border-radius: 8px;
              padding: 16px;
              margin-top: 16px;
              cursor: grab;
              transition: all 0.2s ease;
              box-shadow: var(--build-agents-tool-shadow);
              box-shadow: var(--build-agents-tool-shadow-alt);

              &:hover {
                border-color: var(--build-agents-tab-active-border);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transform: translateY(-1px);
              }

              .agent-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 8px;

                .agent-icon-box {
                  background: var(--pagination-primary-bg);
                  border-radius: 999px;
                  width: 36px;
                  height: 36px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex-shrink: 0;
                }

                .agent-name {
                  margin: 0;
                  font-size: 16px;
                  font-weight: 600;
                  color: var(--build-agents-tool-name) !important;
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .agent-count {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                  font-size: 13px;
                  color: var(--page-footer-text-secondary);

                  .count-text {
                    font-weight: 600;
                    font-size: 16px;
                    color: var(--Text-Title, #14161f);
                  }
                }
              }

              .agent-description {
                margin: 0 0 16px 0;
                font-size: 14px;
                font-weight: 600;
                color: var(--build-agents-toggle-btn-text);
              }

              .agent-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 6px;
                margin-bottom: 12px;

                .agent-tag {
                  background-color: var(--build-agents-panel-border-light);
                  color: var(--build-agents-field-label);
                  font-size: 12px;
                  padding: 4px 8px;
                  border-radius: 8px;
                  font-weight: 500;
                }
              }

              .agent-actions {
                display: flex;
                justify-content: flex-end;

                .preview-btn {
                  ::ng-deep .button-container {
                    border-radius: 8px;
                    font-size: 12px;
                    padding: 6px 12px;
                  }
                }
              }
            }

            .no-results-message {
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 2rem 1rem;
              text-align: center;

              .no-results-content {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;

                p {
                  margin: 0;
                  color: var(--page-footer-text-secondary);
                  font-size: 14px;
                }
              }
            }
          }

          .create-agent-section {
            background-color: white;
            flex-shrink: 0;

            ::ng-deep ava-button {
              .button-container {
                border-radius: 8px;
                background: var(--build-agents-create-gradient);
                border: none;
                font-weight: 600;
                font-size: 14px;
                padding: 12px 20px;

                &:hover {
                  background: var(--build-agents-create-hover-gradient);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
                }
              }
            }
          }
        }
      }

      // Action Buttons Section - Positioned on the right
      .action-buttons-section {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;

        .action-group {
          display: flex;
          align-items: center;
          gap: 8px;

          .action-btn {
            background: white;
            border: 1px solid var(--build-agents-floater-border);
            color: var(--text-secondary);
            cursor: pointer;
            padding: 8px;
            border-radius: 8px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            &:hover {
              background-color: var(--build-agents-floater-header-hover);
              border-color: var(--build-agents-tab-active-bg);
              color: var(--text-primary);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
          }

          .run-btn {
            background: var(--dashboard-primary);
            border: 1px solid var(--dashboard-primary);
            color: white;
            cursor: pointer;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

            &:hover {
              background: var(--dashboard-primary-hover);
              border-color: var(--dashboard-primary-hover);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }

      // Workflow Editor Canvas
      .editor-canvas {
        width: 100%;
        height: 100%;
        background: var(--build-agents-nav-bg);
        position: relative;
        overflow: hidden;

        .sidebar-section {
          // padding: 20px;
          height: 100%;

          h3 {
            margin-top: 0;
            margin-bottom: 12px;
            font-size: 16px;
            font-weight: 500;
            color: var(--text-color);

            &.description-label {
              margin-top: 20px;
            }
          }
        }

        // Canvas board styling
        ::ng-deep app-canvas-board {
          width: 100%;
          height: 100%;

          // Override header positioning to place beside Agent Library panel
          .header-inputs-section {
            position: absolute;
            top: 20px;
            left: 420px; // Position after Agent Library Panel (380px + 40px gap)
            z-index: 10;
            max-width: calc(100% - 440px); // Prevent overflow
            overflow: visible;

            .header-inputs-container {
              display: flex;
              align-items: center;
              gap: 16px;
              flex-wrap: nowrap;
            }
          }

          // LLM Toggle Container styling
          .llm-toggle-container {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background-color: white;
            border: 1px solid var(--build-agents-floater-border);
            border-radius: 8px;
            margin-left: 16px;

            .toggle-container {
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 12px;
              width: 100%;

              .toggle-label {
                font-size: 14px;
                font-weight: 500;
                color: var(--build-agents-tab-active-label);
                white-space: nowrap;
                cursor: pointer;
              }

              // Toggle styling
              ::ng-deep ava-toggle {
                .toggle-container {
                  border-radius: 8px;
                }
              }
            }
          }
        }
      }
    }
  }
}

.llm-actions {
  display: flex;
  justify-content: flex-end;
}

.model-info {
  position: absolute;
  width: fit-content;
  margin-top: 20px;
  height: 51px;
  opacity: 1;
  top: 50px;
  left: 933px;
  border-radius: 8px;
  border-width: 1px;
  padding-top: 8px;
  padding-right: 16px;
  padding-bottom: 8px;
  padding-left: 16px;
  gap: 16px;
  background-color: white;
  left: 50%;
  transform: translateX(-50%);
  border: 1px solid #dadce7;
}

::ng-deep .agent-icon-box svg {
  stroke: var(--console-card-primary-title);
}

::ng-deep .agent-actions .ava-button.secondary {
  border-radius: 999px !important;
  background-color: var(--custom-tabs-active-bg) !important;
  border: none !important;
}

::ng-deep .agent-count svg {
  stroke: var(--svg-icon-color) !important;
}

::ng-deep .ava-button.primary.ava-button--glass-10 {
  background: var(--build-agents-toggle-active-bg) !important;
}
