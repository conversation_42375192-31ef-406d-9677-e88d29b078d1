import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';
import { ChangeDetectorRef } from '@angular/core';
import { WorkflowEditorComponent } from './workflow-editor.component';
import { WorkflowService } from '@shared/services/workflow.service';

describe('WorkflowEditorComponent', () => {
  let component: WorkflowEditorComponent;
  let fixture: ComponentFixture<WorkflowEditorComponent>;
  let mockWorkflowService: any;
  let cdrSpy: jasmine.SpyObj<ChangeDetectorRef>;

  beforeEach(() => {
    mockWorkflowService = {
      getDropdownList: jasmine.createSpy(),
    };

    cdrSpy = jasmine.createSpyObj('ChangeDetectorRef', ['detectChanges']);

    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      declarations: [WorkflowEditorComponent],
      providers: [
        FormBuilder,
        { provide: WorkflowService, useValue: mockWorkflowService },
        { provide: ChangeDetectorRef, useValue: cdrSpy },
      ],
    });

    fixture = TestBed.createComponent(WorkflowEditorComponent);
    component = fixture.componentInstance;

    // Set up dummy data
    component.filterLabels = ['organization', 'domain', 'project', 'team'];
    component.levels = ['organization', 'domain', 'project', 'team'];
    component.workflowForm = new FormBuilder().group({
      organization: [''],
      team: [''],
      project: [''],
      domain: [''],
    });
    component.levelOptionsMap = {};

    fixture.detectChanges();
  });

  it('should set value to correct form control and fetch child options', () => {
    const mockOptions = [{ value: '1', label: 'Child Option' }];
    mockWorkflowService.getDropdownList.and.returnValue(of(mockOptions));

    component.onLevelChange(0, '101');

    expect(component.workflowForm.get('team')?.value).toBe('101');
    expect(component.workflowForm.get('project')?.value).toBeNull();
    expect(component.levelOptionsMap[1]).toEqual(mockOptions);
  });

  it('should not proceed if selected value is empty', () => {
    spyOn(component, 'fetchChildOptions');
    component.onLevelChange(0, '');
    expect(component.workflowForm.get('team')?.value).toBe('');
    expect(component.fetchChildOptions).not.toHaveBeenCalled();
  });

  it('should reset lower levels and detect changes', () => {
    component.workflowForm.get('project')?.setValue('xyz');
    component.levelOptionsMap[1] = [{ value: 'xyz', label: 'Test' }];

    component.resetControlAtLevel(1);

    expect(component.workflowForm.get('project')?.value).toBeNull();
    expect(component.levelOptionsMap[1]).toEqual([
      { value: 'xyz', label: 'Test' },
    ]);
    expect(cdrSpy.detectChanges).toHaveBeenCalled();
  });

  it('should handle error while fetching child options', () => {
    mockWorkflowService.getDropdownList.and.returnValue(
      throwError(() => new Error('error')),
    );

    component.fetchChildOptions(1, 123);

    expect(component.levelOptionsMap[1]).toEqual([]);
  });

  it('should return empty array if no options found for a level', () => {
    expect(component.getOptionsForLevel(5)).toEqual([]);
  });
});
