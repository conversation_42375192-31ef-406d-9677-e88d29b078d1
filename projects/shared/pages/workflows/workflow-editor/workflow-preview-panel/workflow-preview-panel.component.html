<div class="preview-panel">
  <div class="backdrop" (click)="closePreview()"></div>
  <app-preview-panel  class="panel-container" [divider]="false" (click)="$event.stopPropagation()">
    <div panel-header class="preview-header">
      <span class="panel-title">Metadata Information</span>
      <ava-icon iconName="x" iconColor="black" class="close-btn" (click)="closePreview()"></ava-icon>
    </div>
    <div panel-content class="preview-content">
      <!-- Content based on preview data -->
      <div *ngIf="previewData?.data">
        <!-- Agent Preview -->
        <div *ngIf="previewData?.type === 'agent'" class="agent-preview">
          <div class="agent-section">
            <h3 class="headerName">Agent Name</h3>
            <div class="agent-name">{{ previewData.data.name }}</div>
          </div>

          <div class="agent-section">
            <h3 class="headerName">Description</h3>
            <div class="agent-description">{{ previewData.data.description || previewData.data.agentDetails }}</div>
          </div>

          <div class="agent-meta">
            <div class="meta-row">
              <div class="meta-item">
                <label>Created by</label>
                <div class="meta-value">{{ previewData.data.createdBy || 'System' }}</div>
              </div>
              <div class="meta-item">
                <label>Created on</label>
                <div class="meta-value">{{ formatDate(previewData.data.createdAt) || 'N/A' }}</div>
              </div>
            </div>
          </div>

          <div class="agent-section">
            <h3 class="headerName">Agent Configuration</h3>
            
            <!-- Prompt Accordion -->
            <ava-accordion
              [expanded]="false"
              [animation]="true"
              [controlled]="false"
              iconClosed="chevron-down"
              iconOpen="chevron-up"
              iconPosition="right"
              type="default"
            >
              <div header>
                <div class="config-header-content">
                  <ava-icon iconName="FileText" iconSize="20" iconColor="#14161F"></ava-icon>
                  <span class="config-label">Prompt</span>
                </div>
              </div>
              <div content>
                <div class="config-content">
                  <div *ngIf="hasPromptData()" class="prompt-details">
                    <div class="config-field" *ngIf="previewData.data.name">
                     <h3 class="headerName">Prmopt Details</h3>
                      <label>Prompt Name</label>
                      <div class="field-value">{{ previewData.data.name }}</div>
                    </div>
                    <div class="config-field" *ngIf="previewData.data.description">
                      <label>Description</label>
                      <div class="field-value">{{ previewData.data.description }}</div>
                    </div>
                    <div class="config-field" *ngIf="previewData.data.role">
                      <label>Role</label>
                      <div class="field-value">{{ previewData.data.role }}</div>
                    </div>
                    
                    <div class="config-field" *ngIf="previewData.data.goal">
                      <label>Goal</label>
                      <div class="field-value description-text">{{ previewData.data.goal }}</div>
                    </div>
                    
                    <div class="config-field" *ngIf="previewData.data.backstory">
                      <label>Backstory</label>
                      <div class="prompt-content">{{ previewData.data.backstory }}</div>
                    </div>
                    
                    <div class="config-field" *ngIf="previewData.data.expectedOutput">
                      <label>Expected Output</label>
                      <div class="prompt-content">{{ previewData.data.expectedOutput }}</div>
                    </div>

                      <div class="meta-row">
                        <div class="meta-item" *ngIf="previewData.data.createdBy">
                          <label>Added by</label>
                          <div class="meta-value">{{ previewData.data.createdBy }}</div>
                        </div>
                        <div class="meta-item" *ngIf="previewData.data.createdAt || previewData.data.updatedAt">
                          <label>Added on</label>
                          <div class="meta-value">{{ formatDate(previewData.data.createdAt || previewData.data.updatedAt) }}</div>
                        </div>
                      </div>

                    <div class="config-field" *ngIf="previewData.data.promptTask || previewData.data.template || previewData.data.content">
                      <label>Freeform Prompt</label>
                      <div class="prompt-content">{{ previewData.data.promptTask || previewData.data.template || previewData.data.content }}</div>
                    </div>
                  </div>
                  <div *ngIf="!hasPromptData()" class="no-config">
                    <p>No prompt configuration available</p>
                  </div>
                </div>
              </div>
            </ava-accordion>

            <!-- Model Accordion -->
            <ava-accordion
              [expanded]="false"
              [animation]="true"
              [controlled]="false"
              iconClosed="chevron-down"
              iconOpen="chevron-up"
              iconPosition="right"
              type="default"
            >
              <div header>
                <div class="config-header-content">
                  <ava-icon iconName="Box" iconSize="20" iconColor="#14161F"></ava-icon>
                  <span class="config-label">Model</span>
                </div>
              </div>
              <div content>
                <div class="config-content">
                  <div *ngIf="hasModelData()" class="model-details">
                    <div *ngFor="let model of previewData.data.agentConfigs.modelRef; let i = index" class="model-item">
                      <h3 class="headerName">Model Configuration</h3>
                      <div class="config-field" *ngIf="model.modelDeploymentName">
                        <label>Model {{ i + 1 }} - Deployment Name</label>
                        <div class="field-value">{{ model.modelDeploymentName }}</div>
                      </div>
                      
                      <div class="config-field" *ngIf="model.model">
                        <label>Model</label>
                        <div class="dropdown-display">
                          <span>{{ model.model }}</span>
                        </div>
                      </div>
                      
                      <div class="config-field" *ngIf="model.modelType">
                        <label>Model Type</label>
                        <div class="dropdown-display">
                          <span>{{ model.modelType }}</span>
                        </div>
                      </div>
                      
                      
                      <hr *ngIf="i < previewData.data.agentConfigs.modelRef.length - 1" class="model-separator">
                    </div>
                    
                    <div class="config-field" *ngIf="previewData.data.agentConfigs.temperature !== undefined && previewData.data.agentConfigs.temperature !== null">
                      <label>Temperature</label>
                      <ava-slider [min]="0" [max]="1" [step]="0.01"
                          [value]="previewData.data.agentConfigs.temperature" class="disabled-slider"></ava-slider>
                    </div>
                    
                    <div class="config-field" *ngIf="previewData.data.agentConfigs.topP">
                      <label>Top P</label>
                      <div class="field-value">{{ previewData.data.agentConfigs.topP }}</div>
                    </div>
                    
                    <div class="config-field" *ngIf="previewData.data.agentConfigs.maxToken">
                      <label>Max Tokens</label>
                      <div class="field-value">{{ previewData.data.agentConfigs.maxToken }}</div>
                    </div>

                    <div class="config-toggle">
                      <span class="toggle-text" (click)="toggleConfigDetails()">
                        View More Configuration Details {{ showMoreConfig ? '-' : '+' }}
                      </span>
                    </div>

                    <!-- Collapsible Configuration Fields -->
                    <div class="config-details" [class.expanded]="showMoreConfig">
                      <div *ngFor="let model of previewData.data.agentConfigs.modelRef; let i = index">
                        <div class="config-field" *ngIf="model.aiEngine">
                          <label>AI Engine</label>
                          <div class="dropdown-display">
                            <span class="engine-icon"></span>
                            <span>{{ model.aiEngine }}</span>
                          </div>
                        </div>

                        <div class="config-field" *ngIf="model.modelType">
                          <label>Model Type</label>
                          <div class="dropdown-display">
                            <span>{{ model.modelType }}</span>
                          </div>
                        </div>

                        <div class="config-field" *ngIf="model.baseurl || model.baseUrl">
                          <label>Base URL</label>
                          <div class="input-display">{{ model.baseurl || model.baseUrl }}</div>
                        </div>

                        <div class="config-field" *ngIf="model.llmDeploymentName">
                          <label>LLM Deployment Name</label>
                          <div class="input-display">{{ model.llmDeploymentName }}</div>
                        </div>

                        <div class="config-field" *ngIf="model.apiKey">
                          <label>API Key Encoded</label>
                          <div class="input-display">{{ model.apiKey }}</div>
                        </div>

                        <div class="config-field" *ngIf="model.apiVersion">
                          <label>API Version</label>
                          <div class="input-display">{{ model.apiVersion }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div *ngIf="!hasModelData()" class="no-config">
                    <p>No model configuration available</p>
                  </div>
                </div>
              </div>
            </ava-accordion>

            <!-- Knowledgebase Accordion -->
            <ava-accordion
              [expanded]="false"
              [animation]="true"
              [controlled]="false"
              iconClosed="chevron-down"
              iconOpen="chevron-up"
              iconPosition="right"
              type="default"
            >
              <div header>
                <div class="config-header-content">
                  <ava-icon iconName="BookOpen" iconSize="20" iconColor="#14161F"></ava-icon>
                  <span class="config-label">Knowledgebase</span>
                </div>
              </div>
              <div content>
                <div class="config-content">
                  <div *ngIf="hasKnowledgebaseData()" class="knowledgebase-details">
                    <div *ngFor="let kb of previewData.data.agentConfigs.knowledgeBaseRef; let i = index" class="kb-item">
                        <h3 class="headerName">Knowledge Base Details</h3>
                      <div class="config-field" *ngIf="kb.indexCollectionName">
                        <label>Knowledge Base {{ i + 1 }} - Collection Name</label>
                        <div class="field-value">{{ kb.indexCollectionName }}</div>
                      </div>
                      
                      <div class="config-field" *ngIf="kb.modelDeploymentName">
                        <label>Model Deployment Name</label>
                        <div class="field-value">{{ kb.modelDeploymentName }}</div>
                      </div>
                      
                      <div class="config-field" *ngIf="kb.model">
                        <label>Embedding Model</label>
                        <div class="dropdown-display">
                          <span>{{ kb.model }}</span>
                        </div>
                      </div>
                      
                      <div class="config-field" *ngIf="kb.modelType">
                        <label>Model Type</label>
                        <div class="dropdown-display">
                          <span>{{ kb.modelType }}</span>
                        </div>
                      </div>
                      
                      <div class="config-field" *ngIf="kb.aiEngine">
                        <label>AI Engine</label>
                        <div class="dropdown-display">
                          <span>{{ kb.aiEngine }}</span>
                        </div>
                      </div>

                      <div class="config-field" *ngIf="kb.splitSize !== undefined && kb.splitSize !== null">
                        <label>Split Size</label>
                        <ava-slider [min]="0" [max]="20000" [step]="1"
                            [value]="kb.splitSize" class="disabled-slider"></ava-slider>
                      </div>

                      <div class="config-field" *ngIf="kb.uploadType">
                        <label>Upload Type</label>
                        <div class="input-display">{{ kb.uploadType }}</div>
                      </div>

                      <div class="config-field" *ngIf="kb.files && kb.files.length > 0">
                        <label>Files Uploaded</label>
                        <div class="files-list">
                          <div *ngFor="let file of kb.files; let fileIndex = index" class="file-item">
                            <ava-icon iconName="file-text" [iconColor]="getFileIconColor(fileIndex)" [iconSize]="16"></ava-icon>
                            <span class="file-name">{{ file.fileName || file.name || "Knowledge Base Data" }}</span>
                          </div>
                        </div>
                      </div>
                      
                      <hr *ngIf="i < previewData.data.agentConfigs.knowledgeBaseRef.length - 1" class="kb-separator">
                    </div>
                  </div>
                  <div *ngIf="!hasKnowledgebaseData()" class="no-config">
                    <p>No knowledgebase configuration available</p>
                  </div>
                </div>
              </div>
            </ava-accordion>

            <!-- Tools Accordion -->
            <ava-accordion
              [expanded]="false"
              [animation]="true"
              [controlled]="false"
              iconClosed="chevron-down"
              iconOpen="chevron-up"
              iconPosition="right"
              type="default"
            >
              <div header>
                <div class="config-header-content">
                  <ava-icon iconName="Wrench" iconSize="20" iconColor="#14161F"></ava-icon>
                  <span class="config-label">Tools</span>
                </div>
              </div>
              <div content>
                <div class="config-content">
                  <div *ngIf="hasToolsData()" class="tools-details">
                    <!-- Built-in Tools -->
                    <div *ngFor="let tool of previewData.data.agentConfigs.toolRef; let i = index" class="tool-item">
                      <div class="config-field">
                        <label>Tool {{ i + 1 }} - {{ tool.toolName || tool.name }}</label>
                        <div class="field-value">{{ tool.description }}</div>
                      </div>
                      
                      <div class="config-field" *ngIf="tool.toolType">
                        <label>Tool Type</label>
                        <div class="dropdown-display">
                          <span>{{ tool.toolType }}</span>
                        </div>
                      </div>
                      
                      <div class="config-field" *ngIf="tool.toolClassName">
                        <label>Tool Class Name</label>
                        <div class="field-value">{{ tool.toolClassName }}</div>
                      </div>
                      
                      <div class="config-field" *ngIf="tool.functionality || tool.content || tool.toolClassDef">
                        <label>Tool Configuration</label>
                        <div class="code-content">{{ tool.functionality || tool.content || tool.toolClassDef }}</div>
                      </div>
                      
                      <hr *ngIf="i < previewData.data.agentConfigs.toolRef.length - 1" class="tool-separator">
                    </div>
                    
                    <!-- User-defined Tools -->
                    <div *ngFor="let tool of previewData.data.agentConfigs.userToolRef; let i = index" class="tool-item">
                      <div class="config-field">
                        <label>User Tool {{ i + 1 }} - {{ tool.toolName || tool.name }}</label>
                        <div class="field-value">{{ tool.description }}</div>
                      </div>
                      
                      <div class="config-field" *ngIf="tool.toolType">
                        <label>Tool Type</label>
                        <div class="dropdown-display">
                          <span>{{ tool.toolType }}</span>
                        </div>
                      </div>
                      
                      <div class="config-field" *ngIf="tool.toolClassName">
                        <label>Tool Class Name</label>
                        <div class="field-value">{{ tool.toolClassName }}</div>
                      </div>

                      <div class="config-field" *ngIf="tool.functionality || tool.content || tool.toolClassDef">
                        <label>Tool Configuration</label>
                        <div class="code-content">{{ tool.functionality || tool.content || tool.toolClassDef }}</div>
                      </div>
                      
                      <hr *ngIf="i < previewData.data.agentConfigs.userToolRef.length - 1" class="tool-separator">
                    </div>
                  </div>
                  <div *ngIf="!hasToolsData()" class="no-config">
                    <p>No tools configuration available</p>
                  </div>
                </div>
              </div>
            </ava-accordion>

          </div>
        </div>
      </div>
    </div>
    <!-- <div panel-footer>
      <ava-button label="Edit Workflow" variant="info" width="100%" (userClick)="onButtonClick($event)"></ava-button>
    </div> -->
  </app-preview-panel>
</div>
