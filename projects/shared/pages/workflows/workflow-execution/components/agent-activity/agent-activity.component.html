<div class="agent-activity">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="activityGradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <div
    class="activity-content"
    aria-labelledby="activity-title"
    *ngIf="status !== executionStatus.notStarted"
  >
    <div class="image-container">
      <p>{{ activityInfo.message }}</p>
      <img width="200px" [src]="activityInfo.imgSrc" />
      <div class="actions">
        <ava-button
          (userClick)="onViewActivity()"
          class="view-activity-button"
          label="View Activity"
          variant="secondary"
          size="medium"
        ></ava-button>
        <ava-button
          *ngIf="activityInfo.isCompleted"
          (userClick)="onViewOutput()"
          class="view-activity-button"
          label="View Output"
          variant="secondary"
          size="medium"
        ></ava-button>
      </div>
    </div>

    <div class="output" *ngIf="showOutput">
      <div class="activity-text" *ngIf="activityLogs.length">
        <p [style.color]="log.color" *ngFor="let log of activityLogs">
          {{ log.content }}
        </p>
      </div>
    </div>
    <div class="progress-container">
      <span>
        Progress -
        <span [style.color]="progress < 100 ? '#0891B2' : '#059669'"
          >{{ progress }}% Complete</span
        >
      </span>
      <span
        class="icon-btn"
        [ngClass]="{ active: showOutput }"
        (click)="closeOutput()"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6 9L12 15L18 9"
            stroke="black"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>
    </div>
    <!-- Activity Logs -->
    <!-- <div class="logs-section">
      <p *ngIf="!activityLogs.length" class="no-logs" aria-live="polite">
        No activity logs available yet.
      </p>

      <div class="activity-text" *ngIf="activityLogs.length">
        <p [style.color]="log.color" *ngFor="let log of activityLogs">{{log.content}}</p>
      </div>

      <div
        class="success-message"
        *ngIf="activityLogs.length"
        role="status"
        aria-live="polite"
      >
        - Agent Pipeline completed successfully -
      </div>
    </div> -->
  </div>

  <!-- Progress and Controls -->
  <!-- <div class="progress-section">
    <div class="progress-info">
      <div class="progress-bar">
        <div class="progress-fill" [style.width.%]="progress"></div>
      </div>
      <span class="progress-label" aria-live="polite"
        >Progress - {{ progress }}% Complete</span
      >
    </div>

    <div
      class="control-buttons"
      role="toolbar"
      aria-label="Workflow execution controls"
    >
      <button
        class="control-button refresh"
        (click)="onControlAction('play')"
        aria-label="Refresh workflow"
        title="Refresh"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <path
            d="M23 4v6h-6M1 20v-6h6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M3.51 9a9 9 0 0114.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0020.49 15"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <button
        class="control-button stop"
        (click)="onControlAction('stop')"
        aria-label="Stop workflow execution"
        title="Stop"
        [disabled]="!isRunning"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <rect x="4" y="4" width="16" height="16" fill="currentColor" />
        </svg>
      </button>

      <button
        class="control-button play"
        (click)="onControlAction('play')"
        aria-label="Start workflow execution"
        title="Start"
        [disabled]="isRunning"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          aria-hidden="true"
        >
          <path d="M5 3l14 9-14 9V3z" fill="currentColor" />
        </svg>
      </button>
    </div>
  </div> -->
</div>
