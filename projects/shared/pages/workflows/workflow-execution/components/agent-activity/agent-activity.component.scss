:host {
  height: 100%;
  display: block;
}
.agent-activity {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 20px;
  box-shadow: 0 4px 15px var(--card-shadow);
  background: var(--card-bg);

  .image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    flex: 1;
  }

  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: var(--text-color);
    }

    .save-button {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background-color: var(--card-bg);
      position: relative;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      color: transparent;
      background-image: var(--gradient-primary);
      background-clip: text;
      -webkit-background-clip: text;
      cursor: pointer;
      transition: all 0.2s ease;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 8px;
        padding: 1px;
        background: var(--gradient-primary);
        -webkit-mask:
          linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        pointer-events: none;
      }

      &:hover {
        background-color: var(--dropdown-hover-bg);
      }

      svg {
        width: 16px;
        height: 16px;
        stroke: url(#activityGradient);

        path {
          stroke: url(#activityGradient);
          stroke-width: 2;
        }
      }
    }
  }

  .activity-content {
    position: relative;
    flex-grow: 1;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: var(--dashboard-scrollbar-track);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--dashboard-scrollbar-thumb);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: var(--dashboard-scrollbar-thumb-hover);
    }

    .logs-section {
      background-color: var(--dashboard-bg-lighter);
      border-radius: 15px;
      padding: 24px;

      .no-logs {
        color: var(--text-secondary);
        font-style: italic;
        text-align: center;
        padding: 20px 0;
      }

      .activity-text {
        font-size: 16px;
        line-height: 1.7;
        color: var(--text-secondary);
        margin-bottom: 20px;
        white-space: pre-line;
      }

      .success-message {
        font-size: 16px;
        color: var(--success-color);
        font-weight: 500;
        margin-top: 20px;
        text-align: center;
      }
    }
  }

  .progress-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;

    .progress-info {
      flex: 1;
      margin-right: 20px;

      .progress-bar {
        width: 100%;
        height: 6px;
        background-color: var(--dashboard-bg-light);
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 8px;

        .progress-fill {
          height: 100%;
          background: var(--gradient-primary);
          border-radius: 3px;
          transition: width 0.3s ease;
        }
      }

      .progress-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--text-color);
      }
    }

    .control-buttons {
      display: flex;
      gap: 8px;

      .control-button {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--card-bg);
        border: 1px solid var(--dashboard-action-button-border);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.2s ease;

        svg {
          transition: color 0.2s ease;
        }

        &:hover:not(:disabled) {
          background-color: var(--dashboard-bg-lighter);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.refresh {
          color: var(--dashboard-secondary);
        }

        &.stop {
          color: var(--error-color);
        }

        &.play {
          color: var(--success-color);
        }
      }
    }
  }
}

.view-activity-button {
  margin-top: 24px;
}

.progress-container {
  // height: 70px;
  padding: 10px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #d1d3d8;
  span {
    margin-top: 5px;
  }
}
.output {
  padding: 1rem;
  background-color: white;
  border: 1px solid #d1d3d8;
  width: 100%;
  height: calc(100% - 70px);
  position: absolute;
  top: 0;
}

.actions {
  display: flex;
  gap: 1rem;
}

.icon-btn {
  cursor: pointer;
  transform: translateY(-5px) rotate(180deg);
  transition: 0.6s ease-in-out;
  padding-bottom: 5px;
}
.icon-btn.active {
  transform: rotate(0deg);
}
