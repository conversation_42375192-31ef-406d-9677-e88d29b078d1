import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AccordionComponent,
  ButtonComponent,
  IconComponent,
} from '@ava/play-comp-library';
import { ExecutionStatus, ActivityLog } from '@shared/models/execution.model';

// Define a specific ExecutionDetails interface for this component since it has different properties
export interface AgentActivityExecutionDetails {
  agentName: string;
  executionId: string;
  startTime: string;
  endTime?: string;
  status: 'running' | 'completed' | 'failed' | 'canceled';
  steps?: string[];
}

@Component({
  selector: 'app-agent-activity',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  templateUrl: './agent-activity.component.html',
  styleUrls: ['./agent-activity.component.scss'],
})
export class AgentActivityComponent {
  @Input() activityLogs: ActivityLog[] = [];
  @Input() executionDetails?: AgentActivityExecutionDetails;
  @Input() progress: number = 0;
  @Input() isRunning: boolean = false;
  @Input() status!: ExecutionStatus;
  @Input() loadColor!: string;

  get color() {
    return this.loadColor;
  }
  executionStatus = ExecutionStatus;

  showOutput = false;

  @Output() saveLogs = new EventEmitter<void>();
  @Output() controlAction = new EventEmitter<'play' | 'pause' | 'stop'>();
  @Output() onOutPutBtnClick = new EventEmitter<any>();

  showDetails: boolean = true;

  constructor() {}

  ngOnInit(): void {
    console.log(this.activityLogs);
  }

  toggleDetails(): void {
    this.showDetails = !this.showDetails;
  }

  onSaveLogs(): void {
    this.saveLogs.emit();
  }

  onControlAction(action: 'play' | 'pause' | 'stop'): void {
    this.controlAction.emit(action);
  }

  get statusClass(): string {
    if (!this.executionDetails) return '';

    switch (this.executionDetails.status) {
      case 'running':
        return 'status-running';
      case 'completed':
        return 'status-completed';
      case 'failed':
        return 'status-failed';
      case 'canceled':
        return 'status-canceled';
      default:
        return '';
    }
  }

  get activityInfo() {
    let data = {
      message: '',
      imgSrc: '',
      isCompleted: false,
    };

    if (this.status === this.executionStatus.running) {
      data = {
        message: 'Hey! The Agents are working together creating a flow!',
        imgSrc: 'svgs/workflow-execution.svg',
        isCompleted: false,
      };
    }

    if (this.status === this.executionStatus.completed) {
      data = {
        message: 'The flow is complete!',
        imgSrc: 'svgs/ava-flow-complete.svg',
        isCompleted: true,
      };
    }

    return data;
  }

  onViewActivity() {
    this.showOutput = true;
  }

  onViewOutput() {
    this.onOutPutBtnClick.emit();
  }
  closeOutput() {
    this.showOutput = false;
  }
}
