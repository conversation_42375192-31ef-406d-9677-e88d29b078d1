<div class="agent-output">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>

  <div class="output-header">
    <h3 id="agent-output-title">Agent Output</h3>
    <div class="export-dropdown" [class.open]="isDropdownOpen">
      <ava-button
        (userClick)="toggleDropdown()"
        aria-label="Export agent outputs"
        title="Export agent outputs"
        variant="primary"
        size="medium"
        label="Export"
        [iconName]="'ChevronDown'"
        [iconPosition]="'right'"
        [iconColor]="'white'"
      >
      </ava-button>
      
      <div class="dropdown-menu" *ngIf="isDropdownOpen" (click)="$event.stopPropagation()">
        <div class="dropdown-item" 
             *ngFor="let option of filteredExportOptions" 
             (click)="onExportOptionSelected(option.id)"
             [attr.aria-label]="option.label">
          <ava-icon 
            [iconName]="option.icon" 
            [iconSize]="16"
            iconColor="#444653">
          </ava-icon>
          <span>{{ option.label }}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="output-content" aria-labelledby="agent-output-title">
    <!-- No outputs message -->
    <div *ngIf="!outputs.length" class="no-outputs" aria-live="polite">
      No agent outputs available yet.
    </div>

    <!-- Output cards with error state handling -->
    <div *ngFor="let output of outputs">
      <div *ngIf="output.isError" class="error-block">
        <div class="error-icon">
          <ava-icon iconName="AlertCircle" [iconSize]="20" iconColor="#dc2626"></ava-icon>
        </div>
        <div class="error-content">
          <p class="error-message">
            {{ output.content }}
            <span *ngIf="output.errorDetails && output.errorDetails !== output.content" class="error-details">
              {{ output.errorDetails }}
            </span>
          </p>
        </div>
      </div>

      <!-- Regular output cards for non-error outputs -->
      <div *ngIf="!output.isError" class="output-card" [class.expanded]="expandedStates[output.id]">
      <div class="output-card-header" (click)="toggleExpand(output.id, $event)">
        <div class="header-content">
          <h4 class="output-title">
            {{ output.title || 'Output' }}
            <span class="agent-name">({{ output.agentName || 'Agent' }})</span>
          </h4>
          <div class="output-subtitle">
            <span class="timestamp">{{ output.timestamp | date:'medium' }}</span>
          </div>
        </div>
        <div class="header-actions">
          <button 
            class="action-button" 
            (click)="toggleExpand(output.id, $event)" 
            [title]="expandedStates[output.id] ? 'Collapse details' : 'Expand details'"
            [attr.aria-expanded]="expandedStates[output.id]"
          >
            <ava-icon
              [class.expanded]="expandedStates[output.id]"
              [iconName]="expandedStates[output.id] ? 'chevron-up' : 'chevron-down'"
              [iconSize]="16"
              [title]="expandedStates[output.id] ? 'Collapse details' : 'Expand details'"
              iconColor="#444653">
            </ava-icon>
          </button>
        </div>
      </div>
      
      <div class="output-card-content">
        <div class="output-details">
          <div class="output-section" *ngIf="output.description">
            <h5><strong>Description</strong></h5>
            <p>{{ output.description }}</p>
          </div>
          
          <div class="output-section" *ngIf="output.expected_output">
            <h5><strong>Expected Output</strong></h5>
            <p>{{ output.expected_output }}</p>
          </div>
          
          <div class="output-raw" *ngIf="output.raw">
            <div class="raw-header">
              <h5><strong>Raw Output</strong></h5>
              <ava-icon
              iconName="copy"
              (click)="copyToClipboard(output.raw)">
              </ava-icon>
            </div>
            <pre class="code-block"><code [innerHTML]="output.raw"></code></pre>
          </div>
          
          <div class="output-section" *ngIf="output.summary">
            <h5><strong>Summary</strong></h5>
            <p>{{ output.summary }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>