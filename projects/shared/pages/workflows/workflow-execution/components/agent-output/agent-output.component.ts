import { Component, Input, Output, EventEmitter, OnInit, Inject, HostListener, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent, IconComponent } from '@ava/play-comp-library';
import { WorkflowService } from '@shared/services/workflow.service';

// Declare jsPDF for dynamic import
declare var jsPDF: any;
export interface AgentOutput {
  id: string;
  title: string;
  content: string;
  agentName: string;
  timestamp: string;
  type?: 'code' | 'text' | 'json' | 'markdown';
  description: string;
  expected_output: string;
  summary: string;
  raw: string;
  isError?: boolean; // Flag to indicate error state
  errorDetails?: string; // Optional error details
  [key: string]: any; // Allow additional properties
}

@Component({
  selector: 'app-agent-output',
  standalone: true,
  imports: [CommonModule, ButtonComponent, IconComponent],
  templateUrl: './agent-output.component.html',
  styleUrls: ['./agent-output.component.scss'],
})
export class AgentOutputComponent implements OnInit {
  @Input() outputs: AgentOutput[] = [];
  @Output() export = new EventEmitter<void>();
  
  // Track expanded state for each output
  expandedStates: {[key: string]: boolean} = {};
  
  // Dropdown state
  isDropdownOpen: boolean = false;
  
  // Export options
  exportOptions = [
    { id: 'output', label: 'Export Output', icon: 'FileText' },
    // { id: 'pdf', label: 'Export to PDF', icon: 'FilePdf' },
    // { id: 'word', label: 'Export to Word', icon: 'FileWord' },
    // { id: 'excel', label: 'Export to Excel', icon: 'FileExcel' }
  ];

  // Track JSON validity for Excel export
  isJsonValid: boolean = false;

  constructor(private workflowService: WorkflowService, private elementRef: ElementRef) {}

  ngOnInit(): void {
    // Initialize expanded state for each output
    this.outputs.forEach(output => {
      this.expandedStates[output.id] = false;
    });
    
    // Check if any output has valid JSON for Excel export
    this.checkJsonValidity();
  }

  private checkJsonValidity(): void {
    this.isJsonValid = this.outputs.some(output => {
      const content = output.raw || output.content || '';
      return this.validateJson(content) !== null;
    });
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    // Close dropdown if clicking outside
    if (this.isDropdownOpen && !this.elementRef.nativeElement.contains(event.target)) {
      this.closeDropdown();
    }
  }

  toggleExpand(id: string, event?: Event): void {
    if (event) {
      event.stopPropagation();
    }
    this.expandedStates[id] = !this.expandedStates[id];
  }

  getContentType(output: AgentOutput): string {
    // Default to 'text' if not specified
    if (!output.type) {
      // Try to auto-detect
      if (output.content.startsWith('<') && output.content.includes('</')) {
        return 'code';
      } else if (
        output.content.includes('```') ||
        output.content.includes('#')
      ) {
        return 'markdown';
      } else if (
        output.content.startsWith('{') ||
        output.content.startsWith('[')
      ) {
        return 'json';
      }
      return 'text';
    }
    return output.type;
  }

  copyToClipboard(content: string): void {
    navigator.clipboard
      .writeText(content)
      .then(() => {
        // Could show a toast notification here
        console.log('Content copied to clipboard');
      })
      .catch((err) => {
        console.error('Could not copy text: ', err);
      });
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  onExportOptionSelected(option: string): void {
    this.closeDropdown();
    
    switch (option) {
      case 'output':
        this.exportOutput();
        break;
      case 'pdf':
        this.exportPdf();
        break;
      case 'word':
        this.exportDoc();
        break;
      case 'excel':
        if (this.isJsonValid) {
          this.saveToExcel();
        } else {
          console.warn('Excel export requires valid JSON data');
        }
        break;
      default:
        console.warn('Unknown export option:', option);
    }
  }

  // Get filtered export options based on conditions
  get filteredExportOptions() {
    return this.exportOptions.filter(option => {
      if (option.id === 'excel') {
        return this.isJsonValid;
      }
      return true;
    });
  }

  // Export Output as plain text (same as old exportOutput method)
  public exportOutput(): void {
    let content = '';
    this.outputs.forEach((output: AgentOutput, index: number) => {
      content += `Agent ${index + 1}: ${output.agentName || 'Unknown Agent'}\n`;
      content += `Title: ${output.title || 'Output'}\n`;
      content += `Description: ${output.description || 'No description'}\n`;
      content += `Expected Output: ${output.expected_output || 'No expected output'}\n`;
      content += `Summary: ${output.summary || 'No summary'}\n`;
      content += `Raw Output: ${output.raw || output.content || 'No content'}\n`;
      if (output.timestamp) {
        content += `Timestamp: ${output.timestamp}\n`;
      }
      content += '\n' + '='.repeat(50) + '\n\n';
    });
    this.saveFile('Agent_Output.txt', content);
  }



  public exportDoc(): void {
    let content = '';
    const makeSection = (title: string, textContent: string[]): void => {
      content += `${title.toUpperCase()}\n`;
      content += `${textContent.join('\n')}\n\n\n`;
    };

    this.outputs.forEach((output: AgentOutput, index: number) => {
      makeSection('Description:', [output.description || 'No description']);
      makeSection('Expected Output:', [output.expected_output || 'No expected output']);
      makeSection('Summary:', [output.summary || 'No summary']);
      makeSection('Raw Output:', [output.raw || output.content || 'No content']);
      if (index < this.outputs.length - 1) {
        content += '\f';
      }
    });
    this.saveFile('Workflow_output.doc', content);
  }

  public exportPdf(): void {
    // Try to use jsPDF if available, otherwise fallback to text
    if (typeof jsPDF !== 'undefined') {
      this.createRealPdf();
    } else {
      console.warn('jsPDF not available, using text fallback');
      this.createPdfFallback();
    }
  }

  private createRealPdf(): void {
    // Use the exact same approach as DA pipeline component
    const doc = new jsPDF();
    let contentWidth = 180;
    const fontSize = 5;
    const boldFontSize = 14;
    const boldforlines = 10;
    const lineHeight = fontSize * 1.2;
    const sectionSpacing = 5;
    const maxPageHeight = doc.internal.pageSize.height;
    let txtLength = this.outputs.length;

    let yOffset = 10;
    let isFirstPage = true;
    let currentPage = 1;

    let makeSection = (title: string, textContent: string[], headerX = 10, contentX = 15) => {
      doc.setFontSize(boldFontSize);
      doc.text(title, headerX, yOffset);
      yOffset += sectionSpacing;
      doc.setFontSize(boldforlines);
      let remainingLines = this.addTextLines(doc, textContent, contentX, yOffset);
      let nextSectionOffset = doc.getTextDimensions(textContent as any).h;
      while (remainingLines.length > 0) {
        doc.addPage();
        yOffset = 10;
        isFirstPage = true;
        currentPage++;
        yOffset += sectionSpacing;
        doc.setFontSize(boldforlines);
        nextSectionOffset = doc.getTextDimensions(remainingLines as any).h;
        remainingLines = this.addTextLines(doc, remainingLines, contentX, yOffset);
      }
      yOffset += nextSectionOffset + sectionSpacing;
    };

    this.outputs.forEach((output: AgentOutput, index: number) => {
      contentWidth = index === 0 ? 296 : 180;
      const descriptionLines = doc.splitTextToSize(output.description || 'No description', contentWidth);
      const expectedOutputLines = doc.splitTextToSize(output.expected_output || 'No expected output', contentWidth);
      const rawOutputLines = doc.splitTextToSize(output.raw || output.content || 'No content', contentWidth);
      const summaryLines = doc.splitTextToSize(output.summary || 'No summary', contentWidth);

      // Add Description section
      makeSection('Description:', descriptionLines);

      // Add Expected Output section
      makeSection('Expected Output:', expectedOutputLines);

      // Add Summary section
      makeSection('Summary:', summaryLines);

      // Add Raw Output section
      makeSection('Raw Output:', rawOutputLines);

      // Add new Page after every section
      if (index < txtLength - 1) {
        yOffset = 10;
        doc.addPage();
      }
    });

    doc.save('Workflow_output.pdf');
  }

  private addTextLines(doc: any, lines: string[], x: number, y: number): string[] {
    const maxLinesPerPage = Math.floor(
      (doc.internal.pageSize.height - y) / (doc.getLineHeight() / doc.internal.scaleFactor)
    );
    const visibleLines = lines.slice(0, maxLinesPerPage);
    const remainingLines = lines.slice(maxLinesPerPage);
    doc.text(visibleLines, x, y);
    return remainingLines;
  }

  private createPdfFallback(): void {
    // Create a formatted text document as PDF fallback
    let content = 'WORKFLOW OUTPUT REPORT\n';
    content += '='.repeat(60) + '\n\n';
    content += `Generated: ${new Date().toLocaleString()}\n\n`;
    
    this.outputs.forEach((output: AgentOutput, index: number) => {
      content += 'DESCRIPTION:\n';
      content += (output.description || 'No description') + '\n\n\n';
      
      content += 'EXPECTED OUTPUT:\n';
      content += (output.expected_output || 'No expected output') + '\n\n\n';
      
      content += 'SUMMARY:\n';
      content += (output.summary || 'No summary') + '\n\n\n';
      
      content += 'RAW OUTPUT:\n';
      content += (output.raw || output.content || 'No content') + '\n\n\n';
      
      if (index < this.outputs.length - 1) {
        content += '\n' + '='.repeat(60) + '\n\n';
      }
    });
    
    // Save as text file with PDF extension (user can convert if needed)
    this.saveFile('Workflow_output.pdf', content);
  }

  public saveToExcel(): void {
    const fileName = `Workflow_output.xlsx`;
    
    // Use the same format as pipeline component - pass the raw output data
    const outputContent = this.outputs.map(output => output.raw || output.content || '').join('\n');
    
    // For now, create CSV format (can be enhanced with actual Excel library later)
    const csvContent = this.convertToCSV(this.outputs.map(output => ({
      'Description': output.description || 'No description',
      'Expected Output': output.expected_output || 'No expected output', 
      'Summary': output.summary || 'No summary',
      'Raw Output': output.raw || output.content || 'No content'
    })));
    
    this.saveFile('Workflow_output.xlsx', csvContent);
  }

  private convertToCSV(data: any[]): string {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [];
    
    // Add headers
    csvRows.push(headers.join(','));
    
    // Add data rows
    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header] || '';
        // Escape quotes and wrap in quotes if contains comma or newline
        const escaped = String(value).replace(/"/g, '""');
        return escaped.includes(',') || escaped.includes('\n') ? `"${escaped}"` : escaped;
      });
      csvRows.push(values.join(','));
    }
    
    return csvRows.join('\n');
  }



  private saveFile(filename: string, content: any): void {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }

  private validateJson(output: string): any | null {
    try {
      const parsedOutput = JSON.parse(output);
      return parsedOutput;
    } catch (e) {
      return null;
    }
  }

  previewOutput(output: AgentOutput): void {
    // In a real app, this would open a preview modal or navigate to a preview page
    console.log('Preview output:', output);
    window.open(
      `data:text/html;charset=utf-8,${encodeURIComponent(output.content)}`,
      '_blank',
    );
  }
}
