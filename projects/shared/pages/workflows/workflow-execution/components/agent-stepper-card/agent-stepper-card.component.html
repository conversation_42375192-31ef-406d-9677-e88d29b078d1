<div class="agent-stepper-card" [class.active]="isActive">
  <!-- Stepper Section -->
  <!-- <div class="stepper-section"> -->
    <!-- Stepper Circle -->
    <!-- <div [class]="stepperClass">
      <ava-icon 
        *ngIf="isCompleted" 
        iconName="check" 
        [iconSize]="16" 
        iconColor="white">
      </ava-icon>
    </div> -->
    
    <!-- Connector Line -->
    <!-- <div *ngIf="!isLast" class="stepper-line" [class.active]="isCompleted"></div>
  </div> -->

  <!-- Card Content -->
  <div class="card-content">
    <!-- Agent Header -->
    <div class="agent-header" (click)="agent.hasInputs ? toggleExpanded() : null">
      <div class="agent-info">
        <div class="bot-icon">
          <ava-icon
            iconName="bot"
            [iconSize]="20"
            iconColor="var(--color-brand-primary)">
          </ava-icon>
        </div>
        <h4 class="agent-name" [title]="agent.name">{{ agent.name }}</h4>
      </div>

      <div class="header-actions">
        <ava-icon
          [class.expanded]="isExpanded"
          [iconName]="isExpanded ? 'chevron-up' : 'chevron-down'"
          [iconSize]="16"
          [title]="!agent.hasInputs ? 'No Input Required' : ''"
          [disabled]="!agent.hasInputs"
          [class.disabled]="!agent.hasInputs"
          iconColor="#444653">
        </ava-icon>
      </div>
    </div>

    <!-- Expanded Content -->
    <div class="agent-details" *ngIf="isExpanded" [@slideDown]>
      <!-- Input Fields Only -->
      <div class="input-section" *ngIf="agent.hasInputs && agent.inputs">
        <div class="inputs-container scrollable">
          <div 
            class="input-field-container" 
            *ngFor="let input of agent.inputs; let i = index; trackBy: trackByIndex">
            
            <label class="input-label">{{ input.inputName }}</label>
            
            <div class="input-container" [class.image-input]="input.inputType === 'image'">
              <div class="input-with-actions">
                <textarea
                  *ngIf="input.inputType !== 'image'"
                  [(ngModel)]="input.value"
                  [disabled]="isInputDisabled(input)"
                  (input)="onInputChange(i, $event)"
                  (keydown.enter)="handleEnterKey(i, $event)"
                  [placeholder]="'Enter ' + input.inputName"
                  class="input-textarea"
                  [class.disabled]="isInputDisabled(input)"
                  [attr.data-index]="i">
                </textarea>

                <div *ngIf="input.inputType === 'image'" class="image-upload-placeholder" (click)="onFileInputClick(i)">
                  <ava-icon
                    iconName="image"
                    [iconSize]="24"
                    iconColor="#03ACC1">
                  </ava-icon>
                  <span>Click to upload an image</span>
                </div>

                <div class="input-actions">
                  <ava-button
                    *ngIf="showFileUploadButton(input) && input.inputType !== 'image'"
                    variant="primary"
                    size="small"
                    title="Attach File"
                    [disabled]="isInputDisabled(input)"
                    (click)="$event.stopPropagation(); onFileInputClick(i)">
                    <ava-icon
                      iconName="paperclip"
                      [iconSize]="18"
                      iconColor="#03ACC1">
                    </ava-icon>
                  </ava-button>
                </div>
              </div>
            </div>

            <!-- Display uploaded files -->
            <div class="uploaded-files" *ngIf="input.files && input.files.length > 0">
              <div
                class="file-item"
                *ngFor="let file of input.files; let fileIndex = index">
                <span class="file-name">{{ file.name }}</span>
                <button
                  class="remove-file"
                  (click)="removeFile(i, fileIndex)"
                  title="Remove file">
                  <ava-icon iconName="x" [iconSize]="12" iconColor="#e53e3e"></ava-icon>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Single Send Button -->
        <div class="send-section">
          <ava-button
          label="Send"
            variant="primary"
            size="small"
            (click)="handleSendAllInputs()"
            [disabled]="!hasValidInputs()">
            <ava-icon
              iconName="send-horizontal"
              [iconSize]="18"
              iconColor="white">
            </ava-icon>
          </ava-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden file input -->
  <input
    #fileInput
    type="file"
    style="display: none"
    multiple
    (change)="onFileSelected($event)">
</div>
