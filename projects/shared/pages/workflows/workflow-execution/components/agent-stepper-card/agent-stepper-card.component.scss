.agent-stepper-card {
  display: flex;
  align-items: stretch;
  gap: 16px;
  margin-bottom: 24px;
  position: relative;

  &:last-child {
    margin-bottom: 2rem;
  }
}

// .stepper-section {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   width: 40px;
//   flex-shrink: 0;
//   min-height: 100%;
//   padding-top: 24px;
//   position: relative;
// }

// .stepper-circle {
//   width: 32px;
//   height: 32px;
//   border-radius: 50%;
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   background: #0084FF;
//   color: white;
//   border: 2px solid #0084FF;
//   box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.10);

//   &.active,
//   &.completed {
//     background: #0084ff;
//     color: white;
//     border-color: #0084ff;
//     box-shadow: 0 0 0 4px rgba(0, 132, 255, 0.25);
//   }

//   &.inactive {
//     background: #e5e7eb;
//     color: #6b7280;
//     border: 2px solid #d1d5db;
//   }

//   .step-number {
//     font-size: 12px;
//     font-weight: 600;
//   }
// }

// .stepper-line {
//   width: 2px;
//   position: absolute;
//   top: 56px; /* Position below the stepper circle */
//   left: 50%;
//   transform: translateX(-50%);
//   bottom: -24px; /* Extend to the bottom of the card */
//   background: linear-gradient(180deg, #0084FF 0%, #E6F3FF 100%);
//   border: none;

//   &.active {
//     background: linear-gradient(180deg, #0084FF 0%, #0084FF 100%);
//   }
// }

.card-content {
  flex-grow: 1;
  background: var(  --build-agents-nav-bg);
  border: 1px solid var(  --build-agents-nav-bg);
  border-radius: 16px;
  padding: 0;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease-in-out;
  overflow: hidden;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

.agent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  cursor: pointer;
  transition: background-color 0.2s ease;
  padding: 16px;
  box-sizing: border-box;

  .agent-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
    min-width: 0; 
    max-width: calc(100% - 40px); 

    .bot-icon {
      width: 40px;
      height: 40px;
      background: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      border: 2px solid transparent;
      transition: border-color 0.3s ease;
    }

    .agent-name {
      font-family: 'Mulish', sans-serif;
      font-weight: 600;
      font-size: 22px;
      line-height: 100%;
      letter-spacing: 0%;
      color: #000000;
      margin: 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      min-width: 0; 
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
  }
}

.agent-stepper-card.active .bot-icon {
  border-color: #0084ff;
}

.agent-details {
  position: static;
  padding: 0 16px 16px 16px;
  background: var(--build-agents-nav-bg);
  animation: slideDown 0.4s ease-in-out;
}

.input-section {
  padding: 0;
}

.inputs-container {
  display: flex;
  flex-direction: column;
  max-height: 205px;
  overflow-y: auto;
  padding-right: 8px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
}

.input-field-container {
  .input-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #1a202c;
  }
}

.input-container {
  position: relative;
  margin-bottom: 12px;
  
  .input-with-actions {
    position: relative;
    display: flex;
    width: 100%;
    
    .input-textarea {
      flex: 1;
      padding-right: 80px; // Make space for action buttons
    }
    
    .input-actions {
      position: absolute;
      right: 8px;
      bottom: 8px;
      display: flex;
      gap: 4px;
    }
  }

  &.image-input {
    .input-textarea {
      display: none; 
    }

    .image-upload-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 24px;
      border: 2px dashed #03ACC1;
      border-radius: 8px;
      background-color: rgba(3, 172, 193, 0.05);
      cursor: pointer;
      transition: all 0.2s ease;
      text-align: center;
      min-height: 120px;
      width: 100%;
      box-sizing: border-box;
      
      &:hover {
        background-color: rgba(3, 172, 193, 0.1);
      }
      
      ava-icon {
        margin-bottom: 8px;
      }
      
      span {
        color: #03ACC1;
        font-size: 14px;
      }
    }
  }

  .input-textarea {
    flex: 1;
    min-height: 40px;
    padding: 8px 12px;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                var(--build-agents-toggle-active-bg);
    border-radius: 16px;
    resize: none;
    font-size: 14px;
    line-height: 1.5;
    transition: border-color 0.2s;
    margin-bottom: 8px;

    &:focus {
      outline: none;
      border-color: var(--input-box-focus);
      box-shadow: 0 0 0 2px rgba(3, 172, 193, 0.2);
    }

    &.disabled {
      background-color: #f5f5f5;
      cursor: not-allowed;
      opacity: 0.7;
    }
  }


}

.uploaded-files {
  margin-top: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .file-item {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 10px;
    font-size: 12px;

    .file-name {
      color: #718096;
      max-width: 150px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .remove-file {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      border: none;
      background: transparent;
      cursor: pointer;
      border-radius: 2px;
      transition: background-color 0.2s ease;

      &:hover {
        background: #fed7d7;
      }
    }
  }
}



.send-section {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

// Animations
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Responsive design
@media (max-width: 768px) {
  .agent-stepper-card {
    gap: 12px;
    margin-bottom: 20px;
  }

  .stepper-section {
    width: 32px;
  }

  .stepper-circle {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .agent-header {
    padding: 12px 16px;

    .agent-info {
      .agent-name {
        font-size: 15px;
      }
    }
  }

  .agent-details {
    padding: 16px;
  }

  .input-container {
    padding: 10px;

    .input-textarea {
      min-height: 36px;
      font-size: 13px;
    }

    .attach-btn,
    .send-btn {
      width: 28px;
      height: 28px;
    }
  }
}
