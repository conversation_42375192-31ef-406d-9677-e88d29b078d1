import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output, ViewChild, ElementRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconComponent, ButtonComponent } from '@ava/play-comp-library';
import { AgentData, AgentInput } from '../workflow-playground/workflow-playground.component';
import { trigger, state, style, transition, animate } from '@angular/animations';

@Component({
  selector: 'app-agent-stepper-card',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconComponent,
    ButtonComponent
  ],
  templateUrl: './agent-stepper-card.component.html',
  styleUrls: ['./agent-stepper-card.component.scss'],
  animations: [
    trigger('slideDown', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateY(-10px)' }),
        animate('300ms ease-out', style({ opacity: 1, transform: 'translateY(0)' }))
      ])
    ])
  ]
})
export class AgentStepperCardComponent {
  @Input() agent!: AgentData;
  @Input() stepNumber: number = 1;
  @Input() isFirst: boolean = false;
  @Input() isLast: boolean = false;
  @Input() isActive: boolean = false;
  @Input() isCompleted: boolean = false;
  
  @Output() inputChanged = new EventEmitter<{inputIndex: number, value: string}>();
  @Output() fileSelected = new EventEmitter<{inputIndex: number, files: File[]}>();
  @Output() messageSent = new EventEmitter<{inputIndex: number, value: string, files?: File[]}>();
  @Output() stepCompleted = new EventEmitter<void>();

  @ViewChild('fileInput') fileInput!: ElementRef;

  isExpanded: boolean = false;
  
  // Tracks which inputs have been completed
  completedInputs: Set<number> = new Set();

  get stepperClass(): string {
    const classes = ['stepper-circle'];
    if (this.isCompleted) classes.push('completed');
    else if (this.isActive) classes.push('active');
    else classes.push('inactive');
    return classes.join(' ');
  }

  toggleExpanded(): void {
    if (this.agent.hasInputs) {
      this.isExpanded = !this.isExpanded;
    }
  }



  onInputChange(inputIndex: number, event: Event): void {
    const target = event.target as HTMLTextAreaElement;
    this.inputChanged.emit({ inputIndex, value: target.value });
  }

  onFileInputClick(inputIndex: number): void {
    // Store the input index for file selection
    this.fileInput.nativeElement.dataset['inputIndex'] = inputIndex.toString();
    this.fileInput.nativeElement.click();
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    const files = target.files;
    const inputIndex = parseInt(target.dataset['inputIndex'] || '0');
    
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      
      // Update the local files first for immediate UI update
      if (this.agent.inputs && this.agent.inputs[inputIndex]) {
        this.agent.inputs[inputIndex].files = fileArray;
        
        // For image inputs, automatically send the image
        if (this.agent.inputs[inputIndex].inputType === 'image') {
          this.handleSendMessage(inputIndex);
        } else {
          // For non-image files, just emit the file selection
          this.fileSelected.emit({ inputIndex, files: fileArray });
          this.completedInputs.add(inputIndex);
          this.checkAllInputsCompleted();
        }
      }
    }
    
    // Reset the file input to allow selecting the same file again if needed
    target.value = '';
  }

  removeFile(inputIndex: number, fileIndex: number, event?: Event): void {
    event?.stopPropagation();
    
    if (this.agent.inputs && this.agent.inputs[inputIndex]?.files?.length) {
      // Clear the files array
      this.agent.inputs[inputIndex].files = [];
      
      // Emit empty files to clear the selection
      this.fileSelected.emit({ inputIndex, files: [] });
      
      // If this was an image input, emit a message with empty files
      if (this.agent.inputs[inputIndex].inputType === 'image') {
        this.messageSent.emit({
          inputIndex,
          value: '',
          files: []
        });
      }
      
      // Update completion status
      this.completedInputs.delete(inputIndex);
      this.checkAllInputsCompleted();
    }
  }

  handleEnterKey(inputIndex: number, event: Event): void {
    const keyboardEvent = event as KeyboardEvent;
    const isLastInput = inputIndex === this.agent.inputs!.length - 1;
    
    if (isLastInput) {
      // Only close card and send all inputs if this is the last input
      keyboardEvent.preventDefault();
      this.handleSendAllInputs();
    }
    // For non-last inputs, just allow normal Enter behavior (new line)
  }

  handleSendAllInputs(): void {
    if (!this.agent.inputs || !this.hasValidInputs()) {
      return;
    }

    // Send all inputs that have values
    this.agent.inputs.forEach((input, index) => {
      const inputValue = input.value || '';
      const inputFiles = input.files ? [...input.files] : undefined;
      
      // Only send if there's content or it's an optional field
      if (inputValue.trim() || inputFiles?.length || input.isOptional) {
        this.messageSent.emit({
          inputIndex: index,
          value: inputValue,
          files: inputFiles
        });
        
        // Mark as completed
        this.completedInputs.add(index);
      }
    });
    
    // Check if all required inputs are completed
    this.checkAllInputsCompleted();
    
    // Auto-close the card after all inputs are sent
    setTimeout(() => {
      this.isExpanded = false;
    }, 500);
  }

  hasValidInputs(): boolean {
    if (!this.agent.inputs) return false;
    
    // Check if at least one required input has content
    return this.agent.inputs.some(input => {
      if (input.isOptional) return true;
      
      if (input.inputType === 'image' || input.inputType === 'file') {
        return input.files && input.files.length > 0;
      }
      
      if (input.inputType === 'text') {
        return input.value && input.value.trim().length > 0;
      }
      
      return false;
    });
  }

  handleSendMessage(inputIndex: number): void {
    // Keep this method for backward compatibility with image uploads
    if (this.agent.inputs && this.agent.inputs[inputIndex]) {
      const input = this.agent.inputs[inputIndex];
      const inputValue = input.value || '';
      const inputFiles = input.files ? [...input.files] : undefined;
      
      // For image inputs, require a file to be uploaded
      if (input.inputType === 'image' && (!inputFiles || inputFiles.length === 0)) {
        return; // Don't proceed if no file is selected for image input
      }
      
      // Emit the message with the current values
      this.messageSent.emit({
        inputIndex,
        value: inputValue,
        files: inputFiles
      });
      
      // Mark as completed
      this.completedInputs.add(inputIndex);
      
      // Check if all required inputs are completed
      this.checkAllInputsCompleted();
      
      // For image inputs, don't auto-close - let user continue with other inputs
      if (input.inputType === 'image') {
        // Focus next input if available
        const nextIndex = inputIndex + 1;
        if (nextIndex < this.agent.inputs.length) {
          setTimeout(() => {
            const nextInput = document.querySelector(`.input-textarea[data-index="${nextIndex}"]`) as HTMLTextAreaElement;
            if (nextInput) {
              nextInput.focus();
            }
          }, 100);
        }
      }
    }
  }

  getAcceptedFileType(input: AgentInput): string {
    return input.inputType === 'image' 
      ? '.png,.jpg,.jpeg,.gif,.bmp,.svg'
      : '.pdf,.doc,.docx,.txt,.csv,.xlsx,.xls,.ppt,.pptx,.png,.jpg,.jpeg,.gif,.bmp,.svg';
  }

  isInputDisabled(input: AgentInput): boolean {
    // Never disable inputs - user should always be able to enter input
    return false;
  }
  
  showFileUploadButton(input: AgentInput): boolean {
    // Only show file upload for file or image inputs
    return input.inputType === 'file' || input.inputType === 'image';
  }
  
  checkAllInputsCompleted(): void {
    if (!this.agent.inputs) {
      this.stepCompleted.emit();
      return;
    }
    
    // Check if all required inputs are completed
    const allRequiredInputsCompleted = this.agent.inputs.every((input) => {
      // Skip optional inputs
      if (input.isOptional) return true;
      
      // For image inputs, check if files are uploaded
      if (input.inputType === 'image') {
        return !!input.files?.length;
      }
      
      // For text inputs, check if there's a value
      if (input.inputType === 'text') {
        return !!input.value?.trim();
      }
      
      // For file inputs, check if files are uploaded
      if (input.inputType === 'file') {
        return !!input.files?.length;
      }
      
      // Default to true for any other input types
      return true;
    });
    
    if (allRequiredInputsCompleted) {
      this.stepCompleted.emit();
    }
  }

  trackByIndex(index: number, item: any): number {
    return index;
  }
}
