<div class="workflow-configuration-container">
  <!-- Canvas Area -->
  <div class="canvas-container">
    <app-canvas-board
      [nodes]="canvasNodes"
      [edges]="canvasEdges"
      [showGrid]="false"
      [enablePan]="false"
      [enableZoom]="false"
      [enableConnections]="true"
      [showToolbar]="false"
      [mouseInteractionsEnabled]="false"
      [fallbackMessage]="fallbackMessage">
      
      <!-- Node template for rendering agent nodes (using exact same component as workflow editor) -->
      <ng-template 
        #nodeTemplate 
        let-node 
        let-selected="selected"
        let-onDelete="onDelete"
        let-onMove="onMove"
        let-onSelect="onSelect"
        let-onStartConnection="onStartConnection"
      >
        <app-agent-node
          [node]="node"
          [selected]="selected"
        >
        </app-agent-node>
      </ng-template>
    </app-canvas-board>
  </div>
</div>
