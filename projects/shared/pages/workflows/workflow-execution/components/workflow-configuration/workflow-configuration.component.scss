.workflow-configuration-container {
  display: flex;
  flex-direction: column;
  height: 82vh;
  width: 100%;
  background: var(--color-background-primary, #ffffff);
  border-radius: 8px;
  overflow: hidden;
  cursor: none;
  pointer-events: none; // Disable interactions on edges
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  min-height: 500px;
  cursor: none;
  pointer-events: none; // Disable interactions on edges
  padding: 5px;
  
  // Ensure canvas board fits properly in read-only mode
  ::ng-deep app-canvas-board {
    .canvas-toolbar {
      display: none !important; // Hide toolbar completely
    }
    
    // Hide the canvas tools toolbar (the buttons you see at bottom)
    .canvas-tools-toolbar {
      display: none !important;
    }
    
    .canvas-container {
      height: 100%;
      width: 100%;
    }
    
    // Style the connection lines
    .canvas-edges {
      pointer-events: none; // Disable interactions on edges
    }
    
    // Disable node interactions but keep visual styling
    .canvas-nodes {
      .agent-node {
        pointer-events: none; // Disable drag and other interactions
        cursor: default;
        
        // Keep hover effects for visual feedback but no interactions
        &:hover {
          transform: none !important;
        }
      }
    }
  }
}

// Using app-agent-node component from workflow editor - no custom styles needed
// The component already has all the necessary styling for pixel-perfect matching

.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;

  .grid-pattern {
    width: 100%;
    height: 100%;
  }
}

.workflow-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 2;
  padding: 20px;
  box-sizing: border-box;
}

.edges-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 3;

  .edge-line {
    position: absolute;
    height: 2px;
    background: linear-gradient(90deg, var(--color-brand-primary, #3b82f6) 0%, var(--color-brand-secondary, #06b6d4) 100%);
    border-radius: 1px;
    
    &::after {
      content: '';
      position: absolute;
      right: -6px;
      top: -3px;
      width: 0;
      height: 0;
      border-left: 8px solid var(--color-brand-secondary, #06b6d4);
      border-top: 4px solid transparent;
      border-bottom: 4px solid transparent;
    }
  }
}

.nodes-container {
  position: relative;
  z-index: 4;

  .agent-node {
    position: absolute;
    width: 300px;
    background: var(--color-background-primary, #ffffff);
    border: 2px solid var(--color-border-primary, #e5e7eb);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    .node-header {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 16px;
      background: linear-gradient(135deg, var(--color-brand-primary, #3b82f6) 0%, var(--color-brand-secondary, #06b6d4) 100%);
      border-radius: 10px 10px 0 0;

      .node-icon {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
      }

      .node-info {
        flex: 1;
        min-width: 0;

        .node-title {
          margin: 0;
          font-size: 16px;
          font-weight: 600;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .node-serial {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }

    .node-content {
      padding: 16px;

      .node-field {
        margin-bottom: 12px;
        
        &:last-child {
          margin-bottom: 0;
        }

        .field-label {
          display: block;
          font-size: 12px;
          font-weight: 600;
          color: var(--color-text-secondary, #6b7280);
          text-transform: uppercase;
          letter-spacing: 0.5px;
          margin-bottom: 4px;
        }

        .field-value {
          display: block;
          font-size: 14px;
          color: var(--color-text-primary, #1f2937);
          line-height: 1.4;
          word-wrap: break-word;
        }
      }
    }

    .connection-point {
      position: absolute;
      width: 12px;
      height: 12px;
      background: var(--color-brand-primary, #3b82f6);
      border: 2px solid var(--color-background-primary, #ffffff);
      border-radius: 50%;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &.input {
        left: -6px;
        top: 50%;
        transform: translateY(-50%);
      }

      &.output {
        right: -6px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;

  .empty-state-content {
    text-align: center;
    color: var(--color-text-secondary, #6b7280);

    h3 {
      margin: 16px 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: var(--color-text-primary, #1f2937);
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }
}

.configuration-legend {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 16px 24px;
  background: var(--color-background-secondary, #f8f9fa);
  border-top: 1px solid var(--color-border-primary, #e5e7eb);

  .legend-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .legend-text {
      font-size: 12px;
      color: var(--color-text-secondary, #6b7280);
    }

    .agent-icon-small {
      width: 24px;
      height: 24px;
      background: linear-gradient(135deg, var(--color-brand-primary, #3b82f6) 0%, var(--color-brand-secondary, #06b6d4) 100%);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .legend-line {
      width: 24px;
      height: 2px;
      background: linear-gradient(90deg, var(--color-brand-primary, #3b82f6) 0%, var(--color-brand-secondary, #06b6d4) 100%);
      border-radius: 1px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        right: -4px;
        top: -2px;
        width: 0;
        height: 0;
        border-left: 6px solid var(--color-brand-secondary, #06b6d4);
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
      }
    }
  }
}

// Responsive design
@media (max-width: 1024px) {
  .configuration-header {
    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: flex-start;

      .header-stats {
        align-self: stretch;
        justify-content: space-around;
      }
    }
  }

  .agent-node {
    width: 280px !important;
  }
}

@media (max-width: 768px) {
  .configuration-header {
    padding: 16px 20px;

    .header-left {
      gap: 8px;

      .header-text {
        .configuration-title {
          font-size: 18px;
        }

        .configuration-subtitle {
          font-size: 12px;
        }
      }
    }

    .header-stats {
      gap: 16px;

      .stat-item {
        .stat-value {
          font-size: 20px;
        }
      }
    }
  }

  .agent-node {
    width: 260px !important;

    .node-header {
      padding: 12px;

      .node-icon {
        width: 32px;
        height: 32px;
      }

      .node-info {
        .node-title {
          font-size: 14px;
        }
      }
    }

    .node-content {
      padding: 12px;

      .node-field {
        margin-bottom: 8px;

        .field-value {
          font-size: 13px;
        }
      }
    }
  }

  .configuration-legend {
    padding: 12px 20px;
    gap: 16px;
  }
}
