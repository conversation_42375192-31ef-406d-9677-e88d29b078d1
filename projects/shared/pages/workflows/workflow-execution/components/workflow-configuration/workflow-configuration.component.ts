import { Component, Input, OnInit, OnChanges, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '@ava/play-comp-library';
import { CanvasBoardComponent, CanvasNode, CanvasEdge } from '@shared/components/canvas-board/canvas-board.component';
import { AgentNodeComponent } from '../../../workflow-editor/components/agent-node/agent-node.component';
import { WorkflowNode, WorkflowEdge } from '../../../workflow-editor/services/workflow-graph.service';

@Component({
  selector: 'app-workflow-configuration',
  standalone: true,
  imports: [
    CommonModule,
    AgentNodeComponent,
    CanvasBoardComponent
  ],
  templateUrl: './workflow-configuration.component.html',
  styleUrls: ['./workflow-configuration.component.scss']
})
export class WorkflowConfigurationComponent implements OnInit, OnChanges {
  @Input() workflowData: any = null;
  @Input() workflowName: string = 'Workflow';
  @ViewChild('nodeTemplate') nodeTemplate!: TemplateRef<any>;

  canvasNodes: CanvasNode[] = [];
  canvasEdges: CanvasEdge[] = [];
  
  // Canvas board configuration - read-only mode
  showGrid: boolean = true;
  enablePan: boolean = true;
  enableZoom: boolean = true;
  enableConnections: boolean = false; // Read-only, no new connections
  showToolbar: boolean = false; // Hide toolbar in read-only mode
  mouseInteractionsEnabled: boolean = false; // Disable interactions
  fallbackMessage: string = 'No workflow configuration available';
  navigationHints: string[] = [
    'This is a read-only view of your workflow configuration',
    'Use mouse wheel to zoom',
    'Drag to pan around the canvas'
  ];

  ngOnInit(): void {
    if (this.workflowData) {
      this.processWorkflowData();
    }
  }

  ngOnChanges(): void {
    if (this.workflowData) {
      this.processWorkflowData();
    }
  }

  private processWorkflowData(): void {
    // Support both old (pipeLineAgents) and new (workflowAgents) API structures
    const agents = this.workflowData?.pipeLineAgents || this.workflowData?.workflowAgents;
    
    if (!agents || !Array.isArray(agents)) {
      console.warn('No agents found in workflow data');
      return;
    }

    // Convert agents to canvas nodes - handle both old and new structures
    this.canvasNodes = agents.map((agentItem: any, index: number) => {
      // Handle old structure (pipeLineAgents with nested agent object)
      const agent = agentItem.agent || agentItem;
      
      // For new structure, map the properties correctly
      const agentData = agentItem.agent ? {
        // Old structure
        id: agent.id,
        name: agent.name,
        role: agent.role,
        goal: agent.goal,
        backstory: agent.backstory,
        task: agent.task,
        llm: agent.llm,
        tools: agent.tools || []
      } : {
        // New structure
        id: agentItem.agentId,
        name: agentItem.name,
        role: agentItem.agentDetails?.role || 'Agent',
        goal: agentItem.agentDetails?.goal || '',
        backstory: agentItem.agentDetails?.backstory || '',
        task: {
          description: agentItem.description || '',
          expectedOutput: agentItem.agentDetails?.expectedOutput || ''
        },
        llm: {
          model: agentItem.modelName || '',
          modelDeploymentName: agentItem.modelDeploymentName || '',
          topP: agentItem.agentDetails?.topP || 0.95,
          maxToken: agentItem.agentDetails?.maxToken || 4000,
          temperature: agentItem.agentDetails?.temperature || 0.3
        },
        tools: agentItem.agentDetails?.toolReferences || []
      };
      
      return {
        id: `agent-${agentData.id || index}`,
        type: 'agent',
        position: { 
          x: 100 + (index * 350), // Horizontal layout with spacing
          y: 100 
        },
        data: {
          // Fields expected by app-agent-node component
          label: agentData.name || `Agent ${index + 1}`,
          description: agentData.task?.description || agentData.goal || 'No description available',
          model: agentData.llm?.model || agentData.llm?.modelDeploymentName || 'No model specified',
          tools: agentData.tools || [],
          agentTools: agentData.tools || [],
          
          // Additional fields for display
          agentId: agentData.id || `agent-${index}`,
          name: agentData.name || `Agent ${index + 1}`,
          role: agentData.role || 'Agent',
          goal: agentData.goal || '',
          backstory: agentData.backstory || '',
          task: {
            description: agentData.task?.description || '',
            expectedOutput: agentData.task?.expectedOutput || ''
          },
          serial: agentItem.serial || (index + 1),
          llm: agentData.llm || null
        }
      };
    });

    // Create edges to connect agents in sequence
    this.canvasEdges = [];
    for (let i = 0; i < this.canvasNodes.length - 1; i++) {
      this.canvasEdges.push({
        id: `edge-${i}`,
        source: this.canvasNodes[i].id,
        target: this.canvasNodes[i + 1].id,
        animated: false
      });
    }
  }

  // No event handlers needed for read-only configuration view
}
