<div class="workflow-execution-container">
  <!-- SVG Gradient Definitions for Icons -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
        <stop offset="0%" stop-color="#6566CD" />
        <stop offset="100%" stop-color="#F96CAB" />
      </linearGradient>
    </defs>
  </svg>
  <div class="execution-content" role="main">
    <!-- Left Panel: Workflow Playground with Execute Button -->
    <div class="left-panel" [class.collapsed]="isPlaygroundCollapsed" role="region" aria-label="Workflow Input Panel">
      <app-workflow-playground
        [agents]="agents"
        [isCollapsed]="isPlaygroundCollapsed"
        [workflowName]="workflowName"
        [isLoading]="isWorkflowExecuting"
        (backClicked)="onPlaygroundBackClicked()"
        (collapseToggled)="onPlaygroundCollapseToggled($event)"
        (agentInputChanged)="onAgentInputChanged($event)"
        (agentFileSelected)="onAgentFileSelected($event)"
        (messageSent)="onMessageSent($event)"
        class="playground-component"
        role="region"
        aria-label="Workflow Playground">
      </app-workflow-playground>

      <!-- File Upload Section -->
      <div class="form-fields column-gap" *ngIf="!isPlaygroundCollapsed">
        <div class="input__field--wrapper">
          <div class="upload-container">
            <div class="drag-drop-area" 
                 (dragover)="onDragOver($event)" 
                 (dragleave)="onDragLeave($event)" 
                 (drop)="onFilesDrop($event)"
                 [class.drag-over]="isDragOver"
                 [class.has-files]="selectedFiles && selectedFiles.length > 0">
              
              <!-- Upload Area Content -->
              <div class="upload-content" *ngIf="!selectedFiles || selectedFiles.length === 0">
                <p class="upload-text">Supported file formats</p>
                <p class="upload-subtext">Upload only .zip</p>
                <ava-button
                label="Upload File"
                  variant="primary"
                  size="medium"
                  iconName="upload"
                  (click)="onFileUploadClick()">
                </ava-button>
              </div>
              
              <!-- Display uploaded files -->
              <div class="uploaded-files" *ngIf="selectedFiles && selectedFiles.length > 0">
                <div class="files-header">
                  <span class="files-count">{{ selectedFiles.length }} file(s) selected</span>
                  <ava-button
                    variant="secondary"
                    size="small"
                    label="Add More"
                    (click)="onFileUploadClick()">
                    <ava-icon iconName="plus" [iconSize]="14" iconColor="#03ACC1"></ava-icon>
                  </ava-button>
                </div>
                <div class="file-list" [class.scrollable]="selectedFiles.length > 3">
                  <div class="file-item" *ngFor="let file of selectedFiles; let fileIndex = index">
                    <div class="file-info">
                      <ava-icon iconName="FileText" [iconSize]="16" iconColor="#666"></ava-icon>
                      <span class="file-name">{{ file.name }}</span>
                      <span class="file-size">({{ formatFileSize(file.size) }})</span>
                    </div>
                    <button class="remove-file" (click)="removeUploadedFile(fileIndex)" title="Remove file">
                      <ava-icon iconName="x" [iconSize]="12" iconColor="#e53e3e"></ava-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Execute Button -->
      <div class="execute-button-container" *ngIf="!isPlaygroundCollapsed">
        <ava-button
          label="Execute Workflow"
          variant="primary"
          size="large"
          [disabled]="!areAllInputsFilled"
          (click)="areAllInputsFilled && executeWorkflow()"
          [title]="areAllInputsFilled ? 'Execute the workflow with the provided inputs' : 'Please fill in all required inputs before executing'"
          aria-label="Execute Workflow"
          [attr.aria-disabled]="!areAllInputsFilled"
        >
        </ava-button>
      </div>
    </div>

    <!-- Right Panel: Three Sections (Execution, Output, Configuration) -->
    <div class="right-panel" [class.expanded]="isPlaygroundCollapsed" role="region" aria-label="Workflow Results">
      <!-- Tab Navigation -->
      <div class="tabs-header">
        <div class="tabs-container">
          <ava-tabs
            [tabs]="navigationTabs"
            [activeTabId]="activeTabId"
            variant="button"
            [showContentPanels]="false"
            (tabChange)="onTabChange($event)"
            ariaLabel="Workflow sections navigation"
          ></ava-tabs>
        </div>
        <div class="header-actions">
          <ava-button
            label="Send for Approval"
            variant="primary"
            size="medium"
            (click)="sendWorkflowForApproval()"
          >
          </ava-button>
        </div>
      </div>

      <!-- Content Sections -->
      <div class="content-sections">
        <!-- Execution Section -->
        <div *ngIf="activeTabId === 'nav-home'" class="section-content execution-section">
          <!-- Execution Monitor Header -->
          <div class="execution-monitor-header">
            <h2 class="monitor-title">Execution Monitor</h2>
            <div class="progress-section">
              <span class="progress-label">Overall Progress</span>
              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-fill" [style.width.%]="progress"></div>
                </div>
                <span class="progress-text">{{ progress }}%</span>
              </div>
            </div>
          </div>

          <!-- Two Column Layout: Pipeline Steps + Execution Logs -->
          <div class="execution-content-grid">
            <!-- Left Column: Pipeline Steps -->
            <div class="pipeline-steps-section">
              <div class="coming-soon-overlay">
                <div class="coming-soon-content">
                  <div class="coming-soon-icon">
                    <div class="pulse-ring"></div>
                    <div class="pulse-ring-2"></div>
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor"/>
                    </svg>
                  </div>
                  <h2 class="coming-soon-title">Coming Soon</h2>
                  <p class="coming-soon-subtitle">Pipeline steps will be available soon</p>
                  <div class="coming-soon-dots">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                  </div>
                </div>
              </div>
              <h3>Pipeline Steps</h3>
              <div class="pipeline-agents-list">
                <div
                  *ngFor="let agent of agents; let i = index"
                  class="pipeline-agent-item"
                  [class.active]="i === 0"
                  [class.completed]="false">
                  <div class="agent-icon">
                    <ava-icon iconName="bot" [iconSize]="20" iconColor="#666"></ava-icon>
                  </div>
                  <div class="agent-info">
                    <span class="agent-name">{{ agent.name }}</span>
                    <span class="agent-status" *ngIf="i === 0">Agent 1 is currently working</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Right Column: Execution Logs -->
            <div class="execution-logs-section">
              <div class="logs-header">
                <h3 class="logs-title">Execution Logs</h3>
              </div>
              <div class="logs-content">
                <div class="logs-scroll-container">
                  <div class="log-entry" *ngFor="let log of filteredLogs">
                    <span class="log-message" [style.color]="log.color">{{ log.content }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Output Section -->
        <div *ngIf="activeTabId === 'nav-products'" class="section-content output-section">
          <app-agent-output
            [outputs]="taskMessage"
            (export)="exportResults('output')"
          ></app-agent-output>
        </div>

        <!-- Configuration Section -->
        <div *ngIf="activeTabId === 'nav-services'" class="section-content configuration-section">
          <app-workflow-configuration
            [workflowData]="fullWorkflowData"
            [workflowName]="workflowName">
          </app-workflow-configuration>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden file input -->
  <input
    #fileUploadInput
    type="file"
    style="display: none"
    accept=".zip"
    multiple
    (change)="onFilesSelected($event)">
</div>
