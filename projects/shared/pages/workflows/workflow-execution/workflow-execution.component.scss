$header-bg: #e9effd;
$main-color: #1a46a7;

::ng-deep .ava-tabs {
  background: none !important;
}

::ng-deep .ava-tabs__container {
  border-radius: none !important;
  border: none !important;
  padding: 0 !important;
  box-shadow: none !important;
  background: none !important;
}

::ng-deep .ava-tabs__list {
  padding: 0 !important;
}

.workflow-execution-container {
  display: flex;
  flex-direction: column;
  height: 93vh;
  overflow: hidden;
  .execution-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    flex-shrink: 0;

    .execution-title {
      h1 {
        margin: 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color, #333);
      }

      .header-buttons {
        display: flex;
        gap: 12px;
      }
    }

    .execution-actions {
      display: flex;
      gap: 12px;

      .back-button,
      .edit-button {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 5px;
        font-weight: 500;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        svg {
          margin-right: 6px;
        }
      }

      .back-button {
        background-color: var(--bg-muted, #f5f5f5);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);

        &:hover {
          background-color: var(--bg-muted-hover, #e9e9e9);
        }
      }

      .edit-button {
        background-color: var(--card-bg, #fff);
        border: 1px solid var(--border-color, #e0e0e0);
        color: var(--text-color, #333);

        &:hover {
          background-color: var(--card-bg-hover, #f9f9f9);
          border-color: var(--border-color-dark, #d0d0d0);
        }
      }
    }
  }

  // Main content with 2 panels (left input panel + right results panel)
  .execution-content {
    display: flex;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    gap: 20px;
    padding: 20px;

    // Left Panel: Workflow Playground with Execute Button
    .left-panel {
      flex: 2.5;
      display: flex;
      flex-direction: column;
      min-width: 0;
      background: transparent;
      transition: all 0.3s ease;

      &.collapsed {
        flex: 0;
        min-width: 3%;
        width: 3%;
        overflow: hidden;
      }

      .playground-component {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-height: 0;
      }

      // File Upload Section (matching DA pipeline styling)
      .form-fields {
        padding: 16px;
        background-color: var(--card-bg, white);
        // border-top: 1px solid var(--border-color, #e0e0e0);
        flex-shrink: 0;

        &.column-gap {
          display: flex;
          flex-direction: column;
          gap: 16px;
        }

        .input__field--wrapper {
          display: flex;
          flex-direction: column;
          gap: 8px;

          label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color, #333);
            display: flex;
            align-items: center;
            gap: 8px;

            &.tooltip {
              .icon-info {
                width: 16px;
                height: 16px;
                color: var(--text-secondary, #666);
                cursor: help;
              }
            }
          }

          .upload-container {
            width: 100%;
            
            .drag-drop-area {
              border: 2px dashed var(--border-color, #e0e0e0);
              border-radius: 8px;
              padding: 24px;
              text-align: center;
              transition: all 0.3s ease;
              background-color: var(--card-bg, white);
              height: 200px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              
              &.drag-over {
                border-color: var(--primary-color, #1a46a7);
                background-color: var(--hover-bg, #f0f8ff);
                border-style: solid;
              }
              
              &.has-files {
                min-height: auto;
                padding: 16px;
                text-align: left;
              }
              
              .upload-content {
                display: flex;
                flex-direction: column;
                align-items: center;
\                
                .upload-icon-container {
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  width: 64px;
                  height: 64px;
                  border-radius: 50%;
                  background-color: var(--bg-secondary, #f8f9fa);
                  margin-bottom: 8px;
                }
                
                .upload-text {
                  font-size: 16px;
                  font-weight: 600;
                  color: var(--text-color, #333);
                  margin: 0 0 4px 0;
                }
                
                .upload-subtext {
                  font-size: 14px;
                  color: var(--text-secondary, #666);
                  margin: 0 0 16px 0;
                  line-height: 1.4;
                }
              }
              
              .uploaded-files {
                width: 100%;
                
                .files-header {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  margin-bottom: 16px;
                  padding-bottom: 8px;
                  border-bottom: 1px solid var(--border-color, #e0e0e0);
                  
                  .files-count {
                    font-size: 14px;
                    font-weight: 500;
                    color: var(--text-color, #333);
                  }
                }
                
                .file-list {
                  display: flex;
                  flex-direction: column;
                  gap: 8px;
                  
                  &.scrollable {
                    max-height: 150px;
                    overflow-y: auto;
                    padding-right: 4px;
                    
                    // Custom scrollbar styling
                    &::-webkit-scrollbar {
                      width: 6px;
                    }
                    
                    &::-webkit-scrollbar-track {
                      background: var(--bg-secondary, #f8f9fa);
                      border-radius: 3px;
                    }
                    
                    &::-webkit-scrollbar-thumb {
                      background: var(--border-color, #e0e0e0);
                      border-radius: 3px;
                      
                      &:hover {
                        background: var(--text-secondary, #666);
                      }
                    }
                    
                    // Firefox scrollbar styling
                    scrollbar-width: thin;
                    scrollbar-color: var(--border-color, #e0e0e0) var(--bg-secondary, #f8f9fa);
                  }
                  
                  .file-item {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 12px;
                    background-color: var(--bg-secondary, #f8f9fa);
                    border-radius: 6px;
                    border: 1px solid var(--border-color, #e0e0e0);
                    
                    .file-info {
                      display: flex;
                      align-items: center;
                      gap: 8px;
                      flex: 1;
                      
                      .file-name {
                        font-size: 14px;
                        font-weight: 500;
                        color: var(--text-color, #333);
                      }
                      
                      .file-size {
                        font-size: 12px;
                        color: var(--text-secondary, #666);
                      }
                    }
                    
                    .remove-file {
                      background: none;
                      border: none;
                      cursor: pointer;
                      padding: 4px;
                      border-radius: 4px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      transition: background-color 0.2s ease;
                      
                      &:hover {
                        background-color: var(--error-bg, #fee);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }

      .execute-button-container {
        padding: 16px;
        background-color: var(--card-bg, white);
        border-radius: 0 0 8px 8px;
        // border-top: 1px solid var(--border-color, #e0e0e0);
        flex-shrink: 0;

        ::ng-deep .ava-button {
          width: 100% !important;
          min-height: 48px;
          font-weight: 600;
          font-size: 16px;
        }

        ::ng-deep .ava-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    // Right Panel: Three Sections with Tabs
    .right-panel {
      flex: 5.5;
      display: flex;
      flex-direction: column;
      background-color: var(--card-bg, white);
      border-radius: 8px;
      box-shadow: 0 4px 15px var(--card-shadow, rgba(0, 0, 0, 0.05));
      overflow: hidden;
      transition: all 0.3s ease;

      &.expanded {
        flex: 8;
      }

      // Tab Navigation Header
      .tabs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 16px;
        background: var(--build-agents-nav-bg);
        height: 64px;
        flex-shrink: 0;

        .tabs-container {
          flex: 1;
        }

        .header-actions {
          flex-shrink: 0;
          margin-left: 16px;
        }
      }

      // Content Sections
      .content-sections {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .section-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          overflow: hidden;

          &.execution-section {
            display: flex;
            flex-direction: column;

            .execution-monitor-header {
              padding: 20px 24px;
              border-bottom: 1px solid var(--border-color, #e0e0e0);
              background-color: var(--card-bg, white);
              flex-shrink: 0;

              .monitor-title {
                font-size: 18px;
                font-weight: 600;
                color: var(--text-color, #333);
                margin-bottom: 16px;
              }

              h2 {
                margin: 0 0 16px 0;
                font-size: 20px;
                font-weight: 600;
                color: var(--text-color, #333);
              }

              .progress-section {
                .progress-label {
                  font-size: 14px;
                  color: var(--text-muted, #666);
                  margin-bottom: 8px;
                  display: block;
                }

                .progress-container {
                  display: flex;
                  align-items: center;
                  gap: 12px;

                  .progress-bar {
                    flex: 1;
                    height: 8px;
                    background-color: var(--bg-muted, #f0f0f0);
                    border-radius: 4px;
                    overflow: hidden;

                    .progress-fill {
                      height: 100%;
                      background: var(--gradient-primary);
                      border-radius: 4px;
                      transition: width 0.3s ease;
                    }
                  }

                  .progress-text {
                    font-size: 14px;
                    font-weight: 600;
                    color: var(--text-color, #333);
                    min-width: 40px;
                  }
                }
              }
            }

            .execution-content-grid {
              display: flex;
              flex: 1;
              min-height: 0;
              gap: 16px;
              padding: 20px;
              background-color: var(--card-bg, white);

              .pipeline-steps-section,
              .execution-logs-section {
                flex: 1;
                min-height: 0;
                background-color: #F7F8F9;
                border-radius: 16px;
                display: flex;
                flex-direction: column;
                overflow: hidden;
              }

              .pipeline-steps-section {
                position: relative; // This ensures the overlay is contained within this section
                
                .coming-soon-overlay {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: rgba(255, 255, 255, 0.7);
                  backdrop-filter: blur(3px);
                  -webkit-backdrop-filter: blur(3px);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  z-index: 1000;
                  border-radius: 16px;
                  animation: fadeInOverlay 0.8s ease-out;
                }
              
                .coming-soon-content {
                  text-align: center;
                  padding: 40px;
                  animation: slideInUp 1s ease-out 0.3s both;
                }
              
                .coming-soon-icon {
                  position: relative;
                  display: inline-block;
                  margin-bottom: 32px;
                  
                  svg {
                    width: 64px;
                    height: 64px;
                    color: #667eea;
                    position: relative;
                    z-index: 3;
                    animation: sparkle 2s ease-in-out infinite;
                  }
              
                  .pulse-ring {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 100px;
                    height: 100px;
                    border: 2px solid #667eea;
                    border-radius: 50%;
                    opacity: 0;
                    animation: pulseRing 2s ease-out infinite;
                  }
              
                  .pulse-ring-2 {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 120px;
                    height: 120px;
                    border: 1px solid #8b5cf6;
                    border-radius: 50%;
                    opacity: 0;
                    animation: pulseRing 2s ease-out infinite 0.5s;
                  }
                }
              
                .coming-soon-title {
                  font-family: 'Mulish', sans-serif;
                  font-size: 36px;
                  font-weight: 700;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  -webkit-background-clip: text;
                  -webkit-text-fill-color: transparent;
                  background-clip: text;
                  margin: 0 0 16px 0;
                  animation: titleGlow 3s ease-in-out infinite alternate;
                }
              
                .coming-soon-subtitle {
                  font-family: 'Mulish', sans-serif;
                  font-size: 18px;
                  font-weight: 400;
                  color: #6b7280;
                  margin: 0 0 32px 0;
                  opacity: 0;
                  animation: fadeInText 1s ease-out 0.8s both;
                }
              
                .coming-soon-dots {
                  display: flex;
                  justify-content: center;
                  gap: 8px;
                  
                  .dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background: #667eea;
                    opacity: 0.3;
                    animation: dotPulse 1.5s ease-in-out infinite;
              
                    &:nth-child(1) {
                      animation-delay: 0s;
                    }
                    
                    &:nth-child(2) {
                      animation-delay: 0.2s;
                    }
                    
                    &:nth-child(3) {
                      animation-delay: 0.4s;
                    }
                  }
                }
              
                // Animations
                @keyframes fadeInOverlay {
                  from {
                    opacity: 0;
                    backdrop-filter: blur(0px);
                  }
                  to {
                    opacity: 1;
                    backdrop-filter: blur(3px);
                  }
                }
              
                @keyframes slideInUp {
                  from {
                    opacity: 0;
                    transform: translateY(30px);
                  }
                  to {
                    opacity: 1;
                    transform: translateY(0);
                  }
                }
              
                @keyframes sparkle {
                  0%, 100% {
                    transform: scale(1) rotate(0deg);
                  }
                  25% {
                    transform: scale(1.1) rotate(90deg);
                  }
                  50% {
                    transform: scale(1) rotate(180deg);
                  }
                  75% {
                    transform: scale(1.1) rotate(270deg);
                  }
                }
              
                @keyframes pulseRing {
                  0% {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(0.5);
                  }
                  50% {
                    opacity: 0.3;
                  }
                  100% {
                    opacity: 0;
                    transform: translate(-50%, -50%) scale(1.2);
                  }
                }
              
                @keyframes titleGlow {
                  0% {
                    filter: brightness(1);
                  }
                  100% {
                    filter: brightness(1.2);
                  }
                }
              
                @keyframes fadeInText {
                  from {
                    opacity: 0;
                    transform: translateY(10px);
                  }
                  to {
                    opacity: 1;
                    transform: translateY(0);
                  }
                }
              
                @keyframes dotPulse {
                  0%, 100% {
                    opacity: 0.3;
                    transform: scale(1);
                  }
                  50% {
                    opacity: 1;
                    transform: scale(1.2);
                  }
                }
              
                pointer-events: none;
                h3 {
                  margin: 0;
                  padding: 20px 24px 16px 24px;
                  font-size: 18px;
                  font-weight: 600;
                  color: var(--text-color, #333);
                  background-color: #F7F8F9;
                  flex-shrink: 0;
                  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                }

                .pipeline-agents-list {
                  flex: 1;
                  min-height: 0;
                  padding: 0 24px 24px 24px;
                  background-color: #F7F8F9;
                  overflow-y: auto;

                  .pipeline-agent-item {
                    display: flex;
                    align-items: center;
                    gap: 16px;
                    padding: 16px 20px;
                    margin-bottom: 12px;
                    border: 1px solid #E5E7EB;
                    border-radius: 16px;
                    background-color: white;
                    transition: all 0.2s ease;

                    &.active {
                      border-color: #6566cd;
                      background-color: white;
                      box-shadow: 0 2px 8px rgba(101, 102, 205, 0.1);
                    }

                    &.completed {
                      border-color: #10b981;
                      background-color: white;
                      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.1);
                    }

                    .agent-icon {
                      width: 40px;
                      height: 40px;
                      border-radius: 50%;
                      background-color: #F3F4F6;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      flex-shrink: 0;
                      border: 2px solid white;
                    }

                    .agent-info {
                      flex: 1;
                      display: flex;
                      flex-direction: column;

                      .agent-name {
                        font-size: 16px;
                        font-weight: 600;
                        color: var(--text-color, #333);
                        margin-bottom: 4px;
                      }

                      .agent-status {
                        font-size: 14px;
                        color: #6B7280;
                      }
                    }
                  }
                }
              }

              .execution-logs-section {
                display: flex;
                flex-direction: column;
                height: 100%;
                background-color: #F7F8F9;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

                .logs-header {
                  padding: 16px 20px;
                  background-color: #F7F8F9;
                  border-bottom: 1px solid #E5E7EB;
                  position: sticky;
                  top: 0;
                  z-index: 10;
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);

                  h3 {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--text-color, #333);
                    display: flex;
                    align-items: center;
                    gap: 8px;
                  }

                  .logs-subtitle {
                    font-size: 12px;
                    color: #6B7280;
                    margin-top: 4px;
                  }
                }

                .logs-content {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  min-height: 0;
                  background-color: #F7F8F9;
                  border-radius: 0 0 8px 8px;

                  .logs-scroll-container {
                    flex: 1;
                    overflow-y: auto;
                    padding: 12px 20px;
                    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                    font-size: 13px;
                    line-height: 1.5;
                    scrollbar-width: thin;
                    scrollbar-color: #D1D5DB #F3F4F6;

                    &::-webkit-scrollbar {
                      width: 6px;
                      height: 6px;
                    }

                    &::-webkit-scrollbar-track {
                      background: #F3F4F6;
                      border-radius: 3px;
                    }

                    &::-webkit-scrollbar-thumb {
                      background-color: #D1D5DB;
                      border-radius: 3px;
                    }

                    .log-entry {
                      padding: 4px 0;
                      margin: 0;
                      border-bottom: 1px solid #F3F4F6;

                      &:last-child {
                        border-bottom: none;
                      }

                      .log-message {
                        display: block;
                        word-break: break-word;
                        white-space: pre-wrap;
                        color: #374151;
                        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
                        font-size: 13px;
                        line-height: 1.5;
                      }
                    }
                  }

                  .logs-footer {
                    padding: 12px 20px;
                    border-top: 1px solid #E5E7EB;
                    background-color: #FFFFFF;
                    text-align: center;

                    .show-more-btn {
                      background: none;
                      border: 1px solid #E5E7EB;
                      color: #4B5563;
                      font-size: 12px;
                      font-weight: 500;
                      cursor: pointer;
                      padding: 6px 16px;
                      border-radius: 6px;
                      transition: all 0.2s ease;
                      text-decoration: none;

                      &:hover {
                        background-color: #F9FAFB;
                        border-color: #D1D5DB;
                        color: #111827;
                      }

                      &:active {
                        background-color: #F3F4F6;
                      }
                    }
                  }
                }
              }
            }
          }

          &.output-section {
            app-agent-output {
              flex: 1;
              display: flex;
              flex-direction: column;
            }
          }

          &.configuration-section {
            padding: 0;
            overflow: hidden;
            height: 100%;

            .configuration-content {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              text-align: center;

              p {
                color: var(--text-color, #666);
                font-size: 16px;
                margin: 0;
                font-style: italic;
              }
            }
          }
        }
      }
    }

  }
}
::ng-deep nav.ava-tabs__list {
  background: $header-bg;
  padding: 4px;
}

::ng-deep button.ava-button.primary.active {
  background: #616161;
  color: #fff;
}

::ng-deep .column-header .ava-tabs[data-variant="button"] .ava-tabs__tab--pill {
  border-radius: 8px !important;
  padding: 12px 16px !important;
  font-family: "Mulish";
}

::ng-deep
  .ava-tabs[data-variant="button"]
  .ava-tabs__tab--active
  .ava-tabs__tab-text {
  color: white;
}

::ng-deep .ava-tabs__tab-text {
  color: #4c515b;
  font-family: "Mulish";
  font-weight: 600;
}

::ng-deep .right-section-header .ava-button.secondary {
  color: #1a46a7;
  border: none;
}

::ng-deep .right-section-header .ava-button.secondary:hover {
  color: #1a46a7;
  border: none;
}

.right-section-header {
  text-align: end;
}

// Responsive design for workflow execution
@media (max-width: 1024px) {
  .workflow-execution-container {
    .execution-content {
      gap: 16px;
      height: 94vh;
    }
  }
}

@media (max-width: 768px) {
  .workflow-execution-container {
    .execution-header {
      padding: 16px 20px;

      .execution-title h1 {
        font-size: 20px;
      }
    }

    .execution-content {
      flex-direction: column;
      gap: 12px;
      padding: 12px;

      .left-panel {
        flex: 1;
        min-height: 400px;
      }

      .right-panel {
        flex: 1;
        min-height: 300px;

        .execution-content-grid {
          flex-direction: column;
          gap: 12px;
          padding: 16px;

          .pipeline-steps-section,
          .execution-logs-section {
            flex: 1;
            min-height: 200px;
            border-radius: 16px;
            background-color: #F7F8F9;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .workflow-execution-container {
    .execution-header {
      padding: 12px 16px;

      .execution-title h1 {
        font-size: 18px;
      }

      .execution-actions {
        gap: 8px;

        .back-button,
        .edit-button {
          padding: 8px 12px;
          font-size: 14px;
        }
      }
    }

    .execution-content {
      gap: 8px;
      padding: 8px;

      .left-panel {
        flex: 1;
        min-height: 350px;
      }

      .right-panel {
        flex: 1;
        min-height: 250px;

        .execution-content-grid {
          flex-direction: column;
          gap: 12px;
          padding: 12px;

          .pipeline-steps-section,
          .execution-logs-section {
            flex: 1;
            min-height: 180px;
            border-radius: 16px;
            background-color: #F7F8F9;

            .pipeline-agents-list,
            .logs-content {
              padding: 12px 16px;
            }

            h3 {
              padding: 16px 20px 12px 20px;
            }
          }
        }

        .execution-monitor-header {
          padding: 16px 20px;
        }
      }
    }
  }
}
