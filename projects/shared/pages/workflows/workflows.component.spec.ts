import { ComponentFixture, TestBed } from '@angular/core/testing';
import { WorkflowsComponent } from './workflows.component';
import { of, Subject, throwError } from 'rxjs';
import { WorkflowService } from '@shared/services/workflow.service';
import { WORKFLOW_BASE_ACTIONS } from './workflows-actions';

describe('WorkflowsComponent', () => {
  let component: WorkflowsComponent;
  let fixture: ComponentFixture<WorkflowsComponent>;
  let workflowServiceSpy: jasmine.SpyObj<WorkflowService>;

  const mockWorkflows: any[] = [
    {
      name: 'Product Recommendation Engine',
      createdAt: '2025-02-15T00:00:00Z',
      description: 'Engine for recommending products',
      tags: [{ label: 'AI' }],
      id: '1',
    },
    {
      name: 'Customer Support Ticket Triage',
      createdAt: '2025-01-28T00:00:00Z',
      tags: [{ label: 'Support' }],
      id: '2',
    },
  ];

  beforeEach(async () => {
    const workflowSpy = jasmine.createSpyObj('WorkflowService', [
      'fetchAllWorkflows',
    ]);

    await TestBed.configureTestingModule({
      declarations: [WorkflowsComponent],
      providers: [{ provide: WorkflowService, useValue: workflowSpy }],
    }).compileComponents();

    fixture = TestBed.createComponent(WorkflowsComponent);
    component = fixture.componentInstance;
    workflowServiceSpy = TestBed.inject(
      WorkflowService,
    ) as jasmine.SpyObj<WorkflowService>;

    component['destroy$'] = new Subject<void>();
  });

  afterEach(() => {
    component['destroy$'].next();
    component['destroy$'].complete();
  });

  it('should transform workflows and call updateDisplayedWorkflows()', () => {
    const updateSpy = spyOn(component, 'updateDisplayedWorkflows');
    workflowServiceSpy.fetchAllWorkflows.and.returnValue(of(mockWorkflows));

    fixture.detectChanges();

    // Validate that actions were assigned from WORKFLOW_BASE_ACTIONS
    expect(updateSpy).toHaveBeenCalled();
    expect(component.isLoading).toBeFalse();
  });

  it('should assign WORKFLOW_BASE_ACTIONS to each workflow item', () => {
    workflowServiceSpy.fetchAllWorkflows.and.returnValue(of(mockWorkflows));
    fixture.detectChanges();

    const expectedActions = WORKFLOW_BASE_ACTIONS;
    const transformed = component['filteredWorkflows'] || []; // In case used internally

    transformed.forEach((item) => {
      expect(item.actions).toEqual(expectedActions);
    });
  });

  it('should handle fetchAllWorkflows error without crashing', () => {
    workflowServiceSpy.fetchAllWorkflows.and.returnValue(
      throwError(() => new Error('API Error')),
    );
    fixture.detectChanges();

    expect(component.isLoading).toBeFalse();
  });
});
