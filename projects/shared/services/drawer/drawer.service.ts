import { ComponentRef, Injectable, Type, ViewContainerRef } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DrawerService {
 private containerRef!: ViewContainerRef;

  registerViewContainer(container: ViewContainerRef) {
    this.containerRef = container;
  }

  open<T extends object>(component: Type<T>, data?: Partial<T>): ComponentRef<T> | null {
    if (!this.containerRef) {
      return null;
    }

    const componentRef = this.containerRef.createComponent<T>(component);

    if (data) {
      Object.assign(componentRef.instance, data);
    }

    return componentRef;
  }

  clear() {
    this.containerRef?.clear();
  }
}
