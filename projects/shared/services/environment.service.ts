import { Injectable, Inject, InjectionToken } from '@angular/core';

export interface EnvironmentConfig {
  consoleApi?: string;
  consoleApiV2?: string;
  consoleEmbeddingApi?: string;
  consoleInstructionApi?: string;
  baseUrl?: string;
  apiUrl?: string;
  [key: string]: any;
}

export const ENVIRONMENT_CONFIG = new InjectionToken<EnvironmentConfig>('ENVIRONMENT_CONFIG');

@Injectable({
  providedIn: 'root'
})
export class EnvironmentService {
  constructor(@Inject(ENVIRONMENT_CONFIG) private config: EnvironmentConfig) {}

  get consoleApi(): string {
    return this.config.consoleApi || this.config.baseUrl || this.config.apiUrl || '';
  }

  get consoleApiV2(): string {
    return this.config.consoleApiV2 || this.config.baseUrl || this.config.apiUrl || '';
  }

  get consoleEmbeddingApi(): string {
    return this.config.consoleEmbeddingApi || this.config.baseUrl || this.config.apiUrl || '';
  }

  get consoleInstructionApi(): string {
    return this.config.consoleInstructionApi || this.config.baseUrl || this.config.apiUrl || '';
  }

  get baseUrl(): string {
    return this.config.baseUrl || this.config.apiUrl || '';
  }

  getConfig(): EnvironmentConfig {
    return this.config;
  }
} 