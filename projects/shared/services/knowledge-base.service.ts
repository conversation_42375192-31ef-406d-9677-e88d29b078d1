import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { EnvironmentService } from './environment.service';
import { Observable, map, catchError, of, BehaviorSubject } from 'rxjs';
import { CardData } from '../../shared/models/card.model';

@Injectable({
  providedIn: 'root'
})
export class KnowledgeBaseService {
  private headers = {
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };

  constructor(
    private http: HttpClient,
    private environmentService: EnvironmentService
  ) { }

  private get baseUrl(): string {
    return this.environmentService.consoleApi;
  }

  /**
 * Fetches all knowledge base entries from the API.
 *
 * @returns Observable emitting an array of CardData items
 * - On success: logs and returns the fetched data
 * - On error: logs the error and returns an empty array
 */

  fetchAllKnowledge(): Observable<CardData[]> {
    const url = `${this.environmentService.baseUrl}/embedding/knowledge`;
    return this.http.get<CardData[]>(url, this.headers).pipe(
      map((response: CardData[]) => {
        return response;
      }),
      catchError((error: any) => {
        console.error('Knowledge Base API error:', error);
        return of([]); // Return empty array on error
      })
    );
  }

  /**
     * Deletes all documents by collection name
     * @param collectionName The name of the collection to delete from
     * @returns Observable of the delete operation result
     */
  deleteByCollection(collectionName: string): Observable<any> {
    const url = `${this.environmentService.baseUrl}/embedding/knowledge?collectionName=${collectionName}`;
    return this.http.delete(url, this.headers);
  }


 getEmbeddingModelOptions(): Observable<{ name: string; value: string }[]> {
  const url = `${this.environmentService.baseUrl}/models?modelType=Embedding`;
 
  return this.http.get<any>(url).pipe(
    map((response: any) => {
      if (!Array.isArray(response?.models)) {
        console.error('Invalid or missing "models" array in API response');
        return [];
      }

      return response.models;
    }),
    catchError((error: any) => {
      console.error('Knowledge Base API error:', error);
      return of([]);
    })
  );
}

/**
 * Converts a key-value object into an instance of HttpParams.
 * Filters out empty string values and appends only valid entries.
 *
 * @param data - Object containing query parameters
 * @returns HttpParams instance with all valid key-value pairs
 */
makeParams(data: any): any {
  let params = new BehaviorSubject<any>(null);
  if (data) {
    Object.entries(data).forEach(([key, value]) => {
      let strValue: string = `${value}`;
      if (strValue && strValue !== ' ') {
        params.next(params.value ? { ...params.value, [key]: strValue } : { [key]: strValue });
      }
    });
  }
  return params.value;
}

 /**
 * Submits the knowledge base upload request to the API.
 * Accepts form data or JSON, appends query params from formValue, and posts to a dynamic blob endpoint.
 *
 * @param payload - The upload content (can be FormData or plain object)
 * @param formValue - Key-value pair used to build query params
 * @param blob - Dynamic URL suffix to append to the upload endpoint
 * @returns Observable of the API response
 */
submitUpload(payload: FormData | Record<string, any>, formValue: any, blob: string): Observable<any> {
  const options = {
    params: this.makeParams(formValue),
  };
  const url = `${this.environmentService.baseUrl}/embedding/knowledge`;
  return this.http.post(url, payload, options);
}


/**
 * Fetches the available upload type options for the upload type dropdown.
 *
 * @returns Observable emitting the list of upload types or an empty array on error
 */
getUploadDropdowns(): Observable<any> {
  const url = `${this.baseUrl}/ava/force/refdata?ref_key=Upload Type`;
  return this.http.get(url, this.headers).pipe(
    map((response: any) => {
      return response;
    }),
    catchError((error: any) => {
      console.error('Knowledge Base Dropdowm API error:', error);
      return of([]); // Return empty array on error
    })
  );
}


/**
 * Fetches the available upload type options for the upload type dropdown.
 *
 * @returns Observable emitting the list of upload types or an empty array on error
 */
getSchemeDropdowns(): Observable<any> {
  const url = `${this.baseUrl}/ava/force/refdata?ref_key=Scheme`;
  return this.http.get(url, this.headers).pipe(
    map((response: any) => {
      return response;
    }),
    catchError((error: any) => {
      console.error('Knowledge Base Dropdowm API error:', error);
      return of([]); // Return empty array on error
    })
  );
}


  /**
   * Fetches knowledge base files by collection ID
   * @param collectionId - The collection ID to fetch files for
   * @returns Observable emitting the knowledge base files data
   */
  getKnowledgeBaseById(collectionId: string | number): Observable<any> {
    const url = `${this.environmentService.baseUrl}/embedding/knowledge/files?collection_id=${collectionId}`;
    return this.http.get<any>(url, this.headers).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error: any) => {
        console.error('API error fetching knowledge base by ID:', error);
        return of(null);
      })
    );
  }

}
