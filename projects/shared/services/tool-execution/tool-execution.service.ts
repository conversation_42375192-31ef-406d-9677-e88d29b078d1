import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { ChatMessage } from '../../components/chat-window/chat-window.component';

export interface ToolExecutionState {
  isExecuting: boolean;
  toolId: string | null;
  chatMessages: ChatMessage[];
}

/**
 * Service for managing tool execution state across the application.
 * This service standardizes the approach for tools that have an execution mode
 * with chat interface integration.
 */
@Injectable({
  providedIn: 'root'
})
export class ToolExecutionService {
  // Initial state
  private initialState: ToolExecutionState = {
    isExecuting: false,
    toolId: null,
    chatMessages: []
  };

  // The current execution state as a behavior subject
  // Use BehaviorSubject to track the current execution state
  private executionState = new BehaviorSubject<ToolExecutionState>(this.initialState);

  constructor() { }

  /**
   * Start executing a tool and initialize the chat interface
   * @param toolId The ID of the tool to execute
   * @param initialMessages Optional initial messages to display in the chat
   */
  startExecution(toolId: string, initialMessages: ChatMessage[] = []): void {
    const defaultMessages: ChatMessage[] = initialMessages.length > 0 ? initialMessages : [
      {
        from: 'ai',
        text: 'Hi there, this is the tool testing interface.'
      }
    ];

    // Set the current execution state with provided or default messages
    this.executionState.next({
      isExecuting: true,
      toolId,
      chatMessages: defaultMessages
    });
    
    console.log(`Tool execution started for tool ID: ${toolId}`);
  }

  /**
   * Stop the current tool execution
   */
  stopExecution(): void {
    const currentToolId = this.executionState.getValue().toolId;
    this.executionState.next(this.initialState);
    console.log(`Tool execution stopped for tool ID: ${currentToolId}`);
  }

  /**
   * Add a message to the current execution chat
   * @param message The message to add
   */
  addMessage(message: ChatMessage): void {
    const currentState = this.executionState.getValue();
    if (currentState.isExecuting) {
      // Only update the state if we're currently executing
      const updatedMessages = [...currentState.chatMessages, message];
      
      this.executionState.next({
        ...currentState,
        chatMessages: updatedMessages
      });
      
      console.log(`Message added to chat for tool ID: ${currentState.toolId}`);
    }
  }

  /**
   * Process a user message and generate an AI response
   * @param userMessage The message text from the user
   */
  processUserMessage(userMessage: string): void {
    const currentState = this.executionState.getValue();
    if (!currentState.isExecuting) {
      console.warn('Attempted to process a message while not executing a tool');
      return;
    }
    
    // Add user message
    this.addMessage({
      from: 'user',
      text: userMessage
    });

    console.log(`User message processed: "${userMessage}"`);
    
    // Simulate AI response (in a real app, this would call a backend service)
    setTimeout(() => {
      this.addMessage({
        from: 'ai',
        text: `I've processed your input: "${userMessage}". This is a simulated response.`
      });
    }, 1000);
  }

  /**
   * Get the current execution state as an observable
   */
  getExecutionState(): Observable<ToolExecutionState> {
    return this.executionState.asObservable();
  }

  /**
   * Check if a specific tool is currently executing
   * @param toolId The tool ID to check
   */
  isToolExecuting(toolId: string): boolean {
    const state = this.executionState.getValue();
    return state.isExecuting && state.toolId === toolId;
  }

  /**
   * Get the current chat messages for the executing tool
   */
  getChatMessages(): ChatMessage[] {
    const messages = this.executionState.getValue().chatMessages;
    return [...messages]; // Return a copy to prevent accidental mutations
  }

  /**
   * Reset chat messages for the current execution
   */
  resetChat(): void {
    const currentState = this.executionState.getValue();
    if (currentState.isExecuting) {
      this.executionState.next({
        ...currentState,
        chatMessages: []
      });
      console.log(`Chat reset for tool ID: ${currentState.toolId}`);
    }
  }
}