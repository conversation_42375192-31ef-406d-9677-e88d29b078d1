import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable, signal } from '@angular/core';
import { map } from 'rxjs';
import { EnvironmentService } from './environment.service';
import { TokenStorageService } from '@shared/auth/services/token-storage.service';

@Injectable({
  providedIn: 'root'
})
export class ToolsService {
  private readonly RECORDS_PER_PAGE = 11;
  private headers = { 
    headers: new HttpHeaders({
      'Content-Type': 'application/json',
    })
  };

  constructor(
    private http: HttpClient,
    private environmentService: EnvironmentService,
    private tokenStorageService: TokenStorageService
  ) { }

  private get apiServiceUrl(): string {
    return this.environmentService.consoleApi;
  }

  private get apiServiceUrlV2(): string {
    return this.environmentService.consoleApiV2;
  }

  private get apiInstructionsApi(): string {
    return this.environmentService.consoleInstructionApi || this.environmentService.consoleApi;
  }
   private get baseUrl(): string {
    return this.environmentService.baseUrl;
  }

  /**
   * Send tool for approval
   * @param toolId ID of the tool to send for approval
   */
  public sendForApproval(toolId: string) {
    const url = `${this.baseUrl}/tools/userTools/review?tool-id=${toolId}`;
    return this.http.put(url, {}).pipe(
      map((response: any) => {
        return response;
      })
    );
  }

  /* GET API to fetch the list of user-defined tools with pagination. */
  public getUserToolsList(page: number = 1, records: number = this.RECORDS_PER_PAGE, type: string = 'tool') {
    const url = `${this.baseUrl}/tools/userTools?page=${page}&records=${records}&isDeleted=false&type=${type}`;

    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  /* GET API to fetch the list of built-in tools. */
  public getBuiltInToolsList() {
    const url = `${this.baseUrl}/tools/builtIn`;

    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
          return response;
      })
    );
  }

  public getUserToolDetails(id: number) {
    const url = `${this.baseUrl}/tools/userTools?userToolId=${id}`;

    return this.http.get(url, this.headers).pipe(
      map((response: any) => {
        // Handle new API response structure
        if (response && response.userToolDetail) {
          const userTool = response.userToolDetail;
          const toolConfigs = userTool.toolConfigs || {};

          // Transform new structure to match expected format for backward compatibility
          const transformedResponse = {
            tools: [{
              toolId: userTool.id,
              toolName: userTool.name,
              toolDescription: userTool.description,
              toolClassName: toolConfigs.tool_class_name || '',
              toolClassDef: toolConfigs.tool_class_def || '',
              toolImage: toolConfigs.tool_image || '',
              createdBy: userTool.createdBy,
              createTimestamp: userTool.createdAt,
              updateTimestamp: userTool.modifiedAt,
              isApproved: !userTool.isDeleted
            }]
          };

          return transformedResponse;
        }

        // Return original response if it's in the old format
        return response;
      })
    );
  }

  /* POST API to create a new tool. */
  public addNewUserTool(payload: any) {
    const url = `${this.baseUrl}/tools/userTools`;

    console.log('Sending POST request to:', url);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    return this.http.post(url, payload, this.headers).pipe(
      map((response: any) => {
        console.log('Create API response:', response);
        return response;
      })
    );
  }

  /* PUT API to update a tool. */
  public updateUserTool(payload: any) {
    const url = `${this.baseUrl}/tools/userTools`;

    console.log('Sending PUT request to:', url);
    console.log('Payload:', JSON.stringify(payload, null, 2));

    return this.http.put(url, payload, this.headers).pipe(
      map((response: any) => {
        console.log('Update API response:', response);
        return response;
      })
    );
  }

  /* DELETE API to Delete a tool. */
  public deleteTool(id: number, modifiedBy: string) {
    const url = `${this.baseUrl}/tools/userTools/${id}?modifiedBy=${modifiedBy}`;

    console.log('Sending DELETE request to:', url);
    console.log('Deleting tool ID:', id, 'by user:', modifiedBy);

    return this.http.delete(url, this.headers).pipe(
      map((response: any) => {
        console.log('Delete API response:', response);
        return response;
      })
    );
  }

   /* POST API to test the tool with parameters. */
   public testTool(payload: any) {
    const url = `${this.baseUrl}/tools/userTools/execute`;


    return this.http.post(url, payload).pipe(
      map((response: any) => {
        return response;
      })
    );
  }
}
