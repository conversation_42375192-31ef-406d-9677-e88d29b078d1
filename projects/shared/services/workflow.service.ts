import { inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { EnvironmentService } from '@shared/services/environment.service';
import { catchError, map, Observable, of } from 'rxjs';
import { CardData } from '../../shared/models/card.model';
import { webSocket, WebSocketSubject } from 'rxjs/webSocket';


@Injectable({
  providedIn: 'root',
})
export class WorkflowService {
  private environmentService = inject(EnvironmentService);
  private apiServiceUrl = this.environmentService.consoleApi;
  private baseUrl = this.environmentService.consoleApiV2;
  private baseUrlOg = this.environmentService.baseUrl;
  private pipelineAPI = this.environmentService.consoleInstructionApi;
  private http = inject(HttpClient); //  Injecting HttpClient

  public socket$!: WebSocketSubject<any>;

  /**
   * Fetches all prompt data from the API
   *
   * @returns Observable emitting an array of CardData items
   * - On success: returns the fetched data
   * - On error: logs the error and returns an empty array
   */
  fetchAllWorkflows(): Observable<CardData[]> {
    const url = `${this.apiServiceUrl}/ava/force/workflow`;
    return this.http
      .get<CardData[]>(url)
      .pipe(
        map((response: CardData[]) => {
          return response; //  Return the response data
        }),
        catchError((error: any) => {
          console.error('API error:', error); //  Log the API error
          return of([]); //  Fallback: return an empty array on error
        }),
      );
  }

  fetchAllV2Workflows(page: number, records: number, type: string): Observable<CardData[]> {
    const url = `${this.baseUrlOg}/workflows`;
    const params = new HttpParams()
      .set('type', type)
      .set('page', page.toString())
      .set('records', records.toString());
      
    
    return this.http
      .get<CardData[]>(
        url,
        {params}
      )
      .pipe(
        map((response: CardData[]) => {
          return response; //  Return the response data
        }),
        catchError((error: any) => {
          console.error('API error:', error); //  Log the API error
          return of([]); //  Fallback: return an empty array on error
        }),
      );
  }

  /**
   * Deletes a workflow by its ID.
   * @param workflowId The ID of the workflow to delete.
   * @returns Observable of the delete operation result.
   */
  deleteWorkflowById(workflowId: string): Observable<any> {
    console.log(workflowId);
    const url = `${this.apiServiceUrl}/ava/force/workflow?workflowId=${workflowId}`;
    return this.http.delete(url);
  }

  deleteWorkflow(workflowId: string, modifiedBy: string): Observable<any> {
    const url = `${this.baseUrlOg}/workflows/${workflowId}`;
    return this.http.delete(url);
  }

  /**
   * Fetches the dropdown list options based on the level number and parent level ID.
   * @param levelNum The hierarchical level number to retrieve options for.
   * @param parentLvlId The ID of the parent level to filter the options.
   * @returns Observable of an array of dropdown options with `value` and `label`.
   */

  getDropdownList(levelNum: number, parentLvlId: number) {
    const url = `${this.apiServiceUrl}/ava/force/level?levelNum=${levelNum}&parentLvlId=${parentLvlId}`;
    return this.http.get(url).pipe(
      map((res: any) => {
        const optionsArray = (res?.levels || []).map((item: any) => ({
          value: item.levelId,
          label: item.name,
        }));
        return optionsArray;
      }),
    );
  }

  getAllGenerativeModels(modelType = 'Generative') {
    const url = `${this.baseUrlOg}/models?modelType=${modelType}`;
    return this.http
      .get(url)
      .pipe(map((response: any) => response?.models || []));
  }

  private workflowSavePutUrl = `${this.baseUrlOg}/workflows`;

  saveWorkFlow(payload: any) {
    return this.http.post(this.workflowSavePutUrl, payload);
  }

  updateWorkFlow(payload: any) {
    const url = `${this.baseUrlOg}/workflows`
    return this.http.put(url, payload);
  }

  // Initialize WebSocket connection
  public workflowLogConnect(executionId: string): Observable<any> {
    const config = this.environmentService.getConfig();
    const wsUrl = config['logStreamingApiUrl'] || 'wss://aava-dev.avateam.io/ws-pipeline-log-stream';
    const fullWsUrl = `${wsUrl}?executionId=${executionId}`;
    console.log('Full WebSocket URL:', fullWsUrl);

    this.socket$ = webSocket(fullWsUrl);
    return this.socket$;
  }

  // Send a message to the WebSocket server
  public sendMessage(message: any): void {
    if (this.socket$) {
      this.socket$.next(message);
    }
  }

  // Disconnect from the WebSocket server
  public workflowLogDisconnect(): void {
    this.socket$.complete();
  }

  public executeWorkflow(
    payload: FormData | Record<string, any>,
    queryString: string,
  ) {
    const url = `${this.baseUrlOg}/workflows/execute${queryString}`;

    return this.http.post(url, payload).pipe(
      map((response: any) => {
        return response;
      }),
    );
  }

  getOneWorkflow(workflowId: string) {
    const url = `${this.apiServiceUrl}/ava/force/workflow`;
    return this.http.get(url, { params: { workflowId } }).pipe(
      map((response: any) => {
        return response?.pipeline || {};
      }),
    );
  }

  getWorkflowById(workflowId: string){
    const url = `${this.baseUrlOg}/workflows`;
    const params = new HttpParams()
      .set('workFlowId', workflowId)
    return this.http.get(url, { params: params }).pipe(
      map((response: any) => {
        return response?.workFlowDetail || {};
      }),
    );
  }

  /**
   * Send workflow for approval
   * @param workflowId - The workflow ID to send for approval
   * @returns Observable with the approval response
   */
  sendWorkflowForApproval(workflowId: string): Observable<any> {
    const url = `${this.baseUrlOg}/workflows/review`;
    const params = new HttpParams().set('workflow-id', workflowId);

    return this.http.put(url, {}, { params }).pipe(
      map((response: any) => {
        return response;
      }),
      catchError((error: any) => {
        console.error('Error sending workflow for approval:', error);
        throw error;
      })
    );
  }
}
