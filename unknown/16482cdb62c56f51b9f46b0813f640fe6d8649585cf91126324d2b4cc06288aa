{"labels": {"pageTitle": "Model Creator", "name": "Model Name", "modelDeploymentName": "modelDeploymentName", "placeholderModelDescription": "Please enter description", "description": "Description", "modelType": "Model Type", "aiEngine": "AI Engine", "model": "Model", "modelName": "Model Engine", "baseurl": "Base URL", "llmDeploymentName": "LLM Deployment Name", "apiKey": "API Key Encoded", "apiVersion": "API Version", "awsAccessKey": "AWS Access Key", "awsSecretKey": "AWS Secret Key", "awsRegion": "AWS Region", "bedrockModelId": "Bedrock Model Id", "gcpProjectId": "GCP Project Id", "gcpLocation": "GCP Location", "vertexAIEndpoint": "VertexAI Endpoint", "serviceUrl": "Service URL", "apiKeyEncoded": "API Key Encoded", "headerName": "Header Name", "organization": "Organization", "domain": "Domain", "project": "Project", "team": "Team", "configure": "Configure Parameters", "generative": "Generative", "embedding": "Embedding", "exit": "Exit", "placeholderModelName": "Enter model name", "chooseEngineAndModel": "Choose AI Engine,Model Type and Model", "preferredAiEngine": "Choose the preferred AI Engine", "chooseAiEngine": "Choose AI Engine", "choosePreferredModel": "Choose the preferred Model", "modelConfiguration": "Model Configuration", "chooseModelType": "Choose Model Type", "pleaseSelectMessage": "Please select the  AI Engine,Model Type and Model to configure", "selectModelType": "Select Model Type", "placeholderBaseUrl": "Enter base URL", "placeholderLlmDeploymentName": "Enter LLM deployment name", "placeholderApiKey": "Enter API key", "dropdownTitleApiVersion": "Select API Version", "placeholderAwsAccessKey": "Enter AWS Access key", "placeholderAwsSecretKey": "Enter AWS Secret key", "placeholderAwsRegion": "Enter AWS region", "placeholderBedRockModel": "Enter Bedrock model ID", "placeholderGcpProjectId": "Enter GCP project ID", "placeholderGcpLocation": "Enter GCP location", "placeholderVertextAIEndPoint": "Enter VertexAI endpoint", "placeholderServiceUrl": "Enter service URL", "placeholderHeaderName": "Enter Header Name", "placeholderEncodedApiKey": "Enter encoded API key", "choosePreferredAIEngine": "Choose the preferred AI Engine", "chooseModel": "<PERSON><PERSON> Model"}}