import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CreateKnowledgeBaseComponent } from './create-knowledge-base.component';

describe('CreateKnowledgeBaseComponent', () => {
  let component: CreateKnowledgeBaseComponent;
  let fixture: ComponentFixture<CreateKnowledgeBaseComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        ReactiveFormsModule,
        FormsModule,
        CreateKnowledgeBaseComponent,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CreateKnowledgeBaseComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
