import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModelsComponent } from './models.component';

describe('ModelsComponent', () => {
  let component: ModelsComponent;
  let fixture: ComponentFixture<ModelsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ModelsComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ModelsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

   it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should load filter configuration on init', () => {
    spyOn(filterService, 'getFilterConfig').and.returnValue({ filters: [] });
    component.ngOnInit();
    expect(component.modelFilterConfig).toBeDefined();
  });

  it('should fetch models successfully', () => {
    const mockModels = [{ id: '1', model: 'Model 1', modelType: 'Type 1', aiEngine: 'Engine 1' }];
    spyOn(modelService, 'getAllModelList').and.returnValue(of(mockModels));
    component.fetchModels();
    expect(component.allModels.length).toBeGreaterThan(0);
    expect(component.filteredModels.length).toBeGreaterThan(0);
  });

  it('should handle error when fetching models', () => {
    spyOn(modelService, 'getAllModelList').and.returnValue(throwError(() => new Error('Failed to load models')));
    component.fetchModels();
    expect(component.error).toBe('Failed to load models');
    expect(component.isLoading).toBeFalse();
  });

  it('should toggle filter bar visibility', () => {
    component.toggleFilterBar();
    expect(component.isFilterBarVisible).toBeTrue();
    component.toggleFilterBar();
    expect(component.isFilterBarVisible).toBeFalse();
  });

  it('should navigate to create model page on create model click', () => {
    spyOn(component['router'], 'navigate');
    component.onCreateModel();
    expect(component['router'].navigate).toHaveBeenCalledWith(['/libraries/models/create']);
  });

  it('should filter models correctly', () => {
    const mockModels = [
      { id: '1', model: 'Model 1', modelType: 'Type 1', aiEngine: 'Engine 1' },
      { id: '2', model: 'Model 2', modelType: 'Type 2', aiEngine: 'Engine 2' }
    ];
    component.allModels = component.transformModelsToCardData(mockModels);
    component.filteredModels = [...component.allModels];
    const filters = { modelType: 'Type 1' };
    spyOn(filterService, 'filterData').and.returnValue(component.allModels.filter(m => m.modelType === 'Type 1'));
    component.onFilterChange(filters);
    expect(component.filteredModels.length).toBe(1);
  });
});
