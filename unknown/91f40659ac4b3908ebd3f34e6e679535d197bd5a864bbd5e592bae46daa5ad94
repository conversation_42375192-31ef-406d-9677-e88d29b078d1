import {
  Component,
  EventEmitter,
  Input,
  input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import askAvaConstants from './constants/ask-ava.contants.json';
import { CommonModule } from '@angular/common';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import {
  AvaTextareaComponent,
  PopupComponent,
  SpinnerComponent,
  ButtonComponent,
} from '@ava/play-comp-library';

@Component({
  selector: 'app-ask-ava-wrapper',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    AvaTextareaComponent,
    PopupComponent,
    SpinnerComponent,
    ButtonComponent,
  ],
  templateUrl: './ask-ava-wrapper.component.html',
  styleUrl: './ask-ava-wrapper.component.scss',
})
export class AskAvaWrapperComponent {
  labels = askAvaConstants.labels;
  _show = false;

  @Input() set show(isShow: boolean) {
    this._show = isShow;
  }

  @Input() isLoading = false;
  @Input() showOutput = false;
  @Input() title = this.labels.askAva;

  @Output() oNClickGenerate = new EventEmitter<string>();
  @Output() oNClickUse = new EventEmitter();
  @Output() oNClickReset = new EventEmitter();
  @Output() oNClickCancel = new EventEmitter();
  @Output() oNClickClosed = new EventEmitter();

  @Input() prompt = new FormControl('');

  handleClose() {
    this.oNClickClosed.emit();
    // this.show = false;
  }

  onClickGenerate() {
    this.oNClickGenerate.emit(this.prompt.value || '');
  }

  resetPromt() {
    this.prompt.reset('');
  }

  onUse() {
    this.oNClickUse.emit();
  }

  onReset() {
    this.resetPromt();
    this.oNClickReset.emit();
  }

  onCancle() {
    this.resetPromt();
    this.oNClickCancel.emit();
  }
}
